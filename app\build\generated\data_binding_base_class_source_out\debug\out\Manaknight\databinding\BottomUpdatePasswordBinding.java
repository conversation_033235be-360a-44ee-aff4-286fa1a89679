// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class BottomUpdatePasswordBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView addHeading;

  @NonNull
  public final ImageView backButton;

  @NonNull
  public final MaterialButton btnSave;

  @NonNull
  public final EditText edTxtConfirmPassword;

  @NonNull
  public final EditText edTxtCurrentPassword;

  @NonNull
  public final EditText edTxtNewPassword;

  @NonNull
  public final LinearLayout header;

  @NonNull
  public final LinearLayout line1;

  @NonNull
  public final ScrollView mainLayout;

  private BottomUpdatePasswordBinding(@NonNull LinearLayout rootView, @NonNull TextView addHeading,
      @NonNull ImageView backButton, @NonNull MaterialButton btnSave,
      @NonNull EditText edTxtConfirmPassword, @NonNull EditText edTxtCurrentPassword,
      @NonNull EditText edTxtNewPassword, @NonNull LinearLayout header, @NonNull LinearLayout line1,
      @NonNull ScrollView mainLayout) {
    this.rootView = rootView;
    this.addHeading = addHeading;
    this.backButton = backButton;
    this.btnSave = btnSave;
    this.edTxtConfirmPassword = edTxtConfirmPassword;
    this.edTxtCurrentPassword = edTxtCurrentPassword;
    this.edTxtNewPassword = edTxtNewPassword;
    this.header = header;
    this.line1 = line1;
    this.mainLayout = mainLayout;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static BottomUpdatePasswordBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static BottomUpdatePasswordBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.bottom_update_password, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static BottomUpdatePasswordBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addHeading;
      TextView addHeading = ViewBindings.findChildViewById(rootView, id);
      if (addHeading == null) {
        break missingId;
      }

      id = R.id.backButton;
      ImageView backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.btnSave;
      MaterialButton btnSave = ViewBindings.findChildViewById(rootView, id);
      if (btnSave == null) {
        break missingId;
      }

      id = R.id.edTxtConfirmPassword;
      EditText edTxtConfirmPassword = ViewBindings.findChildViewById(rootView, id);
      if (edTxtConfirmPassword == null) {
        break missingId;
      }

      id = R.id.edTxtCurrentPassword;
      EditText edTxtCurrentPassword = ViewBindings.findChildViewById(rootView, id);
      if (edTxtCurrentPassword == null) {
        break missingId;
      }

      id = R.id.edTxtNewPassword;
      EditText edTxtNewPassword = ViewBindings.findChildViewById(rootView, id);
      if (edTxtNewPassword == null) {
        break missingId;
      }

      id = R.id.header;
      LinearLayout header = ViewBindings.findChildViewById(rootView, id);
      if (header == null) {
        break missingId;
      }

      id = R.id.line1;
      LinearLayout line1 = ViewBindings.findChildViewById(rootView, id);
      if (line1 == null) {
        break missingId;
      }

      id = R.id.mainLayout;
      ScrollView mainLayout = ViewBindings.findChildViewById(rootView, id);
      if (mainLayout == null) {
        break missingId;
      }

      return new BottomUpdatePasswordBinding((LinearLayout) rootView, addHeading, backButton,
          btnSave, edTxtConfirmPassword, edTxtCurrentPassword, edTxtNewPassword, header, line1,
          mainLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
