package com.manaknight.app.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000F\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\u001al\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\u0012\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u000b2\u0006\u0010\f\u001a\u00020\u00052\u0012\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u000b2\b\b\u0002\u0010\u000e\u001a\u00020\u000f2\b\b\u0002\u0010\u0010\u001a\u00020\u000fH\u0003\u001a(\u0010\u0011\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\u0012\u001a\u00020\u0013H\u0007\u001a\u0018\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H\u0003\u001a&\u0010\u0015\u001a\u00020\u00012\u0006\u0010\u0016\u001a\u00020\u00052\u0006\u0010\u0017\u001a\u00020\u000f2\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00010\u0019H\u0007\u001a0\u0010\u001a\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\u00052\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\u001b\u001a\u00020\u001cH\u0002\u00a8\u0006\u001d"}, d2 = {"AddLineItemDetailContent", "", "projectID", "", "customerName", "", "navController", "Landroidx/navigation/NavController;", "description", "Landroidx/compose/ui/text/input/TextFieldValue;", "onDescriptionChange", "Lkotlin/Function1;", "selectedType", "onTypeChange", "showTopBar", "", "showDetailHeader", "AddLineItemScreen", "baasViewModel", "Lcom/manaknight/app/viewmodels/BaasViewModel;", "AddLineItemTopBar", "LineItemTypeButton", "text", "isSelected", "onClick", "Lkotlin/Function0;", "handleSaveAction", "context", "Landroid/content/Context;", "app_debug"})
public final class AddLineItemScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void AddLineItemScreen(int projectID, @org.jetbrains.annotations.NotNull()
    java.lang.String customerName, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.viewmodels.BaasViewModel baasViewModel) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void AddLineItemDetailContent(int projectID, java.lang.String customerName, androidx.navigation.NavController navController, androidx.compose.ui.text.input.TextFieldValue description, kotlin.jvm.functions.Function1<? super androidx.compose.ui.text.input.TextFieldValue, kotlin.Unit> onDescriptionChange, java.lang.String selectedType, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onTypeChange, boolean showTopBar, boolean showDetailHeader) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void AddLineItemTopBar(java.lang.String customerName, androidx.navigation.NavController navController) {
    }
    
    private static final void handleSaveAction(java.lang.String selectedType, androidx.compose.ui.text.input.TextFieldValue description, int projectID, androidx.navigation.NavController navController, android.content.Context context) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void LineItemTypeButton(@org.jetbrains.annotations.NotNull()
    java.lang.String text, boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
}