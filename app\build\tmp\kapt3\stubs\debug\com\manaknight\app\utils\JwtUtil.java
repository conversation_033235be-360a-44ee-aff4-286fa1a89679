package com.manaknight.app.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J&\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u00042\u0006\u0010\b\u001a\u00020\u00042\u0006\u0010\t\u001a\u00020\nJ(\u0010\u000b\u001a\u0004\u0018\u00010\f2\u0006\u0010\r\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u00042\u0006\u0010\b\u001a\u00020\u0004J \u0010\u000e\u001a\u0004\u0018\u00010\u00042\u0006\u0010\r\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\n\u00a8\u0006\u000f"}, d2 = {"Lcom/manaknight/app/utils/JwtUtil;", "", "()V", "generateJwtToken", "", "secretKey", "Ljava/security/Key;", "issuer", "subject", "expirationMillis", "", "parseAndValidateJwtToken", "Lio/jsonwebtoken/Claims;", "jwtToken", "refreshJwtToken", "app_debug"})
public final class JwtUtil {
    @org.jetbrains.annotations.NotNull()
    public static final com.manaknight.app.utils.JwtUtil INSTANCE = null;
    
    private JwtUtil() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String generateJwtToken(@org.jetbrains.annotations.NotNull()
    java.security.Key secretKey, @org.jetbrains.annotations.NotNull()
    java.lang.String issuer, @org.jetbrains.annotations.NotNull()
    java.lang.String subject, long expirationMillis) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final io.jsonwebtoken.Claims parseAndValidateJwtToken(@org.jetbrains.annotations.NotNull()
    java.lang.String jwtToken, @org.jetbrains.annotations.NotNull()
    java.security.Key secretKey, @org.jetbrains.annotations.NotNull()
    java.lang.String issuer, @org.jetbrains.annotations.NotNull()
    java.lang.String subject) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String refreshJwtToken(@org.jetbrains.annotations.NotNull()
    java.lang.String jwtToken, @org.jetbrains.annotations.NotNull()
    java.security.Key secretKey, long expirationMillis) {
        return null;
    }
}