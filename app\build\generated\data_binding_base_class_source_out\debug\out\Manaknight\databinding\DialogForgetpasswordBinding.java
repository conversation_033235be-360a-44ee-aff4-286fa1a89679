// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogForgetpasswordBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final MaterialButton btnResetPassword;

  @NonNull
  public final MaterialButton btnSendAgain;

  @NonNull
  public final CardView dialogCardView;

  @NonNull
  public final TextView email;

  private DialogForgetpasswordBinding(@NonNull FrameLayout rootView,
      @NonNull MaterialButton btnResetPassword, @NonNull MaterialButton btnSendAgain,
      @NonNull CardView dialogCardView, @NonNull TextView email) {
    this.rootView = rootView;
    this.btnResetPassword = btnResetPassword;
    this.btnSendAgain = btnSendAgain;
    this.dialogCardView = dialogCardView;
    this.email = email;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogForgetpasswordBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogForgetpasswordBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_forgetpassword, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogForgetpasswordBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnResetPassword;
      MaterialButton btnResetPassword = ViewBindings.findChildViewById(rootView, id);
      if (btnResetPassword == null) {
        break missingId;
      }

      id = R.id.btnSendAgain;
      MaterialButton btnSendAgain = ViewBindings.findChildViewById(rootView, id);
      if (btnSendAgain == null) {
        break missingId;
      }

      id = R.id.dialogCardView;
      CardView dialogCardView = ViewBindings.findChildViewById(rootView, id);
      if (dialogCardView == null) {
        break missingId;
      }

      id = R.id.email;
      TextView email = ViewBindings.findChildViewById(rootView, id);
      if (email == null) {
        break missingId;
      }

      return new DialogForgetpasswordBinding((FrameLayout) rootView, btnResetPassword, btnSendAgain,
          dialogCardView, email);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
