package com.manaknight.app.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\u0018\u0000 \u00192\u00020\u0001:\u0001\u0019B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fJ\u0016\u0010\u0007\u001a\u00020\b2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000b\u001a\u00020\fJ\u0006\u0010\u000f\u001a\u00020\bJ\u0006\u0010\u0010\u001a\u00020\bJ\u000e\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u0014J\u0006\u0010\u0015\u001a\u00020\u0014J\u0006\u0010\u0016\u001a\u00020\u0017J\u0006\u0010\u0018\u001a\u00020\u0017R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001a"}, d2 = {"Lcom/manaknight/app/utils/BiometricAuthManager;", "", "context", "Landroid/content/Context;", "preferences", "Lcom/manaknight/app/data/local/AppPreferences;", "(Landroid/content/Context;Lcom/manaknight/app/data/local/AppPreferences;)V", "authenticateWithFaceId", "", "fragment", "Landroidx/fragment/app/Fragment;", "callback", "Lcom/manaknight/app/utils/BiometricAuthCallback;", "activity", "Landroidx/fragment/app/FragmentActivity;", "disableFaceId", "enableFaceId", "getBiometricStatusMessage", "", "availability", "Lcom/manaknight/app/utils/BiometricAvailability;", "isBiometricAvailable", "isFaceIdEnabled", "", "isFaceIdSupported", "Companion", "app_debug"})
public final class BiometricAuthManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.manaknight.app.data.local.AppPreferences preferences = null;
    public static final int BIOMETRIC_REQUEST_CODE = 100;
    @org.jetbrains.annotations.NotNull()
    public static final com.manaknight.app.utils.BiometricAuthManager.Companion Companion = null;
    
    public BiometricAuthManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.data.local.AppPreferences preferences) {
        super();
    }
    
    /**
     * Check if biometric authentication is available on the device
     */
    @org.jetbrains.annotations.NotNull()
    public final com.manaknight.app.utils.BiometricAvailability isBiometricAvailable() {
        return null;
    }
    
    /**
     * Check if Face ID is specifically supported
     */
    public final boolean isFaceIdSupported() {
        return false;
    }
    
    /**
     * Show Face ID authentication prompt
     */
    public final void authenticateWithFaceId(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.FragmentActivity activity, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.utils.BiometricAuthCallback callback) {
    }
    
    /**
     * Show Face ID authentication prompt from Fragment
     */
    public final void authenticateWithFaceId(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment fragment, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.utils.BiometricAuthCallback callback) {
    }
    
    /**
     * Enable Face ID for the user
     */
    public final void enableFaceId() {
    }
    
    /**
     * Disable Face ID for the user
     */
    public final void disableFaceId() {
    }
    
    /**
     * Check if Face ID is enabled by user
     */
    public final boolean isFaceIdEnabled() {
        return false;
    }
    
    /**
     * Get user-friendly message for biometric availability status
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getBiometricStatusMessage(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.utils.BiometricAvailability availability) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/manaknight/app/utils/BiometricAuthManager$Companion;", "", "()V", "BIOMETRIC_REQUEST_CODE", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}