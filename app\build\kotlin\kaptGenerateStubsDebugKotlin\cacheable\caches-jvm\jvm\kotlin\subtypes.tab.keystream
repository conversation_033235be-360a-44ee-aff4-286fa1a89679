!androidx.navigation.NavDirectionsandroidx.navigation.NavArgsandroid.os.Parcelable1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolderandroidx.fragment.app.Fragmentkotlin.Enum androidx.viewbinding.ViewBindingandroid.app.Application(androidx.appcompat.app.AppCompatActivity"kotlin.properties.ReadOnlyProperty6com.google.firebase.messaging.FirebaseMessagingService)com.manaknight.app.network.BaseDataSource.androidx.recyclerview.widget.DiffUtil.Callback+com.manaknight.app.utils.BaseDialogFragment5com.manaknight.app.utils.ResponsiveBaseDialogFragment-com.manaknight.app.utils.SubscriptionListenerAcom.google.android.material.bottomsheet.BottomSheetDialogFragment$androidx.fragment.app.DialogFragment6com.android.billingclient.api.PurchasesUpdatedListenerandroidx.lifecycle.ViewModel1androidx.constraintlayout.widget.ConstraintLayout                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        