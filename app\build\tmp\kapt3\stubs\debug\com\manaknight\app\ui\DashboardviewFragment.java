package com.manaknight.app.ui;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0094\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\"\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0006\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u001c\u0010#\u001a\b\u0012\u0004\u0012\u00020%0$2\f\u0010&\u001a\b\u0012\u0004\u0012\u00020%0$H\u0002J\u0017\u0010\'\u001a\u00020\u00192\b\u0010(\u001a\u0004\u0018\u00010)H\u0002\u00a2\u0006\u0002\u0010*J\u0017\u0010+\u001a\u00020\u00192\b\u0010,\u001a\u0004\u0018\u00010)H\u0002\u00a2\u0006\u0002\u0010*J\b\u0010-\u001a\u00020.H\u0002J\b\u0010/\u001a\u00020.H\u0002J\u0018\u00100\u001a\u00020.2\u0006\u00101\u001a\u00020\u001b2\u0006\u00102\u001a\u00020\u0019H\u0002J\u0010\u00103\u001a\u00020.2\u0006\u00101\u001a\u00020\u001bH\u0002J\u0018\u00104\u001a\u00020.2\u0006\u00105\u001a\u0002062\u0006\u00107\u001a\u000208H\u0016J\u001a\u00109\u001a\u00020.2\u0006\u0010:\u001a\u00020;2\b\u0010<\u001a\u0004\u0018\u00010=H\u0016J!\u0010>\u001a\u00020.2\b\u0010?\u001a\u0004\u0018\u00010@2\b\u0010,\u001a\u0004\u0018\u00010\u001bH\u0002\u00a2\u0006\u0002\u0010AJ!\u0010B\u001a\u00020.2\b\u0010?\u001a\u0004\u0018\u00010@2\b\u0010,\u001a\u0004\u0018\u00010)H\u0002\u00a2\u0006\u0002\u0010CJ\b\u0010D\u001a\u00020.H\u0002J\b\u0010E\u001a\u00020.H\u0002J\b\u0010F\u001a\u00020.H\u0002J\b\u0010G\u001a\u00020.H\u0002J\b\u0010H\u001a\u00020.H\u0002J\u0010\u0010I\u001a\u00020.2\u0006\u0010J\u001a\u00020KH\u0002J\b\u0010L\u001a\u00020.H\u0002R\u001b\u0010\u0003\u001a\u00020\u00048BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0007\u0010\b\u001a\u0004\b\u0005\u0010\u0006R\u001b\u0010\t\u001a\u00020\n8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\r\u0010\u000e\u001a\u0004\b\u000b\u0010\fR\u000e\u0010\u000f\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u0011\u001a\u00020\u00128BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0015\u0010\b\u001a\u0004\b\u0013\u0010\u0014R\u000e\u0010\u0016\u001a\u00020\u0017X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0019X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0012\u0010\u001a\u001a\u0004\u0018\u00010\u001bX\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010\u001cR\u0010\u0010\u001d\u001a\u0004\u0018\u00010\u0019X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00190\u001fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020!X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0012\u0010\"\u001a\u0004\u0018\u00010\u001bX\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010\u001c\u00a8\u0006M"}, d2 = {"Lcom/manaknight/app/ui/DashboardviewFragment;", "Landroidx/fragment/app/Fragment;", "()V", "baasViewModel", "Lcom/manaknight/app/viewmodels/BaasViewModel;", "getBaasViewModel", "()Lcom/manaknight/app/viewmodels/BaasViewModel;", "baasViewModel$delegate", "Lkotlin/Lazy;", "binding", "LManaknight/databinding/FragmentDashboardviewBinding;", "getBinding", "()LManaknight/databinding/FragmentDashboardviewBinding;", "binding$delegate", "Lcom/manaknight/app/extensions/FragmentDelegate;", "dialog", "Landroid/app/Dialog;", "pref", "Lcom/manaknight/app/data/local/AppPreferences;", "getPref", "()Lcom/manaknight/app/data/local/AppPreferences;", "pref$delegate", "projectsAdapter", "Lcom/manaknight/app/ui/adapters/DashboardProjectsAdapter;", "selectedMonth", "", "selectedProjectId", "", "Ljava/lang/Integer;", "selectedProjectName", "selectedStatuses", "", "showBottomSheet", "", "userId", "filterProjects", "", "Lcom/manaknight/app/model/remote/list;", "projects", "formatCurrency", "amount", "", "(Ljava/lang/Double;)Ljava/lang/String;", "formatPercentage", "value", "loadProjectStats", "", "loadProjects", "navigateToLineItems", "projectId", "projectName", "navigateToViewEstimate", "onCreateOptionsMenu", "menu", "Landroid/view/Menu;", "inflater", "Landroid/view/MenuInflater;", "onViewCreated", "view", "Landroid/view/View;", "savedInstanceState", "Landroid/os/Bundle;", "setValueColor", "textView", "Landroid/widget/TextView;", "(Landroid/widget/TextView;Ljava/lang/Integer;)V", "setValueColorForCurrency", "(Landroid/widget/TextView;Ljava/lang/Double;)V", "setupFilterButtons", "setupRecyclerView", "showBottomSheetDialog", "showMonthFilterBottomSheet", "showStatusFilterBottomSheet", "updateProjectStatsUI", "response", "Lcom/manaknight/app/model/remote/GetProjectStatsResponse;", "updateStatusButtonText", "app_debug"})
public final class DashboardviewFragment extends androidx.fragment.app.Fragment {
    @org.jetbrains.annotations.NotNull()
    private final com.manaknight.app.extensions.FragmentDelegate binding$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy baasViewModel$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy pref$delegate = null;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Integer userId;
    private android.app.Dialog dialog;
    private com.manaknight.app.ui.adapters.DashboardProjectsAdapter projectsAdapter;
    @org.jetbrains.annotations.NotNull()
    private java.util.Set<java.lang.String> selectedStatuses;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String selectedMonth = "All";
    private boolean showBottomSheet = false;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Integer selectedProjectId;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String selectedProjectName;
    
    public DashboardviewFragment() {
        super();
    }
    
    private final Manaknight.databinding.FragmentDashboardviewBinding getBinding() {
        return null;
    }
    
    private final com.manaknight.app.viewmodels.BaasViewModel getBaasViewModel() {
        return null;
    }
    
    private final com.manaknight.app.data.local.AppPreferences getPref() {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupRecyclerView() {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    private final void showBottomSheetDialog() {
    }
    
    private final void navigateToLineItems(int projectId, java.lang.String projectName) {
    }
    
    private final void navigateToViewEstimate(int projectId) {
    }
    
    private final void setupFilterButtons() {
    }
    
    private final void updateStatusButtonText() {
    }
    
    private final void showStatusFilterBottomSheet() {
    }
    
    private final void showMonthFilterBottomSheet() {
    }
    
    private final java.util.List<com.manaknight.app.model.remote.list> filterProjects(java.util.List<com.manaknight.app.model.remote.list> projects) {
        return null;
    }
    
    private final void loadProjectStats() {
    }
    
    private final void loadProjects() {
    }
    
    private final void updateProjectStatsUI(com.manaknight.app.model.remote.GetProjectStatsResponse response) {
    }
    
    private final java.lang.String formatCurrency(java.lang.Double amount) {
        return null;
    }
    
    private final java.lang.String formatPercentage(java.lang.Double value) {
        return null;
    }
    
    private final void setValueColor(android.widget.TextView textView, java.lang.Integer value) {
    }
    
    private final void setValueColorForCurrency(android.widget.TextView textView, java.lang.Double value) {
    }
    
    @java.lang.Override()
    public void onCreateOptionsMenu(@org.jetbrains.annotations.NotNull()
    android.view.Menu menu, @org.jetbrains.annotations.NotNull()
    android.view.MenuInflater inflater) {
    }
}