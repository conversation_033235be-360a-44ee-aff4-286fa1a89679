package com.manaknight.app.model.remote.profitPro;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B1\u0012\u0016\u0010\u0002\u001a\u0012\u0012\u0004\u0012\u00020\u00040\u0003j\b\u0012\u0004\u0012\u00020\u0004`\u0005\u0012\b\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\b\u0010\b\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\u0002\u0010\tJ\u0019\u0010\u000f\u001a\u0012\u0012\u0004\u0012\u00020\u00040\u0003j\b\u0012\u0004\u0012\u00020\u0004`\u0005H\u00c6\u0003J\u000b\u0010\u0010\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J;\u0010\u0012\u001a\u00020\u00002\u0018\b\u0002\u0010\u0002\u001a\u0012\u0012\u0004\u0012\u00020\u00040\u0003j\b\u0012\u0004\u0012\u00020\u0004`\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0007H\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0007H\u00d6\u0001R!\u0010\u0002\u001a\u0012\u0012\u0004\u0012\u00020\u00040\u0003j\b\u0012\u0004\u0012\u00020\u0004`\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0013\u0010\b\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\r\u00a8\u0006\u0019"}, d2 = {"Lcom/manaknight/app/model/remote/profitPro/DrawInfoModelRespModel;", "", "draws", "Ljava/util/ArrayList;", "Lcom/manaknight/app/model/remote/profitPro/DrawsRespModel;", "Lkotlin/collections/ArrayList;", "unaccounted_amount", "", "sale_price", "(Ljava/util/ArrayList;Ljava/lang/String;Ljava/lang/String;)V", "getDraws", "()Ljava/util/ArrayList;", "getSale_price", "()Ljava/lang/String;", "getUnaccounted_amount", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
public final class DrawInfoModelRespModel {
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<com.manaknight.app.model.remote.profitPro.DrawsRespModel> draws = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String unaccounted_amount = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String sale_price = null;
    
    public DrawInfoModelRespModel(@org.jetbrains.annotations.NotNull()
    java.util.ArrayList<com.manaknight.app.model.remote.profitPro.DrawsRespModel> draws, @org.jetbrains.annotations.Nullable()
    java.lang.String unaccounted_amount, @org.jetbrains.annotations.Nullable()
    java.lang.String sale_price) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<com.manaknight.app.model.remote.profitPro.DrawsRespModel> getDraws() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getUnaccounted_amount() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSale_price() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<com.manaknight.app.model.remote.profitPro.DrawsRespModel> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.manaknight.app.model.remote.profitPro.DrawInfoModelRespModel copy(@org.jetbrains.annotations.NotNull()
    java.util.ArrayList<com.manaknight.app.model.remote.profitPro.DrawsRespModel> draws, @org.jetbrains.annotations.Nullable()
    java.lang.String unaccounted_amount, @org.jetbrains.annotations.Nullable()
    java.lang.String sale_price) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}