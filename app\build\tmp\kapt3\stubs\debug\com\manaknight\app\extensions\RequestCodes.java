package com.manaknight.app.extensions;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0006\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/manaknight/app/extensions/RequestCodes;", "", "()V", "CAMERA_IMAGE", "", "CAMERA_PERMISSION", "FILE_PICK", "GALLERY_IMAGE", "NOTIFICATION_PERMISSION", "STORAGE_PERMISSION", "app_debug"})
public final class RequestCodes {
    public static final int GALLERY_IMAGE = 111;
    public static final int CAMERA_IMAGE = 112;
    public static final int CAMERA_PERMISSION = 1001;
    public static final int NOTIFICATION_PERMISSION = 1120;
    public static final int STORAGE_PERMISSION = 1121;
    public static final int FILE_PICK = 115;
    @org.jetbrains.annotations.NotNull()
    public static final com.manaknight.app.extensions.RequestCodes INSTANCE = null;
    
    private RequestCodes() {
        super();
    }
}