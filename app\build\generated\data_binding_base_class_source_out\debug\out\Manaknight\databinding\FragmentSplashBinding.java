// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentSplashBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnSplashRegister;

  @NonNull
  public final MaterialButton buttonSplashLogin;

  @NonNull
  public final ImageView imageViewSplash;

  private FragmentSplashBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnSplashRegister, @NonNull MaterialButton buttonSplashLogin,
      @NonNull ImageView imageViewSplash) {
    this.rootView = rootView;
    this.btnSplashRegister = btnSplashRegister;
    this.buttonSplashLogin = buttonSplashLogin;
    this.imageViewSplash = imageViewSplash;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentSplashBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentSplashBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_splash, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentSplashBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnSplashRegister;
      MaterialButton btnSplashRegister = ViewBindings.findChildViewById(rootView, id);
      if (btnSplashRegister == null) {
        break missingId;
      }

      id = R.id.buttonSplashLogin;
      MaterialButton buttonSplashLogin = ViewBindings.findChildViewById(rootView, id);
      if (buttonSplashLogin == null) {
        break missingId;
      }

      id = R.id.imageViewSplash;
      ImageView imageViewSplash = ViewBindings.findChildViewById(rootView, id);
      if (imageViewSplash == null) {
        break missingId;
      }

      return new FragmentSplashBinding((ConstraintLayout) rootView, btnSplashRegister,
          buttonSplashLogin, imageViewSplash);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
