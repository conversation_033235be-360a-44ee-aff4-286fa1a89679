package com.manaknight.app.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u001a\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\u001a8\u0010\u0000\u001a\u00020\u00012\u0011\u0010\u0002\u001a\r\u0012\u0004\u0012\u00020\u00010\u0003\u00a2\u0006\u0002\b\u00042\u0011\u0010\u0005\u001a\r\u0012\u0004\u0012\u00020\u00010\u0003\u00a2\u0006\u0002\b\u00042\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u00a8\u0006\b"}, d2 = {"LineItemMasterDetailLayout", "", "masterContent", "Lkotlin/Function0;", "Landroidx/compose/runtime/Composable;", "detailContent", "modifier", "Landroidx/compose/ui/Modifier;", "app_debug"})
public final class LineItemMasterDetailLayoutKt {
    
    /**
     * A responsive layout component that displays master-detail view on tablets
     * and single detail view on phones.
     *
     * @param masterContent The content to show in the left panel (master) on tablets
     * @param detailContent The content to show in the right panel (detail) on tablets, or full screen on phones
     * @param modifier Modifier to be applied to the layout
     */
    @androidx.compose.runtime.Composable()
    public static final void LineItemMasterDetailLayout(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> masterContent, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> detailContent, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
}