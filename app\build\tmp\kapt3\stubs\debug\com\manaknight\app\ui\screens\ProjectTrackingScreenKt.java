package com.manaknight.app.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0082\u0001\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u000e\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\b\u0015\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010 \n\u0002\b\u0004\u001a0\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a@\u0010\u0007\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\u00062\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\n2\u0006\u0010\u000b\u001a\u00020\u00062\u0012\u0010\f\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a@\u0010\r\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\u00062\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a@\u0010\u000e\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00102\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00010\n2\u0006\u0010\u0012\u001a\u00020\u00062\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001aH\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0012\u001a\u00020\u00062\u0006\u0010\u0015\u001a\u00020\u00162\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a*\u0010\u0017\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a\u001c\u0010\u0019\u001a\u00020\u00012\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a\u0016\u0010\u001a\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001aX\u0010\u001b\u001a\u00020\u00012\b\u0010\u001c\u001a\u0004\u0018\u00010\u001d2\b\b\u0002\u0010\u001e\u001a\u00020\u00062\u0012\u0010\u001f\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00010\n2\u0012\u0010 \u001a\u000e\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u00020\u00010\n2\u0012\u0010\"\u001a\u000e\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a\u0010\u0010#\u001a\u00020\u00012\u0006\u0010$\u001a\u00020\u0006H\u0007\u001a\u001e\u0010%\u001a\u00020\u00012\u0006\u0010\u001e\u001a\u00020\u00062\f\u0010&\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a\u0016\u0010\'\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001aH\u0010(\u001a\u00020\u00012\u0006\u0010)\u001a\u00020\u00062\u0012\u0010*\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\n2\u0006\u0010+\u001a\u00020\u00062\u0012\u0010,\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\n2\u0006\u0010-\u001a\u00020\u0006H\u0007\u001aJ\u0010.\u001a\u00020\u00012\b\u0010/\u001a\u0004\u0018\u0001002\u0006\u0010)\u001a\u00020\u00062\u0006\u0010+\u001a\u00020\u00062\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a>\u00101\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u0006\u00102\u001a\u0002032\u001e\u0010\u0004\u001a\u001a\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u000104H\u0007\u001a@\u00105\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\u00062\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\n2\u0006\u0010\u000b\u001a\u00020\u00062\u0012\u0010\f\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001aN\u00106\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\u00062\u0006\u00107\u001a\u00020\u00102\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u001e\u0010\u0004\u001a\u001a\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u000104H\u0007\u001aH\u00108\u001a\u00020\u00012\u0006\u00109\u001a\u00020!2\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010:\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010;\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010<\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a:\u0010=\u001a\u00020\u00012\u0006\u00109\u001a\u00020!2\f\u0010:\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010;\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010<\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a\u0016\u0010>\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a:\u0010?\u001a\u00020\u00012\u0006\u0010@\u001a\u00020A2\f\u0010B\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u001a\b\u0002\u0010C\u001a\u0014\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a$\u0010D\u001a\u00020\u00012\u0006\u0010/\u001a\u0002002\u0012\u0010E\u001a\u000e\u0012\u0004\u0012\u000200\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a \u0010F\u001a\u00020\u00012\u0006\u0010$\u001a\u00020\u00062\u0006\u0010G\u001a\u00020\u00062\u0006\u0010H\u001a\u00020\u0006H\u0007\u001ax\u0010I\u001a\u00020\u00012\u0006\u0010J\u001a\u00020\u00102\u0006\u0010K\u001a\u00020\u00062\b\u0010L\u001a\u0004\u0018\u00010\u00062\b\u0010M\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010N\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010O\u001a\u0004\u0018\u00010\u00062\u000e\b\u0002\u0010P\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u000e\b\u0002\u0010Q\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0002\u0010R\u001a\u00020\u00162\b\b\u0002\u0010S\u001a\u00020\u0016H\u0007\u001a$\u0010T\u001a\u00020\u00012\u0006\u00102\u001a\u0002032\u0012\u0010E\u001a\u000e\u0012\u0004\u0012\u000203\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a>\u0010U\u001a\u00020\u00012\u0006\u0010V\u001a\u00020W2\f\u0010X\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u001e\u0010Y\u001a\u001a\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u000104H\u0007\u001a\u00d2\u0001\u0010Z\u001a\u00020\u00012\u0006\u0010[\u001a\u00020\u00102\u0006\u0010\\\u001a\u00020\u00062\u0006\u0010]\u001a\u00020^2\u0006\u0010_\u001a\u00020`2\u0006\u0010a\u001a\u00020b2\u0018\u0010c\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u00052\u001e\u0010Y\u001a\u001a\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u0001042\u001e\u0010d\u001a\u001a\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u00020\u0001042\u0018\u0010C\u001a\u0014\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u00052\u0012\u0010;\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\n2\u0018\u0010e\u001a\u0014\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a&\u0010f\u001a\u00020\u00012\u0006\u0010g\u001a\u00020\u00062\u0006\u0010h\u001a\u00020\u00162\f\u0010&\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a$\u0010i\u001a\u00020\u00012\u0006\u0010N\u001a\u00020\u00062\b\u0010O\u001a\u0004\u0018\u00010\u00062\b\u0010M\u001a\u0004\u0018\u00010\u0006H\u0007\u001a\u001e\u0010j\u001a\u00020\u00162\f\u0010k\u001a\b\u0012\u0004\u0012\u00020!0l2\u0006\u0010m\u001a\u00020\u0010H\u0002\u001a\u0016\u0010n\u001a\u00020\u00102\f\u0010k\u001a\b\u0012\u0004\u0012\u00020!0lH\u0002\u001a\u0012\u0010o\u001a\u0004\u0018\u00010\u00062\b\u0010N\u001a\u0004\u0018\u00010\u0006\u00a8\u0006p"}, d2 = {"AddMaterialExpenseSheet", "", "onDismiss", "Lkotlin/Function0;", "onSave", "Lkotlin/Function2;", "", "AddMaterialExpenseSheetContentBody", "materialName", "onMaterialNameChange", "Lkotlin/Function1;", "unitCost", "onUnitCostChange", "AddMaterialExpenseSheetHeader", "AddPaymentMethodSheetContent", "selectedPaymentMethod", "", "onSelectedPaymentMethodChange", "checkNumber", "onCheckNumberChange", "AddPaymentMethodSheetHeader", "isSaveEnabled", "", "DrawStatusFilterSheet", "onStatusSelected", "DrawStatusFilterSheetContent", "DrawStatusFilterSheetHeader", "DrawsContent", "drawData", "Lcom/manaknight/app/model/remote/profitPro/Draws;", "selectedStatus", "showStatusFilter", "onShowPaymentMethodSheet", "Lcom/manaknight/app/model/remote/profitPro/DrawItem;", "onShowInvoiceOptionsSheet", "DrawsDropdownFilter", "label", "DrawsStatusFilterDropdown", "onClick", "EditLaborSheet", "EditLaborSheetContentBody", "employeeName", "onEmployeeNameChange", "hours", "onHoursChange", "hourlyRate", "EditLaborSheetHeader", "teamMember", "Lcom/manaknight/app/model/remote/profitPro/TeamMembers;", "EditMaterialExpenseSheet", "materialItem", "Lcom/manaknight/app/model/remote/profitPro/MaterialItem;", "Lkotlin/Function3;", "EditMaterialExpenseSheetContentBody", "EditMaterialExpenseSheetHeader", "materialId", "InvoiceOptionsSheet", "drawItem", "onViewInvoice", "onSendInvoice", "onEdit", "InvoiceOptionsSheetContent", "InvoiceOptionsSheetHeader", "LaborContent", "laborData", "Lcom/manaknight/app/model/remote/profitPro/Labor;", "onLaborEditClick", "onEditLabor", "LaborCostItem", "onEditClick", "LaborSummaryItem", "value", "percentageValue", "ListItem", "number", "amount", "badge", "description", "status", "date", "onMoreOptionsClick", "onCheckmarkClick", "isCheckboxEnabled", "isNextAvailable", "MaterialItem", "MaterialsContent", "materialsData", "Lcom/manaknight/app/model/remote/profitPro/Materials;", "onNewMaterialClick", "onEditMaterial", "ProjectTrackingScreen", "projectId", "projectName", "baasViewModel", "Lcom/manaknight/app/viewmodels/BaasViewModel;", "dialog", "Landroid/app/Dialog;", "navController", "Landroidx/navigation/NavController;", "onSaveMaterial", "onUpdateDraws", "onEditInvoice", "TabButton", "text", "selected", "TrackingStatusBadge", "canMarkDraw", "drawsList", "", "currentIndex", "findNextAvailableDrawIndex", "getDrawStatusText", "app_debug"})
public final class ProjectTrackingScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void ProjectTrackingScreen(int projectId, @org.jetbrains.annotations.NotNull()
    java.lang.String projectName, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.viewmodels.BaasViewModel baasViewModel, @org.jetbrains.annotations.NotNull()
    android.app.Dialog dialog, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, kotlin.Unit> onSaveMaterial, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.String, ? super java.lang.String, ? super java.lang.Integer, kotlin.Unit> onEditMaterial, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.Integer, ? super java.lang.String, ? super com.manaknight.app.model.remote.profitPro.DrawItem, kotlin.Unit> onUpdateDraws, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Integer, ? super java.lang.String, kotlin.Unit> onEditLabor, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onSendInvoice, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Integer, ? super java.lang.String, kotlin.Unit> onEditInvoice) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TabButton(@org.jetbrains.annotations.NotNull()
    java.lang.String text, boolean selected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DrawsContent(@org.jetbrains.annotations.Nullable()
    com.manaknight.app.model.remote.profitPro.Draws drawData, @org.jetbrains.annotations.NotNull()
    java.lang.String selectedStatus, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> showStatusFilter, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.profitPro.DrawItem, kotlin.Unit> onShowPaymentMethodSheet, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.profitPro.DrawItem, kotlin.Unit> onShowInvoiceOptionsSheet) {
    }
    
    private static final boolean canMarkDraw(java.util.List<com.manaknight.app.model.remote.profitPro.DrawItem> drawsList, int currentIndex) {
        return false;
    }
    
    private static final int findNextAvailableDrawIndex(java.util.List<com.manaknight.app.model.remote.profitPro.DrawItem> drawsList) {
        return 0;
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DrawStatusFilterSheetHeader(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DrawStatusFilterSheetContent(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onStatusSelected) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DrawStatusFilterSheet(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onStatusSelected) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DrawsStatusFilterDropdown(@org.jetbrains.annotations.NotNull()
    java.lang.String selectedStatus, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DrawsDropdownFilter(@org.jetbrains.annotations.NotNull()
    java.lang.String label) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void LaborContent(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.Labor laborData, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onLaborEditClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Integer, ? super java.lang.String, kotlin.Unit> onEditLabor) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void LaborSummaryItem(@org.jetbrains.annotations.NotNull()
    java.lang.String label, @org.jetbrains.annotations.NotNull()
    java.lang.String value, @org.jetbrains.annotations.NotNull()
    java.lang.String percentageValue) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void LaborCostItem(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.TeamMembers teamMember, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.profitPro.TeamMembers, kotlin.Unit> onEditClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ListItem(int number, @org.jetbrains.annotations.NotNull()
    java.lang.String amount, @org.jetbrains.annotations.Nullable()
    java.lang.String badge, @org.jetbrains.annotations.Nullable()
    java.lang.String description, @org.jetbrains.annotations.Nullable()
    java.lang.String status, @org.jetbrains.annotations.Nullable()
    java.lang.String date, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onMoreOptionsClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onCheckmarkClick, boolean isCheckboxEnabled, boolean isNextAvailable) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public static final java.lang.String getDrawStatusText(@org.jetbrains.annotations.Nullable()
    java.lang.String status) {
        return null;
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TrackingStatusBadge(@org.jetbrains.annotations.NotNull()
    java.lang.String status, @org.jetbrains.annotations.Nullable()
    java.lang.String date, @org.jetbrains.annotations.Nullable()
    java.lang.String description) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void MaterialsContent(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.Materials materialsData, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNewMaterialClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.String, ? super java.lang.String, ? super java.lang.Integer, kotlin.Unit> onEditMaterial) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void MaterialItem(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.MaterialItem materialItem, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.profitPro.MaterialItem, kotlin.Unit> onEditClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AddMaterialExpenseSheet(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, kotlin.Unit> onSave) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void EditMaterialExpenseSheet(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.MaterialItem materialItem, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.String, ? super java.lang.String, ? super java.lang.Integer, kotlin.Unit> onSave) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void EditLaborSheet(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void InvoiceOptionsSheet(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.DrawItem drawItem, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onViewInvoice, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onSendInvoice, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onEdit) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void InvoiceOptionsSheetHeader(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void InvoiceOptionsSheetContent(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.DrawItem drawItem, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onViewInvoice, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onSendInvoice, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onEdit) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AddPaymentMethodSheetHeader(int selectedPaymentMethod, @org.jetbrains.annotations.NotNull()
    java.lang.String checkNumber, boolean isSaveEnabled, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Integer, ? super java.lang.String, kotlin.Unit> onSave) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AddPaymentMethodSheetContent(int selectedPaymentMethod, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onSelectedPaymentMethodChange, @org.jetbrains.annotations.NotNull()
    java.lang.String checkNumber, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onCheckNumberChange) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AddMaterialExpenseSheetHeader(@org.jetbrains.annotations.NotNull()
    java.lang.String materialName, @org.jetbrains.annotations.NotNull()
    java.lang.String unitCost, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, kotlin.Unit> onSave) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AddMaterialExpenseSheetContentBody(@org.jetbrains.annotations.NotNull()
    java.lang.String materialName, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onMaterialNameChange, @org.jetbrains.annotations.NotNull()
    java.lang.String unitCost, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onUnitCostChange) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void EditMaterialExpenseSheetHeader(@org.jetbrains.annotations.NotNull()
    java.lang.String materialName, @org.jetbrains.annotations.NotNull()
    java.lang.String unitCost, int materialId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.String, ? super java.lang.String, ? super java.lang.Integer, kotlin.Unit> onSave) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void EditMaterialExpenseSheetContentBody(@org.jetbrains.annotations.NotNull()
    java.lang.String materialName, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onMaterialNameChange, @org.jetbrains.annotations.NotNull()
    java.lang.String unitCost, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onUnitCostChange) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void EditLaborSheetHeader(@org.jetbrains.annotations.Nullable()
    com.manaknight.app.model.remote.profitPro.TeamMembers teamMember, @org.jetbrains.annotations.NotNull()
    java.lang.String employeeName, @org.jetbrains.annotations.NotNull()
    java.lang.String hours, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, kotlin.Unit> onSave) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void EditLaborSheetContentBody(@org.jetbrains.annotations.NotNull()
    java.lang.String employeeName, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onEmployeeNameChange, @org.jetbrains.annotations.NotNull()
    java.lang.String hours, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onHoursChange, @org.jetbrains.annotations.NotNull()
    java.lang.String hourlyRate) {
    }
}