// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentCostviewBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final HeaderBinding headerInclude;

  @NonNull
  public final LinearLayout linearlayoutC0d0f5da;

  private FragmentCostviewBinding(@NonNull LinearLayout rootView,
      @NonNull HeaderBinding headerInclude, @NonNull LinearLayout linearlayoutC0d0f5da) {
    this.rootView = rootView;
    this.headerInclude = headerInclude;
    this.linearlayoutC0d0f5da = linearlayoutC0d0f5da;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentCostviewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentCostviewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_costview, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentCostviewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.headerInclude;
      View headerInclude = ViewBindings.findChildViewById(rootView, id);
      if (headerInclude == null) {
        break missingId;
      }
      HeaderBinding binding_headerInclude = HeaderBinding.bind(headerInclude);

      id = R.id.linearlayout_c0d0f5da;
      LinearLayout linearlayoutC0d0f5da = ViewBindings.findChildViewById(rootView, id);
      if (linearlayoutC0d0f5da == null) {
        break missingId;
      }

      return new FragmentCostviewBinding((LinearLayout) rootView, binding_headerInclude,
          linearlayoutC0d0f5da);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
