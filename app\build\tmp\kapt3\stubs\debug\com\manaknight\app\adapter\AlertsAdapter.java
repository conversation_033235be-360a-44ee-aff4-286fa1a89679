package com.manaknight.app.adapter;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\u0018\u00002\f\u0012\b\u0012\u00060\u0002R\u00020\u00000\u0001:\u0001\u0018B\u0019\u0012\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u0004\u00a2\u0006\u0002\u0010\u0007J\b\u0010\r\u001a\u00020\u000eH\u0016J\u001c\u0010\u000f\u001a\u00020\u00062\n\u0010\u0010\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u0011\u001a\u00020\u000eH\u0016J\u001c\u0010\u0012\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u000eH\u0016J\u001e\u0010\u0016\u001a\u00020\u00062\u0016\u0010\u0017\u001a\u0012\u0012\u0004\u0012\u00020\u00050\tj\b\u0012\u0004\u0012\u00020\u0005`\nR\u001e\u0010\b\u001a\u0012\u0012\u0004\u0012\u00020\u00050\tj\b\u0012\u0004\u0012\u00020\u0005`\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00060\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\f\u00a8\u0006\u0019"}, d2 = {"Lcom/manaknight/app/adapter/AlertsAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/manaknight/app/adapter/AlertsAdapter$ViewHolder;", "onClick", "Lkotlin/Function1;", "Lcom/manaknight/app/model/remote/AlertModel;", "", "(Lkotlin/jvm/functions/Function1;)V", "list", "Ljava/util/ArrayList;", "Lkotlin/collections/ArrayList;", "getOnClick", "()Lkotlin/jvm/functions/Function1;", "getItemCount", "", "onBindViewHolder", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "refresh", "newList", "ViewHolder", "app_debug"})
public final class AlertsAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.manaknight.app.adapter.AlertsAdapter.ViewHolder> {
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<com.manaknight.app.model.remote.AlertModel, kotlin.Unit> onClick = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<com.manaknight.app.model.remote.AlertModel> list = null;
    
    public AlertsAdapter(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.AlertModel, kotlin.Unit> onClick) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlin.jvm.functions.Function1<com.manaknight.app.model.remote.AlertModel, kotlin.Unit> getOnClick() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.manaknight.app.adapter.AlertsAdapter.ViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.adapter.AlertsAdapter.ViewHolder holder, int position) {
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    public final void refresh(@org.jetbrains.annotations.NotNull()
    java.util.ArrayList<com.manaknight.app.model.remote.AlertModel> newList) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/manaknight/app/adapter/AlertsAdapter$ViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "binding", "LManaknight/databinding/ItemAppAlertBinding;", "(Lcom/manaknight/app/adapter/AlertsAdapter;LManaknight/databinding/ItemAppAlertBinding;)V", "getBinding", "()LManaknight/databinding/ItemAppAlertBinding;", "app_debug"})
    public final class ViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final Manaknight.databinding.ItemAppAlertBinding binding = null;
        
        public ViewHolder(@org.jetbrains.annotations.NotNull()
        Manaknight.databinding.ItemAppAlertBinding binding) {
            super(null);
        }
        
        @org.jetbrains.annotations.NotNull()
        public final Manaknight.databinding.ItemAppAlertBinding getBinding() {
            return null;
        }
    }
}