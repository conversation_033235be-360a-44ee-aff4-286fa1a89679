// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.RadioButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemLineLinealBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final EditText edtUnits;

  @NonNull
  public final RadioButton radioButtonMaterial;

  @NonNull
  public final TextView txtLinealItemName;

  @NonNull
  public final TextView txtUnit;

  @NonNull
  public final TextView txtUnitStart;

  private ItemLineLinealBinding(@NonNull CardView rootView, @NonNull EditText edtUnits,
      @NonNull RadioButton radioButtonMaterial, @NonNull TextView txtLinealItemName,
      @NonNull TextView txtUnit, @NonNull TextView txtUnitStart) {
    this.rootView = rootView;
    this.edtUnits = edtUnits;
    this.radioButtonMaterial = radioButtonMaterial;
    this.txtLinealItemName = txtLinealItemName;
    this.txtUnit = txtUnit;
    this.txtUnitStart = txtUnitStart;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemLineLinealBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemLineLinealBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_line_lineal, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemLineLinealBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.edtUnits;
      EditText edtUnits = ViewBindings.findChildViewById(rootView, id);
      if (edtUnits == null) {
        break missingId;
      }

      id = R.id.radioButtonMaterial;
      RadioButton radioButtonMaterial = ViewBindings.findChildViewById(rootView, id);
      if (radioButtonMaterial == null) {
        break missingId;
      }

      id = R.id.txtLinealItemName;
      TextView txtLinealItemName = ViewBindings.findChildViewById(rootView, id);
      if (txtLinealItemName == null) {
        break missingId;
      }

      id = R.id.txtUnit;
      TextView txtUnit = ViewBindings.findChildViewById(rootView, id);
      if (txtUnit == null) {
        break missingId;
      }

      id = R.id.txtUnitStart;
      TextView txtUnitStart = ViewBindings.findChildViewById(rootView, id);
      if (txtUnitStart == null) {
        break missingId;
      }

      return new ItemLineLinealBinding((CardView) rootView, edtUnits, radioButtonMaterial,
          txtLinealItemName, txtUnit, txtUnitStart);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
