package com.manaknight.app.widget;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000h\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u001f\u0018\u0000 S2\u00020\u0001:\u0001SB\u001b\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0002\u0010\u0006J\u000e\u0010(\u001a\u00020\u00102\u0006\u0010)\u001a\u00020\u0013J\u001e\u0010(\u001a\u00020\u00102\u0016\u0010*\u001a\u0012\u0012\u0004\u0012\u00020\u00130+j\b\u0012\u0004\u0012\u00020\u0013`,J\u0006\u0010-\u001a\u00020\u0010J\b\u0010.\u001a\u00020\u0010H\u0002J\u000e\u0010/\u001a\u00020\u00102\u0006\u0010)\u001a\u00020\u0013J\u000e\u0010/\u001a\u00020\u00102\u0006\u00100\u001a\u00020#J\u000e\u00101\u001a\u00020\u00102\u0006\u00102\u001a\u00020#J\u0010\u00103\u001a\u00020\u00102\u0006\u00104\u001a\u000205H\u0002J\u000e\u00106\u001a\u00020\u00102\u0006\u00107\u001a\u00020#J\u000e\u00108\u001a\u00020\u00102\u0006\u00102\u001a\u00020#J\u000e\u00109\u001a\u00020\u00102\u0006\u00102\u001a\u00020#J\u0010\u0010:\u001a\u00020\u00102\b\u0010;\u001a\u0004\u0018\u00010\u0019J\u000e\u0010<\u001a\u00020\u00102\u0006\u00102\u001a\u00020#J\b\u0010=\u001a\u00020\u0010H\u0002J\u001a\u0010>\u001a\u00020\u00102\u0012\u0010?\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00100\u0012J\u001a\u0010@\u001a\u00020\u00102\u0012\u0010?\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00100\u0012J\u001a\u0010A\u001a\u00020\u00102\u0012\u0010?\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00100\u0012J\u001a\u0010B\u001a\u00020\u00102\u0012\u0010?\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00100\u0012J\u001a\u0010C\u001a\u00020\u00102\u0012\u0010?\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00100\u0012J\u001a\u0010D\u001a\u00020\u00102\u0012\u0010?\u001a\u000e\u0012\u0004\u0012\u00020\u0019\u0012\u0004\u0012\u00020\u00100\u0012J\u0014\u0010E\u001a\u00020\u00102\f\u0010?\u001a\b\u0012\u0004\u0012\u00020\u00100\u000fJ\u0014\u0010F\u001a\u00020\u00102\f\u0010?\u001a\b\u0012\u0004\u0012\u00020\u00100\u000fJ\u0014\u0010G\u001a\u00020\u00102\f\u0010?\u001a\b\u0012\u0004\u0012\u00020\u00100\u000fJ\b\u0010H\u001a\u00020\u0010H\u0002J\u000e\u0010I\u001a\u00020\u00102\u0006\u00102\u001a\u00020#J\u000e\u0010J\u001a\u00020\u00102\u0006\u0010K\u001a\u00020\rJ\u000e\u0010L\u001a\u00020\u00102\u0006\u0010K\u001a\u00020\rJ\u000e\u0010M\u001a\u00020\u00102\u0006\u0010K\u001a\u00020\rJ\u000e\u0010N\u001a\u00020\u00102\u0006\u0010K\u001a\u00020\rJ\u000e\u0010O\u001a\u00020\u00102\u0006\u0010K\u001a\u00020\rJ\u000e\u0010P\u001a\u00020\u00102\u0006\u00102\u001a\u00020#J\u000e\u0010Q\u001a\u00020\u00102\u0006\u0010\"\u001a\u00020#J\b\u0010R\u001a\u00020\u0010H\u0002R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\t\u001a\u00020\b8BX\u0082\u0004\u00a2\u0006\u0006\u001a\u0004\b\n\u0010\u000bR\u000e\u0010\f\u001a\u00020\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u000e\u001a\n\u0012\u0004\u0012\u00020\u0010\u0018\u00010\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0011\u001a\u0010\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u0010\u0018\u00010\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0014\u001a\u0010\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u0010\u0018\u00010\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0015\u001a\u0010\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u0010\u0018\u00010\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0016\u001a\u0010\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u0010\u0018\u00010\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0017\u001a\u0010\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u0010\u0018\u00010\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0018\u001a\u0010\u0012\u0004\u0012\u00020\u0019\u0012\u0004\u0012\u00020\u0010\u0018\u00010\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u001a\u001a\n\u0012\u0004\u0012\u00020\u0010\u0018\u00010\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u001b\u001a\n\u0012\u0004\u0012\u00020\u0010\u0018\u00010\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u001c\u001a\u00020\u001d8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b \u0010!\u001a\u0004\b\u001e\u0010\u001fR\u001a\u0010\"\u001a\u00020#X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b$\u0010%\"\u0004\b&\u0010\'\u00a8\u0006T"}, d2 = {"Lcom/manaknight/app/widget/SimpleChatView;", "Landroidx/constraintlayout/widget/ConstraintLayout;", "context", "Landroid/content/Context;", "attributeSet", "Landroid/util/AttributeSet;", "(Landroid/content/Context;Landroid/util/AttributeSet;)V", "_binding", "LManaknight/databinding/SimpleChatViewWidgetBinding;", "binding", "getBinding", "()LManaknight/databinding/SimpleChatViewWidgetBinding;", "isMoreLayoutVisible", "", "onCameraClickListener", "Lkotlin/Function0;", "", "onChatImageClickListener", "Lkotlin/Function1;", "Lcom/manaknight/app/model/remote/SingleChatResponse;", "onChatUserImageClickListener", "onChatUsernameClickListener", "onChatVideoClickListener", "onMessageClickListener", "onMessageSendListener", "", "onSelectImageClickListener", "onSelectVideoClickListener", "simpleChatAdapter", "Lcom/manaknight/app/ui/adapter/SimpleChatAdapter;", "getSimpleChatAdapter", "()Lcom/manaknight/app/ui/adapter/SimpleChatAdapter;", "simpleChatAdapter$delegate", "Lkotlin/Lazy;", "userId", "", "getUserId", "()I", "setUserId", "(I)V", "addMessage", "chatMessage", "chatMessageList", "Ljava/util/ArrayList;", "Lkotlin/collections/ArrayList;", "clearMessages", "hideMoreLayout", "remove", "position", "setAddButtonColor", "color", "setAttributes", "a", "Landroid/content/res/TypedArray;", "setChatInputBackground", "int", "setChatInputBackgroundColor", "setChatViewBackground", "setHint", "string", "setHintTextColor", "setListeners", "setOnChatImageClickListener", "listener", "setOnChatUserImageClickListener", "setOnChatUsernameClickListener", "setOnChatVideoClickListener", "setOnMessageClickListener", "setOnMessageSendListener", "setOnSelectCameraClickListener", "setOnSelectImageClickListener", "setOnSelectVideoClickListener", "setRecyclerView", "setSendButtonColor", "setShowAddButton", "boolean", "setShowCameraButton", "setShowImageButton", "setShowSenderLayout", "setShowVideoButton", "setTextColor", "setUserID", "showMoreLayout", "Companion", "app_debug"})
public final class SimpleChatView extends androidx.constraintlayout.widget.ConstraintLayout {
    public static final int TYPE_TEXT = 0;
    public static final int TYPE_IMAGE = 1;
    public static final int TYPE_VIDEO = 2;
    @org.jetbrains.annotations.Nullable()
    private Manaknight.databinding.SimpleChatViewWidgetBinding _binding;
    private boolean isMoreLayoutVisible = false;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy simpleChatAdapter$delegate = null;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.SingleChatResponse, kotlin.Unit> onChatImageClickListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.SingleChatResponse, kotlin.Unit> onChatVideoClickListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.SingleChatResponse, kotlin.Unit> onChatUserImageClickListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.SingleChatResponse, kotlin.Unit> onChatUsernameClickListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function0<kotlin.Unit> onSelectImageClickListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function0<kotlin.Unit> onSelectVideoClickListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function0<kotlin.Unit> onCameraClickListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onMessageSendListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.SingleChatResponse, kotlin.Unit> onMessageClickListener;
    private int userId = 0;
    @org.jetbrains.annotations.NotNull()
    public static final com.manaknight.app.widget.SimpleChatView.Companion Companion = null;
    
    @kotlin.jvm.JvmOverloads()
    public SimpleChatView(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attributeSet) {
        super(null);
    }
    
    private final Manaknight.databinding.SimpleChatViewWidgetBinding getBinding() {
        return null;
    }
    
    private final com.manaknight.app.ui.adapter.SimpleChatAdapter getSimpleChatAdapter() {
        return null;
    }
    
    public final int getUserId() {
        return 0;
    }
    
    public final void setUserId(int p0) {
    }
    
    private final void setAttributes(android.content.res.TypedArray a) {
    }
    
    public final void setShowSenderLayout(boolean p0_32355860) {
    }
    
    public final void setShowCameraButton(boolean p0_32355860) {
    }
    
    public final void setShowVideoButton(boolean p0_32355860) {
    }
    
    public final void setShowImageButton(boolean p0_32355860) {
    }
    
    public final void setSendButtonColor(int color) {
    }
    
    public final void setHint(@org.jetbrains.annotations.Nullable()
    java.lang.String string) {
    }
    
    public final void setTextColor(int color) {
    }
    
    public final void setHintTextColor(int color) {
    }
    
    public final void setChatInputBackground(int p0_52215) {
    }
    
    public final void setChatInputBackgroundColor(int color) {
    }
    
    public final void setAddButtonColor(int color) {
    }
    
    public final void setShowAddButton(boolean p0_32355860) {
    }
    
    public final void setChatViewBackground(int color) {
    }
    
    private final void showMoreLayout() {
    }
    
    public final void setUserID(int userId) {
    }
    
    private final void setListeners() {
    }
    
    private final void hideMoreLayout() {
    }
    
    private final void setRecyclerView() {
    }
    
    public final void addMessage(@org.jetbrains.annotations.NotNull()
    java.util.ArrayList<com.manaknight.app.model.remote.SingleChatResponse> chatMessageList) {
    }
    
    public final void addMessage(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.SingleChatResponse chatMessage) {
    }
    
    public final void remove(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.SingleChatResponse chatMessage) {
    }
    
    public final void remove(int position) {
    }
    
    public final void clearMessages() {
    }
    
    public final void setOnChatImageClickListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.SingleChatResponse, kotlin.Unit> listener) {
    }
    
    public final void setOnChatVideoClickListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.SingleChatResponse, kotlin.Unit> listener) {
    }
    
    public final void setOnChatUserImageClickListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.SingleChatResponse, kotlin.Unit> listener) {
    }
    
    public final void setOnChatUsernameClickListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.SingleChatResponse, kotlin.Unit> listener) {
    }
    
    public final void setOnMessageSendListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> listener) {
    }
    
    public final void setOnMessageClickListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.SingleChatResponse, kotlin.Unit> listener) {
    }
    
    public final void setOnSelectImageClickListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> listener) {
    }
    
    public final void setOnSelectVideoClickListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> listener) {
    }
    
    public final void setOnSelectCameraClickListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> listener) {
    }
    
    @kotlin.jvm.JvmOverloads()
    public SimpleChatView(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super(null);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/manaknight/app/widget/SimpleChatView$Companion;", "", "()V", "TYPE_IMAGE", "", "TYPE_TEXT", "TYPE_VIDEO", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}