package com.manaknight.app.model.remote.profitPro;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0014\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BG\u0012\b\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\b\u0010\b\u001a\u0004\u0018\u00010\u0007\u0012\u000e\u0010\t\u001a\n\u0012\u0004\u0012\u00020\u000b\u0018\u00010\n\u00a2\u0006\u0002\u0010\fJ\u0010\u0010\u0017\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u000eJ\u0010\u0010\u0018\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u000eJ\u0010\u0010\u0019\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u000eJ\u000b\u0010\u001a\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\u000b\u0010\u001b\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\u0011\u0010\u001c\u001a\n\u0012\u0004\u0012\u00020\u000b\u0018\u00010\nH\u00c6\u0003J\\\u0010\u001d\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00072\u0010\b\u0002\u0010\t\u001a\n\u0012\u0004\u0012\u00020\u000b\u0018\u00010\nH\u00c6\u0001\u00a2\u0006\u0002\u0010\u001eJ\u0013\u0010\u001f\u001a\u00020 2\b\u0010!\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\"\u001a\u00020#H\u00d6\u0001J\t\u0010$\u001a\u00020\u0007H\u00d6\u0001R\u001a\u0010\u0005\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\n\n\u0002\u0010\u000f\u001a\u0004\b\r\u0010\u000eR\u001a\u0010\u0002\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\n\n\u0002\u0010\u000f\u001a\u0004\b\u0010\u0010\u000eR\u001a\u0010\u0004\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\n\n\u0002\u0010\u000f\u001a\u0004\b\u0011\u0010\u000eR\u0018\u0010\u0006\u001a\u0004\u0018\u00010\u00078\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0018\u0010\b\u001a\u0004\u0018\u00010\u00078\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0013R\u001e\u0010\t\u001a\n\u0012\u0004\u0012\u00020\u000b\u0018\u00010\n8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016\u00a8\u0006%"}, d2 = {"Lcom/manaknight/app/model/remote/profitPro/Labor;", "", "laborBudget", "", "laborSpent", "laborBalance", "laborSpentPercentage", "", "remainingPercentage", "teamMembers", "", "Lcom/manaknight/app/model/remote/profitPro/TeamMembers;", "(Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;)V", "getLaborBalance", "()Ljava/lang/Double;", "Ljava/lang/Double;", "getLaborBudget", "getLaborSpent", "getLaborSpentPercentage", "()Ljava/lang/String;", "getRemainingPercentage", "getTeamMembers", "()Ljava/util/List;", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "(Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;)Lcom/manaknight/app/model/remote/profitPro/Labor;", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
public final class Labor {
    @com.google.gson.annotations.SerializedName(value = "labor_budget")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Double laborBudget = null;
    @com.google.gson.annotations.SerializedName(value = "labor_spent")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Double laborSpent = null;
    @com.google.gson.annotations.SerializedName(value = "labor_balance")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Double laborBalance = null;
    @com.google.gson.annotations.SerializedName(value = "labor_spent_percentage")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String laborSpentPercentage = null;
    @com.google.gson.annotations.SerializedName(value = "remaining_percentage")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String remainingPercentage = null;
    @com.google.gson.annotations.SerializedName(value = "team_members")
    @org.jetbrains.annotations.Nullable()
    private final java.util.List<com.manaknight.app.model.remote.profitPro.TeamMembers> teamMembers = null;
    
    public Labor(@org.jetbrains.annotations.Nullable()
    java.lang.Double laborBudget, @org.jetbrains.annotations.Nullable()
    java.lang.Double laborSpent, @org.jetbrains.annotations.Nullable()
    java.lang.Double laborBalance, @org.jetbrains.annotations.Nullable()
    java.lang.String laborSpentPercentage, @org.jetbrains.annotations.Nullable()
    java.lang.String remainingPercentage, @org.jetbrains.annotations.Nullable()
    java.util.List<com.manaknight.app.model.remote.profitPro.TeamMembers> teamMembers) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double getLaborBudget() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double getLaborSpent() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double getLaborBalance() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getLaborSpentPercentage() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getRemainingPercentage() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.manaknight.app.model.remote.profitPro.TeamMembers> getTeamMembers() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.manaknight.app.model.remote.profitPro.TeamMembers> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.manaknight.app.model.remote.profitPro.Labor copy(@org.jetbrains.annotations.Nullable()
    java.lang.Double laborBudget, @org.jetbrains.annotations.Nullable()
    java.lang.Double laborSpent, @org.jetbrains.annotations.Nullable()
    java.lang.Double laborBalance, @org.jetbrains.annotations.Nullable()
    java.lang.String laborSpentPercentage, @org.jetbrains.annotations.Nullable()
    java.lang.String remainingPercentage, @org.jetbrains.annotations.Nullable()
    java.util.List<com.manaknight.app.model.remote.profitPro.TeamMembers> teamMembers) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}