package com.manaknight.app.ui.fragments.home;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0088\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010 \n\u0002\b\n\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0005\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J \u0010)\u001a\u00020*2\u0006\u0010+\u001a\u00020,2\u0006\u0010-\u001a\u00020,2\u0006\u0010.\u001a\u00020/H\u0002J \u00100\u001a\u00020*2\u0006\u0010+\u001a\u00020,2\u0006\u00101\u001a\u00020,2\u0006\u0010.\u001a\u00020/H\u0002J\b\u00102\u001a\u00020*H\u0002J\b\u00103\u001a\u00020*H\u0002J\u0010\u00104\u001a\u00020*2\u0006\u00105\u001a\u00020!H\u0002J\u000e\u00106\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u000107J\b\u00108\u001a\u00020*H\u0002J\u0018\u00109\u001a\u00020*2\u0006\u0010:\u001a\u00020\u00052\u0006\u0010;\u001a\u00020!H\u0002J\u0018\u0010<\u001a\u00020*2\u0006\u0010:\u001a\u00020\u00052\u0006\u0010;\u001a\u00020!H\u0002J\b\u0010=\u001a\u00020*H\u0016J\b\u0010>\u001a\u00020*H\u0002J\b\u0010?\u001a\u00020*H\u0016J\u001a\u0010@\u001a\u00020*2\u0006\u0010A\u001a\u00020B2\b\u0010C\u001a\u0004\u0018\u00010DH\u0016J\b\u0010E\u001a\u00020*H\u0002J \u0010F\u001a\u00020*2\u0006\u0010:\u001a\u00020\u00052\u0006\u0010G\u001a\u00020,2\u0006\u0010H\u001a\u00020IH\u0002J(\u0010J\u001a\u00020*2\u0006\u0010+\u001a\u00020,2\u0006\u0010-\u001a\u00020,2\u0006\u00105\u001a\u00020!2\u0006\u0010K\u001a\u00020/H\u0002J(\u0010L\u001a\u00020*2\u0006\u0010+\u001a\u00020,2\u0006\u00101\u001a\u00020,2\u0006\u00105\u001a\u00020!2\u0006\u0010.\u001a\u00020/H\u0002J\u0006\u0010M\u001a\u00020*R\u001e\u0010\u0003\u001a\u0012\u0012\u0004\u0012\u00020\u00050\u0004j\b\u0012\u0004\u0012\u00020\u0005`\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u0007\u001a\u00020\b8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u000b\u0010\f\u001a\u0004\b\t\u0010\nR\u001b\u0010\r\u001a\u00020\u000e8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0011\u0010\u0012\u001a\u0004\b\u000f\u0010\u0010R\u001b\u0010\u0013\u001a\u00020\u00148BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0017\u0010\u0018\u001a\u0004\b\u0015\u0010\u0016R\u000e\u0010\u0019\u001a\u00020\u001aX\u0082.\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u001b\u001a\u00020\u001c8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u001f\u0010\u0012\u001a\u0004\b\u001d\u0010\u001eR\u000e\u0010 \u001a\u00020!X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\"\u001a\u0004\u0018\u00010#X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001b\u0010$\u001a\u00020%8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b(\u0010\u0012\u001a\u0004\b&\u0010\'\u00a8\u0006N"}, d2 = {"Lcom/manaknight/app/ui/fragments/home/<USER>", "Landroidx/fragment/app/Fragment;", "()V", "allDrawList", "Ljava/util/ArrayList;", "Lcom/manaknight/app/model/remote/profitPro/DrawsRespModel;", "Lkotlin/collections/ArrayList;", "args", "Lcom/manaknight/app/ui/fragments/home/<USER>", "getArgs", "()Lcom/manaknight/app/ui/fragments/home/<USER>", "args$delegate", "Landroidx/navigation/NavArgsLazy;", "baasViewModel", "Lcom/manaknight/app/viewmodels/BaasViewModel;", "getBaasViewModel", "()Lcom/manaknight/app/viewmodels/BaasViewModel;", "baasViewModel$delegate", "Lkotlin/Lazy;", "binding", "LManaknight/databinding/FragmentDrawsBinding;", "getBinding", "()LManaknight/databinding/FragmentDrawsBinding;", "binding$delegate", "Lcom/manaknight/app/extensions/FragmentDelegate;", "dialog", "Landroid/app/Dialog;", "drawAdapter", "Lcom/manaknight/app/adapter/DrawsAdapter;", "getDrawAdapter", "()Lcom/manaknight/app/adapter/DrawsAdapter;", "drawAdapter$delegate", "drawSelection", "", "infoModel", "Lcom/manaknight/app/model/remote/profitPro/DrawInfoModelRespModel;", "pref", "Lcom/manaknight/app/data/local/AppPreferences;", "getPref", "()Lcom/manaknight/app/data/local/AppPreferences;", "pref$delegate", "addNewPercentageDraw", "", "description", "", "percentage", "sheetDialog", "Lcom/google/android/material/bottomsheet/BottomSheetDialog;", "addNewPriceDraw", "amount", "checkAndShowFirstDrawPopup", "getAllDraws", "getDeleteDraw", "id", "getDrawList", "", "onAddDrawClick", "onDeleteClick", "item", "position", "onEditClick", "onResume", "onReviewClick", "onStop", "onViewCreated", "view", "Landroid/view/View;", "savedInstanceState", "Landroid/os/Bundle;", "showAddNewDraw", "showEditDraw", "drawInfo", "isEditable", "", "updatePercentageDraw", "sheet", "updatePriceDraw", "updateView", "app_debug"})
public final class DrawsFragment extends androidx.fragment.app.Fragment {
    @org.jetbrains.annotations.NotNull()
    private final com.manaknight.app.extensions.FragmentDelegate binding$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.navigation.NavArgsLazy args$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy baasViewModel$delegate = null;
    private android.app.Dialog dialog;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy pref$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy drawAdapter$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<com.manaknight.app.model.remote.profitPro.DrawsRespModel> allDrawList = null;
    @org.jetbrains.annotations.Nullable()
    private com.manaknight.app.model.remote.profitPro.DrawInfoModelRespModel infoModel;
    private int drawSelection = 0;
    
    public DrawsFragment() {
        super();
    }
    
    private final Manaknight.databinding.FragmentDrawsBinding getBinding() {
        return null;
    }
    
    private final com.manaknight.app.ui.fragments.home.DrawsFragmentArgs getArgs() {
        return null;
    }
    
    private final com.manaknight.app.viewmodels.BaasViewModel getBaasViewModel() {
        return null;
    }
    
    private final com.manaknight.app.data.local.AppPreferences getPref() {
        return null;
    }
    
    private final com.manaknight.app.adapter.DrawsAdapter getDrawAdapter() {
        return null;
    }
    
    private final void onEditClick(com.manaknight.app.model.remote.profitPro.DrawsRespModel item, int position) {
    }
    
    private final void onDeleteClick(com.manaknight.app.model.remote.profitPro.DrawsRespModel item, int position) {
    }
    
    private final void onAddDrawClick() {
    }
    
    private final void onReviewClick() {
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void getAllDraws() {
    }
    
    private final void checkAndShowFirstDrawPopup() {
    }
    
    private final void getDeleteDraw(int id) {
    }
    
    private final void addNewPriceDraw(java.lang.String description, java.lang.String amount, com.google.android.material.bottomsheet.BottomSheetDialog sheetDialog) {
    }
    
    private final void updatePercentageDraw(java.lang.String description, java.lang.String percentage, int id, com.google.android.material.bottomsheet.BottomSheetDialog sheet) {
    }
    
    private final void updatePriceDraw(java.lang.String description, java.lang.String amount, int id, com.google.android.material.bottomsheet.BottomSheetDialog sheetDialog) {
    }
    
    private final void addNewPercentageDraw(java.lang.String description, java.lang.String percentage, com.google.android.material.bottomsheet.BottomSheetDialog sheetDialog) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.manaknight.app.model.remote.profitPro.DrawsRespModel> getDrawList() {
        return null;
    }
    
    public final void updateView() {
    }
    
    private final void showAddNewDraw() {
    }
    
    private final void showEditDraw(com.manaknight.app.model.remote.profitPro.DrawsRespModel item, java.lang.String drawInfo, boolean isEditable) {
    }
    
    @java.lang.Override()
    public void onResume() {
    }
    
    @java.lang.Override()
    public void onStop() {
    }
}