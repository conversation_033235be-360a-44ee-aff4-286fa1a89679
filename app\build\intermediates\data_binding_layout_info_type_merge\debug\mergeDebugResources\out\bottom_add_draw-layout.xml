<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="bottom_add_draw" modulePackage="Manaknight" filePath="app\src\main\res\layout\bottom_add_draw.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/bottom_add_draw_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="419" endOffset="14"/></Target><Target id="@+id/header" view="LinearLayout"><Expressions/><location startLine="14" startOffset="4" endLine="79" endOffset="18"/></Target><Target id="@+id/addHeading" view="TextView"><Expressions/><location startLine="36" startOffset="16" endLine="48" endOffset="46"/></Target><Target id="@+id/backButton" view="ImageView"><Expressions/><location startLine="50" startOffset="12" endLine="57" endOffset="17"/></Target><Target id="@+id/btnSave" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="61" startOffset="12" endLine="75" endOffset="37"/></Target><Target id="@+id/mainLayout" view="ScrollView"><Expressions/><location startLine="88" startOffset="4" endLine="418" endOffset="16"/></Target><Target id="@+id/line1" view="LinearLayout"><Expressions/><location startLine="96" startOffset="4" endLine="417" endOffset="18"/></Target><Target id="@+id/descInput" view="EditText"><Expressions/><location startLine="132" startOffset="16" endLine="145" endOffset="51"/></Target><Target id="@+id/priceDrawLayout" view="LinearLayout"><Expressions/><location startLine="147" startOffset="16" endLine="247" endOffset="30"/></Target><Target id="@+id/radioButtonDollar" view="RadioButton"><Expressions/><location startLine="184" startOffset="28" endLine="190" endOffset="74"/></Target><Target id="@+id/radioButtonPercentage" view="RadioButton"><Expressions/><location startLine="223" startOffset="28" endLine="228" endOffset="74"/></Target><Target id="@+id/priceLayout" view="LinearLayout"><Expressions/><location startLine="249" startOffset="16" endLine="319" endOffset="30"/></Target><Target id="@+id/tvPrice" view="TextView"><Expressions/><location startLine="257" startOffset="20" endLine="268" endOffset="82"/></Target><Target id="@+id/edTxtPrice" view="EditText"><Expressions/><location startLine="286" startOffset="24" endLine="300" endOffset="53"/></Target><Target id="@+id/percentageLayout" view="LinearLayout"><Expressions/><location startLine="321" startOffset="16" endLine="381" endOffset="30"/></Target><Target id="@+id/edTxtPercentage" view="EditText"><Expressions/><location startLine="366" startOffset="24" endLine="379" endOffset="29"/></Target><Target id="@+id/availableAmountLayout" view="LinearLayout"><Expressions/><location startLine="384" startOffset="16" endLine="412" endOffset="30"/></Target><Target id="@+id/tvAvailableAmount" view="TextView"><Expressions/><location startLine="391" startOffset="20" endLine="399" endOffset="53"/></Target><Target id="@+id/tvAvailablePercentage" view="TextView"><Expressions/><location startLine="401" startOffset="20" endLine="410" endOffset="53"/></Target></Targets></Layout>