// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentProfileBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnLogout;

  @NonNull
  public final MaterialButton buttonEditProfile;

  @NonNull
  public final ImageView ivEdit;

  @NonNull
  public final ImageView ivUser;

  @NonNull
  public final CardView materialCardView2;

  @NonNull
  public final TextView textViewEmail;

  @NonNull
  public final TextView textViewFirstName;

  @NonNull
  public final TextView textViewLastName;

  private FragmentProfileBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnLogout, @NonNull MaterialButton buttonEditProfile,
      @NonNull ImageView ivEdit, @NonNull ImageView ivUser, @NonNull CardView materialCardView2,
      @NonNull TextView textViewEmail, @NonNull TextView textViewFirstName,
      @NonNull TextView textViewLastName) {
    this.rootView = rootView;
    this.btnLogout = btnLogout;
    this.buttonEditProfile = buttonEditProfile;
    this.ivEdit = ivEdit;
    this.ivUser = ivUser;
    this.materialCardView2 = materialCardView2;
    this.textViewEmail = textViewEmail;
    this.textViewFirstName = textViewFirstName;
    this.textViewLastName = textViewLastName;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentProfileBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentProfileBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_profile, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentProfileBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnLogout;
      MaterialButton btnLogout = ViewBindings.findChildViewById(rootView, id);
      if (btnLogout == null) {
        break missingId;
      }

      id = R.id.buttonEditProfile;
      MaterialButton buttonEditProfile = ViewBindings.findChildViewById(rootView, id);
      if (buttonEditProfile == null) {
        break missingId;
      }

      id = R.id.ivEdit;
      ImageView ivEdit = ViewBindings.findChildViewById(rootView, id);
      if (ivEdit == null) {
        break missingId;
      }

      id = R.id.ivUser;
      ImageView ivUser = ViewBindings.findChildViewById(rootView, id);
      if (ivUser == null) {
        break missingId;
      }

      id = R.id.materialCardView2;
      CardView materialCardView2 = ViewBindings.findChildViewById(rootView, id);
      if (materialCardView2 == null) {
        break missingId;
      }

      id = R.id.textViewEmail;
      TextView textViewEmail = ViewBindings.findChildViewById(rootView, id);
      if (textViewEmail == null) {
        break missingId;
      }

      id = R.id.textViewFirstName;
      TextView textViewFirstName = ViewBindings.findChildViewById(rootView, id);
      if (textViewFirstName == null) {
        break missingId;
      }

      id = R.id.textViewLastName;
      TextView textViewLastName = ViewBindings.findChildViewById(rootView, id);
      if (textViewLastName == null) {
        break missingId;
      }

      return new FragmentProfileBinding((ConstraintLayout) rootView, btnLogout, buttonEditProfile,
          ivEdit, ivUser, materialCardView2, textViewEmail, textViewFirstName, textViewLastName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
