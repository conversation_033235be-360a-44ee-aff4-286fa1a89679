// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class SimpleChatViewWidgetBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageView btnAdd;

  @NonNull
  public final ImageView btnSend;

  @NonNull
  public final EditText edtMessage;

  @NonNull
  public final ImageView imgCamera;

  @NonNull
  public final ImageView imgImage;

  @NonNull
  public final ImageView imgVideo;

  @NonNull
  public final LinearLayout layoutChatInputHolder;

  @NonNull
  public final LinearLayoutCompat moreLayout;

  @NonNull
  public final RecyclerView rvChats;

  @NonNull
  public final ConstraintLayout simpleChatView;

  private SimpleChatViewWidgetBinding(@NonNull ConstraintLayout rootView, @NonNull ImageView btnAdd,
      @NonNull ImageView btnSend, @NonNull EditText edtMessage, @NonNull ImageView imgCamera,
      @NonNull ImageView imgImage, @NonNull ImageView imgVideo,
      @NonNull LinearLayout layoutChatInputHolder, @NonNull LinearLayoutCompat moreLayout,
      @NonNull RecyclerView rvChats, @NonNull ConstraintLayout simpleChatView) {
    this.rootView = rootView;
    this.btnAdd = btnAdd;
    this.btnSend = btnSend;
    this.edtMessage = edtMessage;
    this.imgCamera = imgCamera;
    this.imgImage = imgImage;
    this.imgVideo = imgVideo;
    this.layoutChatInputHolder = layoutChatInputHolder;
    this.moreLayout = moreLayout;
    this.rvChats = rvChats;
    this.simpleChatView = simpleChatView;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static SimpleChatViewWidgetBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static SimpleChatViewWidgetBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.simple_chat_view_widget, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static SimpleChatViewWidgetBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnAdd;
      ImageView btnAdd = ViewBindings.findChildViewById(rootView, id);
      if (btnAdd == null) {
        break missingId;
      }

      id = R.id.btnSend;
      ImageView btnSend = ViewBindings.findChildViewById(rootView, id);
      if (btnSend == null) {
        break missingId;
      }

      id = R.id.edtMessage;
      EditText edtMessage = ViewBindings.findChildViewById(rootView, id);
      if (edtMessage == null) {
        break missingId;
      }

      id = R.id.imgCamera;
      ImageView imgCamera = ViewBindings.findChildViewById(rootView, id);
      if (imgCamera == null) {
        break missingId;
      }

      id = R.id.imgImage;
      ImageView imgImage = ViewBindings.findChildViewById(rootView, id);
      if (imgImage == null) {
        break missingId;
      }

      id = R.id.imgVideo;
      ImageView imgVideo = ViewBindings.findChildViewById(rootView, id);
      if (imgVideo == null) {
        break missingId;
      }

      id = R.id.layoutChatInputHolder;
      LinearLayout layoutChatInputHolder = ViewBindings.findChildViewById(rootView, id);
      if (layoutChatInputHolder == null) {
        break missingId;
      }

      id = R.id.moreLayout;
      LinearLayoutCompat moreLayout = ViewBindings.findChildViewById(rootView, id);
      if (moreLayout == null) {
        break missingId;
      }

      id = R.id.rvChats;
      RecyclerView rvChats = ViewBindings.findChildViewById(rootView, id);
      if (rvChats == null) {
        break missingId;
      }

      ConstraintLayout simpleChatView = (ConstraintLayout) rootView;

      return new SimpleChatViewWidgetBinding((ConstraintLayout) rootView, btnAdd, btnSend,
          edtMessage, imgCamera, imgImage, imgVideo, layoutChatInputHolder, moreLayout, rvChats,
          simpleChatView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
