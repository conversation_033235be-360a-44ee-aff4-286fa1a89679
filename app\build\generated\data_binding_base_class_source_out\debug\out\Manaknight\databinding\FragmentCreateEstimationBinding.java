// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentCreateEstimationBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnContinue;

  @NonNull
  public final MaterialButton btnEditCustomer;

  @NonNull
  public final MaterialButton btnSearch;

  @NonNull
  public final ConstraintLayout constraint;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final RelativeLayout container;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   */
  @Nullable
  public final MaterialButton createCustomer;

  @NonNull
  public final LinearLayout dataLayout;

  @NonNull
  public final HeaderBinding headerInclude;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final ConstraintLayout innerConstraintLayout;

  @NonNull
  public final LinearLayout line1;

  @NonNull
  public final ScrollView scrollable;

  @NonNull
  public final TextView txtAddress;

  @NonNull
  public final TextView txtEmail;

  @NonNull
  public final TextView txtPhoneNumber;

  private FragmentCreateEstimationBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnContinue, @NonNull MaterialButton btnEditCustomer,
      @NonNull MaterialButton btnSearch, @NonNull ConstraintLayout constraint,
      @Nullable RelativeLayout container, @Nullable MaterialButton createCustomer,
      @NonNull LinearLayout dataLayout, @NonNull HeaderBinding headerInclude,
      @Nullable ConstraintLayout innerConstraintLayout, @NonNull LinearLayout line1,
      @NonNull ScrollView scrollable, @NonNull TextView txtAddress, @NonNull TextView txtEmail,
      @NonNull TextView txtPhoneNumber) {
    this.rootView = rootView;
    this.btnContinue = btnContinue;
    this.btnEditCustomer = btnEditCustomer;
    this.btnSearch = btnSearch;
    this.constraint = constraint;
    this.container = container;
    this.createCustomer = createCustomer;
    this.dataLayout = dataLayout;
    this.headerInclude = headerInclude;
    this.innerConstraintLayout = innerConstraintLayout;
    this.line1 = line1;
    this.scrollable = scrollable;
    this.txtAddress = txtAddress;
    this.txtEmail = txtEmail;
    this.txtPhoneNumber = txtPhoneNumber;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentCreateEstimationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentCreateEstimationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_create_estimation, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentCreateEstimationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnContinue;
      MaterialButton btnContinue = ViewBindings.findChildViewById(rootView, id);
      if (btnContinue == null) {
        break missingId;
      }

      id = R.id.btnEditCustomer;
      MaterialButton btnEditCustomer = ViewBindings.findChildViewById(rootView, id);
      if (btnEditCustomer == null) {
        break missingId;
      }

      id = R.id.btnSearch;
      MaterialButton btnSearch = ViewBindings.findChildViewById(rootView, id);
      if (btnSearch == null) {
        break missingId;
      }

      ConstraintLayout constraint = (ConstraintLayout) rootView;

      id = R.id.container;
      RelativeLayout container = ViewBindings.findChildViewById(rootView, id);

      id = R.id.createCustomer;
      MaterialButton createCustomer = ViewBindings.findChildViewById(rootView, id);

      id = R.id.dataLayout;
      LinearLayout dataLayout = ViewBindings.findChildViewById(rootView, id);
      if (dataLayout == null) {
        break missingId;
      }

      id = R.id.headerInclude;
      View headerInclude = ViewBindings.findChildViewById(rootView, id);
      if (headerInclude == null) {
        break missingId;
      }
      HeaderBinding binding_headerInclude = HeaderBinding.bind(headerInclude);

      id = R.id.innerConstraintLayout;
      ConstraintLayout innerConstraintLayout = ViewBindings.findChildViewById(rootView, id);

      id = R.id.line1;
      LinearLayout line1 = ViewBindings.findChildViewById(rootView, id);
      if (line1 == null) {
        break missingId;
      }

      id = R.id.scrollable;
      ScrollView scrollable = ViewBindings.findChildViewById(rootView, id);
      if (scrollable == null) {
        break missingId;
      }

      id = R.id.txtAddress;
      TextView txtAddress = ViewBindings.findChildViewById(rootView, id);
      if (txtAddress == null) {
        break missingId;
      }

      id = R.id.txtEmail;
      TextView txtEmail = ViewBindings.findChildViewById(rootView, id);
      if (txtEmail == null) {
        break missingId;
      }

      id = R.id.txtPhoneNumber;
      TextView txtPhoneNumber = ViewBindings.findChildViewById(rootView, id);
      if (txtPhoneNumber == null) {
        break missingId;
      }

      return new FragmentCreateEstimationBinding((ConstraintLayout) rootView, btnContinue,
          btnEditCustomer, btnSearch, constraint, container, createCustomer, dataLayout,
          binding_headerInclude, innerConstraintLayout, line1, scrollable, txtAddress, txtEmail,
          txtPhoneNumber);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
