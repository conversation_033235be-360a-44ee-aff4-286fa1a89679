package com.manaknight.app.utils;

/**
 * Callback interface for biometric authentication
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H&J\b\u0010\b\u001a\u00020\u0003H&J\b\u0010\t\u001a\u00020\u0003H&\u00a8\u0006\n"}, d2 = {"Lcom/manaknight/app/utils/BiometricAuthCallback;", "", "onAuthenticationError", "", "errorCode", "", "errorMessage", "", "onAuthenticationFailed", "onAuthenticationSucceeded", "app_debug"})
public abstract interface BiometricAuthCallback {
    
    public abstract void onAuthenticationSucceeded();
    
    public abstract void onAuthenticationError(int errorCode, @org.jetbrains.annotations.NotNull()
    java.lang.String errorMessage);
    
    public abstract void onAuthenticationFailed();
}