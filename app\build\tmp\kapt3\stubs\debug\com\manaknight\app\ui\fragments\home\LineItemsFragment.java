package com.manaknight.app.ui.fragments.home;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000p\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010&\u001a\u00020\'H\u0002J\u0010\u0010(\u001a\u00020\'2\u0006\u0010)\u001a\u00020*H\u0002J\b\u0010+\u001a\u00020\'H\u0002J\b\u0010,\u001a\u00020\'H\u0002J\u0006\u0010-\u001a\u00020\'J\b\u0010.\u001a\u00020\'H\u0016J\b\u0010/\u001a\u00020\'H\u0016J\u001a\u00100\u001a\u00020\'2\u0006\u00101\u001a\u0002022\b\u00103\u001a\u0004\u0018\u000104H\u0016J\b\u00105\u001a\u00020\'H\u0002J\b\u00106\u001a\u00020\'H\u0002J\b\u00107\u001a\u00020\'H\u0002J\u0006\u00108\u001a\u00020\'J\b\u00109\u001a\u00020\'H\u0002J\u0006\u0010:\u001a\u00020\'R\u001e\u0010\u0003\u001a\u0012\u0012\u0004\u0012\u00020\u00050\u0004j\b\u0012\u0004\u0012\u00020\u0005`\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u0007\u001a\u00020\b8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u000b\u0010\f\u001a\u0004\b\t\u0010\nR\u001b\u0010\r\u001a\u00020\u000e8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0011\u0010\u0012\u001a\u0004\b\u000f\u0010\u0010R\u001b\u0010\u0013\u001a\u00020\u00148BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0017\u0010\u0018\u001a\u0004\b\u0015\u0010\u0016R\u0010\u0010\u0019\u001a\u0004\u0018\u00010\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u001cX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u001eX\u0082.\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u001f\u001a\u00020 8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b#\u0010\u0012\u001a\u0004\b!\u0010\"R\u0010\u0010$\u001a\u0004\u0018\u00010%X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006;"}, d2 = {"Lcom/manaknight/app/ui/fragments/home/<USER>", "Landroidx/fragment/app/Fragment;", "()V", "allLineItem", "Ljava/util/ArrayList;", "Lcom/manaknight/app/model/remote/profitPro/JobDetailsRespModel;", "Lkotlin/collections/ArrayList;", "args", "Lcom/manaknight/app/ui/fragments/home/<USER>", "getArgs", "()Lcom/manaknight/app/ui/fragments/home/<USER>", "args$delegate", "Landroidx/navigation/NavArgsLazy;", "baasViewModel", "Lcom/manaknight/app/viewmodels/BaasViewModel;", "getBaasViewModel", "()Lcom/manaknight/app/viewmodels/BaasViewModel;", "baasViewModel$delegate", "Lkotlin/Lazy;", "binding", "LManaknight/databinding/FragmentLineItemsBinding;", "getBinding", "()LManaknight/databinding/FragmentLineItemsBinding;", "binding$delegate", "Lcom/manaknight/app/extensions/FragmentDelegate;", "clientDetails", "Lcom/manaknight/app/model/remote/profitPro/ClientDetailRespModel;", "dialog", "Landroid/app/Dialog;", "dynamicLineItemManager", "Lcom/manaknight/app/utils/DynamicLineItemManager;", "pref", "Lcom/manaknight/app/data/local/AppPreferences;", "getPref", "()Lcom/manaknight/app/data/local/AppPreferences;", "pref$delegate", "totals", "Lcom/manaknight/app/model/remote/profitPro/TotalRespModel;", "deleteProject", "", "delteItem", "id", "", "getAllLineItem", "initializeDraws", "moveToDrawScreen", "onResume", "onStop", "onViewCreated", "view", "Landroid/view/View;", "savedInstanceState", "Landroid/os/Bundle;", "showBackNavigationDialog", "showCancelProjectButton", "showCancelProjectDialog", "updateAllData", "updateProjectStatusToOutstanding", "updateTotal", "app_debug"})
public final class LineItemsFragment extends androidx.fragment.app.Fragment {
    @org.jetbrains.annotations.NotNull()
    private final com.manaknight.app.extensions.FragmentDelegate binding$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.navigation.NavArgsLazy args$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy baasViewModel$delegate = null;
    private android.app.Dialog dialog;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy pref$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<com.manaknight.app.model.remote.profitPro.JobDetailsRespModel> allLineItem = null;
    @org.jetbrains.annotations.Nullable()
    private com.manaknight.app.model.remote.profitPro.TotalRespModel totals;
    @org.jetbrains.annotations.Nullable()
    private com.manaknight.app.model.remote.profitPro.ClientDetailRespModel clientDetails;
    private com.manaknight.app.utils.DynamicLineItemManager dynamicLineItemManager;
    
    public LineItemsFragment() {
        super();
    }
    
    private final Manaknight.databinding.FragmentLineItemsBinding getBinding() {
        return null;
    }
    
    private final com.manaknight.app.ui.fragments.home.LineItemsFragmentArgs getArgs() {
        return null;
    }
    
    private final com.manaknight.app.viewmodels.BaasViewModel getBaasViewModel() {
        return null;
    }
    
    private final com.manaknight.app.data.local.AppPreferences getPref() {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void getAllLineItem() {
    }
    
    private final void initializeDraws() {
    }
    
    private final void updateProjectStatusToOutstanding() {
    }
    
    private final void delteItem(int id) {
    }
    
    public final void moveToDrawScreen() {
    }
    
    public final void updateAllData() {
    }
    
    public final void updateTotal() {
    }
    
    private final void showCancelProjectButton() {
    }
    
    private final void showCancelProjectDialog() {
    }
    
    private final void deleteProject() {
    }
    
    @java.lang.Override()
    public void onResume() {
    }
    
    @java.lang.Override()
    public void onStop() {
    }
    
    private final void showBackNavigationDialog() {
    }
}