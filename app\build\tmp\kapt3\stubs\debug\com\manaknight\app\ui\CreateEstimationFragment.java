package com.manaknight.app.ui;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010*\u001a\u00020+H\u0002J\"\u0010,\u001a\u00020+2\b\u0010-\u001a\u0004\u0018\u00010.2\u0006\u0010/\u001a\u0002002\u0006\u00101\u001a\u000202H\u0002J\b\u00103\u001a\u00020+H\u0016J\u0018\u00104\u001a\u00020+2\u0006\u00105\u001a\u00020\u00162\u0006\u00106\u001a\u00020$H\u0002J\b\u00107\u001a\u00020+H\u0016J\u001a\u00108\u001a\u00020+2\u0006\u00109\u001a\u00020:2\b\u0010;\u001a\u0004\u0018\u00010<H\u0016J\b\u0010=\u001a\u00020+H\u0002J\u0006\u0010>\u001a\u00020+R\u001b\u0010\u0003\u001a\u00020\u00048BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0007\u0010\b\u001a\u0004\b\u0005\u0010\u0006R\u001b\u0010\t\u001a\u00020\n8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\r\u0010\u000e\u001a\u0004\b\u000b\u0010\fR\u001b\u0010\u000f\u001a\u00020\u00108BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0013\u0010\b\u001a\u0004\b\u0011\u0010\u0012R\u001e\u0010\u0014\u001a\u0012\u0012\u0004\u0012\u00020\u00160\u0015j\b\u0012\u0004\u0012\u00020\u0016`\u0017X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0019X\u0082.\u00a2\u0006\u0002\n\u0000R\u001e\u0010\u001a\u001a\u0012\u0012\u0004\u0012\u00020\u00160\u0015j\b\u0012\u0004\u0012\u00020\u0016`\u0017X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u001b\u001a\u00020\u001c8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u001f\u0010\b\u001a\u0004\b\u001d\u0010\u001eR\u0010\u0010 \u001a\u0004\u0018\u00010\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010!\u001a\u00020\"X\u0082.\u00a2\u0006\u0002\n\u0000R\u001e\u0010#\u001a\u0004\u0018\u00010$X\u0086\u000e\u00a2\u0006\u0010\n\u0002\u0010)\u001a\u0004\b%\u0010&\"\u0004\b\'\u0010(\u00a8\u0006?"}, d2 = {"Lcom/manaknight/app/ui/CreateEstimationFragment;", "Landroidx/fragment/app/Fragment;", "()V", "baasViewModel", "Lcom/manaknight/app/viewmodels/BaasViewModel;", "getBaasViewModel", "()Lcom/manaknight/app/viewmodels/BaasViewModel;", "baasViewModel$delegate", "Lkotlin/Lazy;", "binding", "LManaknight/databinding/FragmentCreateEstimationBinding;", "getBinding", "()LManaknight/databinding/FragmentCreateEstimationBinding;", "binding$delegate", "Lcom/manaknight/app/extensions/FragmentDelegate;", "customerAdapter", "Lcom/manaknight/app/adapter/CustomerAdapter;", "getCustomerAdapter", "()Lcom/manaknight/app/adapter/CustomerAdapter;", "customerAdapter$delegate", "customerList", "Ljava/util/ArrayList;", "Lcom/manaknight/app/model/remote/profitPro/CustomerRespListModel;", "Lkotlin/collections/ArrayList;", "dialog", "Landroid/app/Dialog;", "filteredList", "pref", "Lcom/manaknight/app/data/local/AppPreferences;", "getPref", "()Lcom/manaknight/app/data/local/AppPreferences;", "pref$delegate", "selectedCustomer", "sheet", "Lcom/google/android/material/bottomsheet/BottomSheetDialog;", "userId", "", "getUserId", "()Ljava/lang/Integer;", "setUserId", "(Ljava/lang/Integer;)V", "Ljava/lang/Integer;", "createNewEstimation", "", "getCustomerList", "searchText", "", "employeeRecylerView", "Landroidx/recyclerview/widget/RecyclerView;", "noCustomer", "Landroid/widget/RelativeLayout;", "onResume", "onSelectCustomer", "item", "position", "onStop", "onViewCreated", "view", "Landroid/view/View;", "savedInstanceState", "Landroid/os/Bundle;", "showCustomerSelection", "updateData", "app_debug"})
public final class CreateEstimationFragment extends androidx.fragment.app.Fragment {
    @org.jetbrains.annotations.NotNull()
    private final com.manaknight.app.extensions.FragmentDelegate binding$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy baasViewModel$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy pref$delegate = null;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Integer userId;
    private android.app.Dialog dialog;
    @org.jetbrains.annotations.Nullable()
    private com.manaknight.app.model.remote.profitPro.CustomerRespListModel selectedCustomer;
    private com.google.android.material.bottomsheet.BottomSheetDialog sheet;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy customerAdapter$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<com.manaknight.app.model.remote.profitPro.CustomerRespListModel> customerList = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<com.manaknight.app.model.remote.profitPro.CustomerRespListModel> filteredList = null;
    
    public CreateEstimationFragment() {
        super();
    }
    
    private final Manaknight.databinding.FragmentCreateEstimationBinding getBinding() {
        return null;
    }
    
    private final com.manaknight.app.viewmodels.BaasViewModel getBaasViewModel() {
        return null;
    }
    
    private final com.manaknight.app.data.local.AppPreferences getPref() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getUserId() {
        return null;
    }
    
    public final void setUserId(@org.jetbrains.annotations.Nullable()
    java.lang.Integer p0) {
    }
    
    private final com.manaknight.app.adapter.CustomerAdapter getCustomerAdapter() {
        return null;
    }
    
    private final void onSelectCustomer(com.manaknight.app.model.remote.profitPro.CustomerRespListModel item, int position) {
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    public final void updateData() {
    }
    
    @java.lang.Override()
    public void onResume() {
    }
    
    @java.lang.Override()
    public void onStop() {
    }
    
    private final void showCustomerSelection() {
    }
    
    private final void getCustomerList(java.lang.String searchText, androidx.recyclerview.widget.RecyclerView employeeRecylerView, android.widget.RelativeLayout noCustomer) {
    }
    
    private final void createNewEstimation() {
    }
}