// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.textview.MaterialTextView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class BottomUpdateProfileBinding implements ViewBinding {
  @NonNull
  private final View rootView;

  @NonNull
  public final MaterialTextView btnCamera;

  @NonNull
  public final MaterialTextView btnGallery;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final ImageView closeButton;

  @NonNull
  public final MaterialTextView imageView6;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   */
  @Nullable
  public final TextView textView29;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   */
  @Nullable
  public final View view;

  private BottomUpdateProfileBinding(@NonNull View rootView, @NonNull MaterialTextView btnCamera,
      @NonNull MaterialTextView btnGallery, @Nullable ImageView closeButton,
      @NonNull MaterialTextView imageView6, @Nullable TextView textView29, @Nullable View view) {
    this.rootView = rootView;
    this.btnCamera = btnCamera;
    this.btnGallery = btnGallery;
    this.closeButton = closeButton;
    this.imageView6 = imageView6;
    this.textView29 = textView29;
    this.view = view;
  }

  @Override
  @NonNull
  public View getRoot() {
    return rootView;
  }

  @NonNull
  public static BottomUpdateProfileBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static BottomUpdateProfileBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.bottom_update_profile, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static BottomUpdateProfileBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnCamera;
      MaterialTextView btnCamera = ViewBindings.findChildViewById(rootView, id);
      if (btnCamera == null) {
        break missingId;
      }

      id = R.id.btnGallery;
      MaterialTextView btnGallery = ViewBindings.findChildViewById(rootView, id);
      if (btnGallery == null) {
        break missingId;
      }

      id = R.id.closeButton;
      ImageView closeButton = ViewBindings.findChildViewById(rootView, id);

      id = R.id.imageView6;
      MaterialTextView imageView6 = ViewBindings.findChildViewById(rootView, id);
      if (imageView6 == null) {
        break missingId;
      }

      id = R.id.textView29;
      TextView textView29 = ViewBindings.findChildViewById(rootView, id);

      id = R.id.view;
      View view = ViewBindings.findChildViewById(rootView, id);

      return new BottomUpdateProfileBinding(rootView, btnCamera, btnGallery, closeButton,
          imageView6, textView29, view);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
