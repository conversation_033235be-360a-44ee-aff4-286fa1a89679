// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemLineBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageView btnDelete;

  @NonNull
  public final ImageView btnEdit;

  @NonNull
  public final TextView description;

  @NonNull
  public final TextView txtLaboutBudget;

  @NonNull
  public final TextView txtLaboutBudget1;

  @NonNull
  public final TextView txtMaterialBudget;

  @NonNull
  public final TextView txtMaterialBudget1;

  @NonNull
  public final TextView txtProfitOverhead;

  @NonNull
  public final TextView txtProfitOverhead1;

  @NonNull
  public final TextView txtSalePrice;

  @NonNull
  public final TextView txtSalePrice1;

  @NonNull
  public final TextView txtTypeLabel;

  private ItemLineBinding(@NonNull ConstraintLayout rootView, @NonNull ImageView btnDelete,
      @NonNull ImageView btnEdit, @NonNull TextView description, @NonNull TextView txtLaboutBudget,
      @NonNull TextView txtLaboutBudget1, @NonNull TextView txtMaterialBudget,
      @NonNull TextView txtMaterialBudget1, @NonNull TextView txtProfitOverhead,
      @NonNull TextView txtProfitOverhead1, @NonNull TextView txtSalePrice,
      @NonNull TextView txtSalePrice1, @NonNull TextView txtTypeLabel) {
    this.rootView = rootView;
    this.btnDelete = btnDelete;
    this.btnEdit = btnEdit;
    this.description = description;
    this.txtLaboutBudget = txtLaboutBudget;
    this.txtLaboutBudget1 = txtLaboutBudget1;
    this.txtMaterialBudget = txtMaterialBudget;
    this.txtMaterialBudget1 = txtMaterialBudget1;
    this.txtProfitOverhead = txtProfitOverhead;
    this.txtProfitOverhead1 = txtProfitOverhead1;
    this.txtSalePrice = txtSalePrice;
    this.txtSalePrice1 = txtSalePrice1;
    this.txtTypeLabel = txtTypeLabel;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemLineBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemLineBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_line, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemLineBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnDelete;
      ImageView btnDelete = ViewBindings.findChildViewById(rootView, id);
      if (btnDelete == null) {
        break missingId;
      }

      id = R.id.btnEdit;
      ImageView btnEdit = ViewBindings.findChildViewById(rootView, id);
      if (btnEdit == null) {
        break missingId;
      }

      id = R.id.description;
      TextView description = ViewBindings.findChildViewById(rootView, id);
      if (description == null) {
        break missingId;
      }

      id = R.id.txtLaboutBudget;
      TextView txtLaboutBudget = ViewBindings.findChildViewById(rootView, id);
      if (txtLaboutBudget == null) {
        break missingId;
      }

      id = R.id.txtLaboutBudget_1;
      TextView txtLaboutBudget1 = ViewBindings.findChildViewById(rootView, id);
      if (txtLaboutBudget1 == null) {
        break missingId;
      }

      id = R.id.txtMaterialBudget;
      TextView txtMaterialBudget = ViewBindings.findChildViewById(rootView, id);
      if (txtMaterialBudget == null) {
        break missingId;
      }

      id = R.id.txtMaterialBudget_1;
      TextView txtMaterialBudget1 = ViewBindings.findChildViewById(rootView, id);
      if (txtMaterialBudget1 == null) {
        break missingId;
      }

      id = R.id.txtProfitOverhead;
      TextView txtProfitOverhead = ViewBindings.findChildViewById(rootView, id);
      if (txtProfitOverhead == null) {
        break missingId;
      }

      id = R.id.txtProfitOverhead_1;
      TextView txtProfitOverhead1 = ViewBindings.findChildViewById(rootView, id);
      if (txtProfitOverhead1 == null) {
        break missingId;
      }

      id = R.id.txtSalePrice;
      TextView txtSalePrice = ViewBindings.findChildViewById(rootView, id);
      if (txtSalePrice == null) {
        break missingId;
      }

      id = R.id.txtSalePrice_1;
      TextView txtSalePrice1 = ViewBindings.findChildViewById(rootView, id);
      if (txtSalePrice1 == null) {
        break missingId;
      }

      id = R.id.txtTypeLabel;
      TextView txtTypeLabel = ViewBindings.findChildViewById(rootView, id);
      if (txtTypeLabel == null) {
        break missingId;
      }

      return new ItemLineBinding((ConstraintLayout) rootView, btnDelete, btnEdit, description,
          txtLaboutBudget, txtLaboutBudget1, txtMaterialBudget, txtMaterialBudget1,
          txtProfitOverhead, txtProfitOverhead1, txtSalePrice, txtSalePrice1, txtTypeLabel);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
