// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentSquresetupBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnAddSquare;

  @NonNull
  public final MaterialButton btnContinue;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final RelativeLayout container;

  @NonNull
  public final HeaderBinding headerInclude;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final ConstraintLayout innerConstraintLayout;

  @NonNull
  public final RecyclerView squareRecylerView;

  private FragmentSquresetupBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnAddSquare, @NonNull MaterialButton btnContinue,
      @Nullable RelativeLayout container, @NonNull HeaderBinding headerInclude,
      @Nullable ConstraintLayout innerConstraintLayout, @NonNull RecyclerView squareRecylerView) {
    this.rootView = rootView;
    this.btnAddSquare = btnAddSquare;
    this.btnContinue = btnContinue;
    this.container = container;
    this.headerInclude = headerInclude;
    this.innerConstraintLayout = innerConstraintLayout;
    this.squareRecylerView = squareRecylerView;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentSquresetupBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentSquresetupBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_squresetup, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentSquresetupBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnAddSquare;
      MaterialButton btnAddSquare = ViewBindings.findChildViewById(rootView, id);
      if (btnAddSquare == null) {
        break missingId;
      }

      id = R.id.btnContinue;
      MaterialButton btnContinue = ViewBindings.findChildViewById(rootView, id);
      if (btnContinue == null) {
        break missingId;
      }

      id = R.id.container;
      RelativeLayout container = ViewBindings.findChildViewById(rootView, id);

      id = R.id.headerInclude;
      View headerInclude = ViewBindings.findChildViewById(rootView, id);
      if (headerInclude == null) {
        break missingId;
      }
      HeaderBinding binding_headerInclude = HeaderBinding.bind(headerInclude);

      id = R.id.innerConstraintLayout;
      ConstraintLayout innerConstraintLayout = ViewBindings.findChildViewById(rootView, id);

      id = R.id.squareRecylerView;
      RecyclerView squareRecylerView = ViewBindings.findChildViewById(rootView, id);
      if (squareRecylerView == null) {
        break missingId;
      }

      return new FragmentSquresetupBinding((ConstraintLayout) rootView, btnAddSquare, btnContinue,
          container, binding_headerInclude, innerConstraintLayout, squareRecylerView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
