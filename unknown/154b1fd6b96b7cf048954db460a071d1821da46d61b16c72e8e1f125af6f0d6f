package com.manaknight.app.ui;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000n\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0004\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\"\u001a\u00020#H\u0002J\u0018\u0010$\u001a\u00020#2\u0006\u0010%\u001a\u00020\u00182\u0006\u0010&\u001a\u00020\'H\u0002J\u0018\u0010(\u001a\u00020#2\u0006\u0010%\u001a\u00020\u00182\u0006\u0010&\u001a\u00020\'H\u0002J\b\u0010)\u001a\u00020#H\u0016J\b\u0010*\u001a\u00020#H\u0016J\b\u0010+\u001a\u00020#H\u0016J\u001a\u0010,\u001a\u00020#2\u0006\u0010-\u001a\u00020.2\b\u0010/\u001a\u0004\u0018\u000100H\u0016J\u0018\u00101\u001a\u00020#2\u000e\u00102\u001a\n\u0012\u0004\u0012\u00020\u0018\u0018\u000103H\u0002J\b\u00104\u001a\u00020#H\u0002J\u0018\u00105\u001a\u00020#2\u0006\u0010%\u001a\u00020\u00182\u0006\u00106\u001a\u00020\'H\u0002R\u001b\u0010\u0003\u001a\u00020\u00048BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0007\u0010\b\u001a\u0004\b\u0005\u0010\u0006R\u001b\u0010\t\u001a\u00020\n8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\r\u0010\u000e\u001a\u0004\b\u000b\u0010\fR\u000e\u0010\u000f\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u0011\u001a\u00020\u00128BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0015\u0010\b\u001a\u0004\b\u0013\u0010\u0014R\u001e\u0010\u0016\u001a\u0012\u0012\u0004\u0012\u00020\u00180\u0017j\b\u0012\u0004\u0012\u00020\u0018`\u0019X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u001bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u001bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u001d\u001a\u00020\u001e8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b!\u0010\b\u001a\u0004\b\u001f\u0010 \u00a8\u00067"}, d2 = {"Lcom/manaknight/app/ui/CompanySetupFragment;", "Landroidx/fragment/app/Fragment;", "()V", "baasViewModel", "Lcom/manaknight/app/viewmodels/BaasViewModel;", "getBaasViewModel", "()Lcom/manaknight/app/viewmodels/BaasViewModel;", "baasViewModel$delegate", "Lkotlin/Lazy;", "binding", "LManaknight/databinding/FragmentCompanysetupBinding;", "getBinding", "()LManaknight/databinding/FragmentCompanysetupBinding;", "binding$delegate", "Lcom/manaknight/app/extensions/FragmentDelegate;", "dialog", "Landroid/app/Dialog;", "employeeAdapter", "Lcom/manaknight/app/adapter/EmployeeAdapter;", "getEmployeeAdapter", "()Lcom/manaknight/app/adapter/EmployeeAdapter;", "employeeAdapter$delegate", "employeeList", "Ljava/util/ArrayList;", "Lcom/manaknight/app/model/remote/EmplyeeModel;", "Lkotlin/collections/ArrayList;", "isShowFirst", "", "isShowSecond", "pref", "Lcom/manaknight/app/data/local/AppPreferences;", "getPref", "()Lcom/manaknight/app/data/local/AppPreferences;", "pref$delegate", "companySetup", "", "onEmployeeDeleteClick", "item", "position", "", "onEmployeeEditClick", "onPause", "onResume", "onStop", "onViewCreated", "view", "Landroid/view/View;", "savedInstanceState", "Landroid/os/Bundle;", "setEmployeeList", "message", "", "showAddEmployee", "showEditEmployee", "index", "app_debug"})
public final class CompanySetupFragment extends androidx.fragment.app.Fragment {
    @org.jetbrains.annotations.NotNull()
    private final com.manaknight.app.extensions.FragmentDelegate binding$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy baasViewModel$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy pref$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy employeeAdapter$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<com.manaknight.app.model.remote.EmplyeeModel> employeeList = null;
    private boolean isShowFirst = true;
    private boolean isShowSecond = true;
    private android.app.Dialog dialog;
    
    public CompanySetupFragment() {
        super();
    }
    
    private final Manaknight.databinding.FragmentCompanysetupBinding getBinding() {
        return null;
    }
    
    private final com.manaknight.app.viewmodels.BaasViewModel getBaasViewModel() {
        return null;
    }
    
    private final com.manaknight.app.data.local.AppPreferences getPref() {
        return null;
    }
    
    private final com.manaknight.app.adapter.EmployeeAdapter getEmployeeAdapter() {
        return null;
    }
    
    private final void onEmployeeEditClick(com.manaknight.app.model.remote.EmplyeeModel item, int position) {
    }
    
    private final void onEmployeeDeleteClick(com.manaknight.app.model.remote.EmplyeeModel item, int position) {
    }
    
    @java.lang.Override()
    public void onPause() {
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setEmployeeList(java.util.List<com.manaknight.app.model.remote.EmplyeeModel> message) {
    }
    
    private final void showAddEmployee() {
    }
    
    private final void showEditEmployee(com.manaknight.app.model.remote.EmplyeeModel item, int index) {
    }
    
    @java.lang.Override()
    public void onResume() {
    }
    
    @java.lang.Override()
    public void onStop() {
    }
    
    private final void companySetup() {
    }
}