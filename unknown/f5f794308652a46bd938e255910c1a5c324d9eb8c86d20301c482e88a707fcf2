// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentLineItemsBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnAddLineItem;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final RelativeLayout container;

  @NonNull
  public final ConstraintLayout coss;

  @NonNull
  public final LinearLayout dynamicLineItemsContainer;

  @NonNull
  public final HeaderBinding headerInclude;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final ConstraintLayout innerConstraintLayout;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final LinearLayout line1;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final ItemLineBinding linearLineItem;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final ItemLineBinding materialLineItem;

  @NonNull
  public final RelativeLayout noCustomer;

  @NonNull
  public final TextView noMatch;

  @NonNull
  public final ScrollView scrollableContent;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final ItemLineBinding squareLineItem;

  @NonNull
  public final LinearLayout topLayout;

  @NonNull
  public final ItemLineTotalBinding totalLineItem;

  private FragmentLineItemsBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnAddLineItem, @Nullable RelativeLayout container,
      @NonNull ConstraintLayout coss, @NonNull LinearLayout dynamicLineItemsContainer,
      @NonNull HeaderBinding headerInclude, @Nullable ConstraintLayout innerConstraintLayout,
      @Nullable LinearLayout line1, @Nullable ItemLineBinding linearLineItem,
      @Nullable ItemLineBinding materialLineItem, @NonNull RelativeLayout noCustomer,
      @NonNull TextView noMatch, @NonNull ScrollView scrollableContent,
      @Nullable ItemLineBinding squareLineItem, @NonNull LinearLayout topLayout,
      @NonNull ItemLineTotalBinding totalLineItem) {
    this.rootView = rootView;
    this.btnAddLineItem = btnAddLineItem;
    this.container = container;
    this.coss = coss;
    this.dynamicLineItemsContainer = dynamicLineItemsContainer;
    this.headerInclude = headerInclude;
    this.innerConstraintLayout = innerConstraintLayout;
    this.line1 = line1;
    this.linearLineItem = linearLineItem;
    this.materialLineItem = materialLineItem;
    this.noCustomer = noCustomer;
    this.noMatch = noMatch;
    this.scrollableContent = scrollableContent;
    this.squareLineItem = squareLineItem;
    this.topLayout = topLayout;
    this.totalLineItem = totalLineItem;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentLineItemsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentLineItemsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_line_items, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentLineItemsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnAddLineItem;
      MaterialButton btnAddLineItem = ViewBindings.findChildViewById(rootView, id);
      if (btnAddLineItem == null) {
        break missingId;
      }

      id = R.id.container;
      RelativeLayout container = ViewBindings.findChildViewById(rootView, id);

      ConstraintLayout coss = (ConstraintLayout) rootView;

      id = R.id.dynamicLineItemsContainer;
      LinearLayout dynamicLineItemsContainer = ViewBindings.findChildViewById(rootView, id);
      if (dynamicLineItemsContainer == null) {
        break missingId;
      }

      id = R.id.headerInclude;
      View headerInclude = ViewBindings.findChildViewById(rootView, id);
      if (headerInclude == null) {
        break missingId;
      }
      HeaderBinding binding_headerInclude = HeaderBinding.bind(headerInclude);

      id = R.id.innerConstraintLayout;
      ConstraintLayout innerConstraintLayout = ViewBindings.findChildViewById(rootView, id);

      id = R.id.line1;
      LinearLayout line1 = ViewBindings.findChildViewById(rootView, id);

      id = R.id.linearLineItem;
      View linearLineItem = ViewBindings.findChildViewById(rootView, id);
      ItemLineBinding binding_linearLineItem = linearLineItem != null
          ? ItemLineBinding.bind(linearLineItem)
          : null;

      id = R.id.materialLineItem;
      View materialLineItem = ViewBindings.findChildViewById(rootView, id);
      ItemLineBinding binding_materialLineItem = materialLineItem != null
          ? ItemLineBinding.bind(materialLineItem)
          : null;

      id = R.id.noCustomer;
      RelativeLayout noCustomer = ViewBindings.findChildViewById(rootView, id);
      if (noCustomer == null) {
        break missingId;
      }

      id = R.id.noMatch;
      TextView noMatch = ViewBindings.findChildViewById(rootView, id);
      if (noMatch == null) {
        break missingId;
      }

      id = R.id.scrollableContent;
      ScrollView scrollableContent = ViewBindings.findChildViewById(rootView, id);
      if (scrollableContent == null) {
        break missingId;
      }

      id = R.id.squareLineItem;
      View squareLineItem = ViewBindings.findChildViewById(rootView, id);
      ItemLineBinding binding_squareLineItem = squareLineItem != null
          ? ItemLineBinding.bind(squareLineItem)
          : null;

      id = R.id.topLayout;
      LinearLayout topLayout = ViewBindings.findChildViewById(rootView, id);
      if (topLayout == null) {
        break missingId;
      }

      id = R.id.totalLineItem;
      View totalLineItem = ViewBindings.findChildViewById(rootView, id);
      if (totalLineItem == null) {
        break missingId;
      }
      ItemLineTotalBinding binding_totalLineItem = ItemLineTotalBinding.bind(totalLineItem);

      return new FragmentLineItemsBinding((ConstraintLayout) rootView, btnAddLineItem, container,
          coss, dynamicLineItemsContainer, binding_headerInclude, innerConstraintLayout, line1,
          binding_linearLineItem, binding_materialLineItem, noCustomer, noMatch, scrollableContent,
          binding_squareLineItem, topLayout, binding_totalLineItem);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
