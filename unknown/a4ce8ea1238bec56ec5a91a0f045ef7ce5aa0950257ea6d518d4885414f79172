package com.manaknight.app.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001c\u0010\u0003\u001a\u00020\u0004*\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\b\u0010\b\u001a\u0004\u0018\u00010\tJ\u0012\u0010\n\u001a\u00020\u0004*\u00020\u00052\u0006\u0010\u000b\u001a\u00020\f\u00a8\u0006\r"}, d2 = {"Lcom/manaknight/app/utils/FileUtils;", "", "()V", "getPathFromUri", "", "Landroidx/fragment/app/Fragment;", "context", "Landroid/content/Context;", "uri", "Landroid/net/Uri;", "saveImage", "myBitmap", "Landroid/graphics/Bitmap;", "app_debug"})
public final class FileUtils {
    @org.jetbrains.annotations.NotNull()
    public static final com.manaknight.app.utils.FileUtils INSTANCE = null;
    
    private FileUtils() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String saveImage(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$saveImage, @org.jetbrains.annotations.NotNull()
    android.graphics.Bitmap myBitmap) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getPathFromUri(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$getPathFromUri, @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.net.Uri uri) {
        return null;
    }
}