// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentLoginBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView btnForgetPassword;

  @NonNull
  public final MaterialButton btnLogin;

  @NonNull
  public final TextView btnPolicy;

  @NonNull
  public final TextView btnSignup;

  @NonNull
  public final TextView btnTerms;

  @NonNull
  public final ConstraintLayout constraint;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final RelativeLayout container;

  @NonNull
  public final EditText edTxtPassword;

  @NonNull
  public final EditText edTxtUserName;

  @NonNull
  public final HeaderBinding headerInclude;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final ConstraintLayout innerConstraintLayout;

  @NonNull
  public final TextView tvEmail;

  @NonNull
  public final TextView tvPassword;

  private FragmentLoginBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextView btnForgetPassword, @NonNull MaterialButton btnLogin,
      @NonNull TextView btnPolicy, @NonNull TextView btnSignup, @NonNull TextView btnTerms,
      @NonNull ConstraintLayout constraint, @Nullable RelativeLayout container,
      @NonNull EditText edTxtPassword, @NonNull EditText edTxtUserName,
      @NonNull HeaderBinding headerInclude, @Nullable ConstraintLayout innerConstraintLayout,
      @NonNull TextView tvEmail, @NonNull TextView tvPassword) {
    this.rootView = rootView;
    this.btnForgetPassword = btnForgetPassword;
    this.btnLogin = btnLogin;
    this.btnPolicy = btnPolicy;
    this.btnSignup = btnSignup;
    this.btnTerms = btnTerms;
    this.constraint = constraint;
    this.container = container;
    this.edTxtPassword = edTxtPassword;
    this.edTxtUserName = edTxtUserName;
    this.headerInclude = headerInclude;
    this.innerConstraintLayout = innerConstraintLayout;
    this.tvEmail = tvEmail;
    this.tvPassword = tvPassword;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentLoginBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentLoginBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_login, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentLoginBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnForgetPassword;
      TextView btnForgetPassword = ViewBindings.findChildViewById(rootView, id);
      if (btnForgetPassword == null) {
        break missingId;
      }

      id = R.id.btnLogin;
      MaterialButton btnLogin = ViewBindings.findChildViewById(rootView, id);
      if (btnLogin == null) {
        break missingId;
      }

      id = R.id.btnPolicy;
      TextView btnPolicy = ViewBindings.findChildViewById(rootView, id);
      if (btnPolicy == null) {
        break missingId;
      }

      id = R.id.btnSignup;
      TextView btnSignup = ViewBindings.findChildViewById(rootView, id);
      if (btnSignup == null) {
        break missingId;
      }

      id = R.id.btnTerms;
      TextView btnTerms = ViewBindings.findChildViewById(rootView, id);
      if (btnTerms == null) {
        break missingId;
      }

      ConstraintLayout constraint = (ConstraintLayout) rootView;

      id = R.id.container;
      RelativeLayout container = ViewBindings.findChildViewById(rootView, id);

      id = R.id.edTxtPassword;
      EditText edTxtPassword = ViewBindings.findChildViewById(rootView, id);
      if (edTxtPassword == null) {
        break missingId;
      }

      id = R.id.edTxtUserName;
      EditText edTxtUserName = ViewBindings.findChildViewById(rootView, id);
      if (edTxtUserName == null) {
        break missingId;
      }

      id = R.id.headerInclude;
      View headerInclude = ViewBindings.findChildViewById(rootView, id);
      if (headerInclude == null) {
        break missingId;
      }
      HeaderBinding binding_headerInclude = HeaderBinding.bind(headerInclude);

      id = R.id.innerConstraintLayout;
      ConstraintLayout innerConstraintLayout = ViewBindings.findChildViewById(rootView, id);

      id = R.id.tvEmail;
      TextView tvEmail = ViewBindings.findChildViewById(rootView, id);
      if (tvEmail == null) {
        break missingId;
      }

      id = R.id.tvPassword;
      TextView tvPassword = ViewBindings.findChildViewById(rootView, id);
      if (tvPassword == null) {
        break missingId;
      }

      return new FragmentLoginBinding((ConstraintLayout) rootView, btnForgetPassword, btnLogin,
          btnPolicy, btnSignup, btnTerms, constraint, container, edTxtPassword, edTxtUserName,
          binding_headerInclude, innerConstraintLayout, tvEmail, tvPassword);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
