package com.manaknight.app.ui.fragments.alerts;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000d\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u001e\u001a\u00020\u001fH\u0002J\u0010\u0010 \u001a\u00020\u001f2\u0006\u0010!\u001a\u00020\u000bH\u0002J\b\u0010\"\u001a\u00020\u001fH\u0016J\u001a\u0010#\u001a\u00020\u001f2\u0006\u0010$\u001a\u00020%2\b\u0010&\u001a\u0004\u0018\u00010\'H\u0016J\u0018\u0010(\u001a\u00020\u001f2\u000e\u0010)\u001a\n\u0012\u0004\u0012\u00020\u000b\u0018\u00010*H\u0002J\u0012\u0010+\u001a\u00020\u001f2\b\b\u0002\u0010,\u001a\u00020-H\u0002J\u0018\u0010.\u001a\u00020\u001f2\u0006\u0010)\u001a\u00020/2\u0006\u0010,\u001a\u00020-H\u0002R\u001b\u0010\u0003\u001a\u00020\u00048BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0007\u0010\b\u001a\u0004\b\u0005\u0010\u0006R\u001e\u0010\t\u001a\u0012\u0012\u0004\u0012\u00020\u000b0\nj\b\u0012\u0004\u0012\u00020\u000b`\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001b\u0010\r\u001a\u00020\u000e8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0011\u0010\u0012\u001a\u0004\b\u000f\u0010\u0010R\u001b\u0010\u0013\u001a\u00020\u00148BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0017\u0010\u0012\u001a\u0004\b\u0015\u0010\u0016R\u001e\u0010\u0018\u001a\u0012\u0012\u0004\u0012\u00020\u000b0\nj\b\u0012\u0004\u0012\u00020\u000b`\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u0019\u001a\u00020\u001a8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u001d\u0010\u0012\u001a\u0004\b\u001b\u0010\u001c\u00a8\u00060"}, d2 = {"Lcom/manaknight/app/ui/fragments/alerts/AlertsFragment;", "Landroidx/fragment/app/Fragment;", "()V", "binding", "LManaknight/databinding/FragmentAlertsBinding;", "getBinding", "()LManaknight/databinding/FragmentAlertsBinding;", "binding$delegate", "Lcom/manaknight/app/extensions/FragmentDelegate;", "newAlerts", "Ljava/util/ArrayList;", "Lcom/manaknight/app/model/remote/AlertModel;", "Lkotlin/collections/ArrayList;", "newAlertsAdapter", "Lcom/manaknight/app/adapter/AlertsAdapter;", "getNewAlertsAdapter", "()Lcom/manaknight/app/adapter/AlertsAdapter;", "newAlertsAdapter$delegate", "Lkotlin/Lazy;", "pref", "Lcom/manaknight/app/data/local/AppPreferences;", "getPref", "()Lcom/manaknight/app/data/local/AppPreferences;", "pref$delegate", "readAlerts", "viewModel", "Lcom/manaknight/app/viewmodels/BaasViewModel;", "getViewModel", "()Lcom/manaknight/app/viewmodels/BaasViewModel;", "viewModel$delegate", "getAlerts", "", "onAlertClick", "item", "onResume", "onViewCreated", "view", "Landroid/view/View;", "savedInstanceState", "Landroid/os/Bundle;", "setAlertLists", "message", "", "setAlertSeen", "id", "", "showDialog", "", "app_debug"})
public final class AlertsFragment extends androidx.fragment.app.Fragment {
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy viewModel$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final com.manaknight.app.extensions.FragmentDelegate binding$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<com.manaknight.app.model.remote.AlertModel> newAlerts = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<com.manaknight.app.model.remote.AlertModel> readAlerts = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy newAlertsAdapter$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy pref$delegate = null;
    
    public AlertsFragment() {
        super();
    }
    
    private final com.manaknight.app.viewmodels.BaasViewModel getViewModel() {
        return null;
    }
    
    private final Manaknight.databinding.FragmentAlertsBinding getBinding() {
        return null;
    }
    
    private final com.manaknight.app.adapter.AlertsAdapter getNewAlertsAdapter() {
        return null;
    }
    
    private final com.manaknight.app.data.local.AppPreferences getPref() {
        return null;
    }
    
    private final void onAlertClick(com.manaknight.app.model.remote.AlertModel item) {
    }
    
    private final void showDialog(java.lang.String message, int id) {
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    public void onResume() {
    }
    
    private final void getAlerts() {
    }
    
    private final void setAlertSeen(int id) {
    }
    
    private final void setAlertLists(java.util.List<com.manaknight.app.model.remote.AlertModel> message) {
    }
}