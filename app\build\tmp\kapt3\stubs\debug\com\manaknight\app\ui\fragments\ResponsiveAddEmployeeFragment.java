package com.manaknight.app.ui.fragments;

/**
 * Responsive version of AddEmployeeFragment that shows as bottom sheet on phones
 * and dialog on tablets
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0003J\u001a\u0010\u0004\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u00062\b\u0010\u0007\u001a\u0004\u0018\u00010\bH\u0016J\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fH\u0016J\b\u0010\r\u001a\u00020\nH\u0016\u00a8\u0006\u000e"}, d2 = {"Lcom/manaknight/app/ui/fragments/ResponsiveAddEmployeeFragment;", "Lcom/manaknight/app/utils/ResponsiveBaseDialogFragment;", "LManaknight/databinding/BottomAddEmployeeBinding;", "()V", "getViewBinding", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "onDismiss", "", "dialog", "Landroid/content/DialogInterface;", "setupViews", "app_debug"})
public final class ResponsiveAddEmployeeFragment extends com.manaknight.app.utils.ResponsiveBaseDialogFragment<Manaknight.databinding.BottomAddEmployeeBinding> {
    
    public ResponsiveAddEmployeeFragment() {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public Manaknight.databinding.BottomAddEmployeeBinding getViewBinding(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container) {
        return null;
    }
    
    @java.lang.Override()
    public void setupViews() {
    }
    
    @java.lang.Override()
    public void onDismiss(@org.jetbrains.annotations.NotNull()
    android.content.DialogInterface dialog) {
    }
}