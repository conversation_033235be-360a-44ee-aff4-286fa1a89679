package com.manaknight.app.adapters;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\u0018\u00002\f\u0012\b\u0012\u00060\u0002R\u00020\u00000\u0001:\u0001\u0015B\u0013\u0012\f\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\u0002\u0010\u0006J\b\u0010\n\u001a\u00020\u000bH\u0016J\u001c\u0010\f\u001a\u00020\r2\n\u0010\u000e\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u000f\u001a\u00020\u000bH\u0016J\u001c\u0010\u0010\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u000f\u001a\u00020\u000bH\u0016J\u0014\u0010\u0013\u001a\u00020\r2\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004R \u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0007\u0010\b\"\u0004\b\t\u0010\u0006\u00a8\u0006\u0016"}, d2 = {"Lcom/manaknight/app/adapters/VideosAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/manaknight/app/adapters/VideosAdapter$ViewHolderItem;", "list", "", "Lcom/manaknight/app/model/remote/GetVideoListResponse;", "(Ljava/util/List;)V", "getList", "()Ljava/util/List;", "setList", "getItemCount", "", "onBindViewHolder", "", "viewHolderItem", "i", "onCreateViewHolder", "viewGroup", "Landroid/view/ViewGroup;", "updateData", "newData", "ViewHolderItem", "app_debug"})
public final class VideosAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.manaknight.app.adapters.VideosAdapter.ViewHolderItem> {
    @org.jetbrains.annotations.NotNull()
    private java.util.List<com.manaknight.app.model.remote.GetVideoListResponse> list;
    
    public VideosAdapter(@org.jetbrains.annotations.NotNull()
    java.util.List<com.manaknight.app.model.remote.GetVideoListResponse> list) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.manaknight.app.model.remote.GetVideoListResponse> getList() {
        return null;
    }
    
    public final void setList(@org.jetbrains.annotations.NotNull()
    java.util.List<com.manaknight.app.model.remote.GetVideoListResponse> p0) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.manaknight.app.adapters.VideosAdapter.ViewHolderItem onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup viewGroup, int i) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.adapters.VideosAdapter.ViewHolderItem viewHolderItem, int i) {
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    public final void updateData(@org.jetbrains.annotations.NotNull()
    java.util.List<com.manaknight.app.model.remote.GetVideoListResponse> newData) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u001a\u0010\u0002\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0005\u0010\u0006\"\u0004\b\u0007\u0010\b\u00a8\u0006\t"}, d2 = {"Lcom/manaknight/app/adapters/VideosAdapter$ViewHolderItem;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "binding", "LManaknight/databinding/ItemBroadcastVideoBinding;", "(Lcom/manaknight/app/adapters/VideosAdapter;LManaknight/databinding/ItemBroadcastVideoBinding;)V", "getBinding", "()LManaknight/databinding/ItemBroadcastVideoBinding;", "setBinding", "(LManaknight/databinding/ItemBroadcastVideoBinding;)V", "app_debug"})
    public final class ViewHolderItem extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private Manaknight.databinding.ItemBroadcastVideoBinding binding;
        
        public ViewHolderItem(@org.jetbrains.annotations.NotNull()
        Manaknight.databinding.ItemBroadcastVideoBinding binding) {
            super(null);
        }
        
        @org.jetbrains.annotations.NotNull()
        public final Manaknight.databinding.ItemBroadcastVideoBinding getBinding() {
            return null;
        }
        
        public final void setBinding(@org.jetbrains.annotations.NotNull()
        Manaknight.databinding.ItemBroadcastVideoBinding p0) {
        }
    }
}