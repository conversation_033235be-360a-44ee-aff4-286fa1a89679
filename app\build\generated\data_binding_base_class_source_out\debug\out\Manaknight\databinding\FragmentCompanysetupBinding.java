// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentCompanysetupBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnAddEmployee;

  @NonNull
  public final MaterialButton btnContinue;

  @NonNull
  public final CheckBox checkBox1;

  @NonNull
  public final CheckBox checkBox2;

  @NonNull
  public final ConstraintLayout constraint;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final RelativeLayout container;

  @NonNull
  public final EditText edTxtHourlyRate;

  @NonNull
  public final EditText edTxtProfitOverhead;

  @NonNull
  public final RecyclerView employeeRecylerView;

  @NonNull
  public final HeaderBinding headerInclude;

  @NonNull
  public final TextView hide1;

  @NonNull
  public final TextView hide2;

  @NonNull
  public final ImageView infoButton1;

  @NonNull
  public final ImageView infoButton2;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final ConstraintLayout innerConstraintLayout;

  @NonNull
  public final LinearLayout line1;

  @NonNull
  public final LinearLayout linearDefaultHourlyRate;

  @NonNull
  public final LinearLayout linearDefaultHourlyRate2;

  @NonNull
  public final LinearLayout linearDefaultHourlyRate3;

  @NonNull
  public final LinearLayout linearDefaultProfitOverhead;

  @NonNull
  public final LinearLayout linearDefaultProfitOverhead2;

  @NonNull
  public final LinearLayout linearDefaultProfitOverhead3;

  @NonNull
  public final ScrollView scrollable;

  @NonNull
  public final TextView tvDefaultRate;

  private FragmentCompanysetupBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnAddEmployee, @NonNull MaterialButton btnContinue,
      @NonNull CheckBox checkBox1, @NonNull CheckBox checkBox2,
      @NonNull ConstraintLayout constraint, @Nullable RelativeLayout container,
      @NonNull EditText edTxtHourlyRate, @NonNull EditText edTxtProfitOverhead,
      @NonNull RecyclerView employeeRecylerView, @NonNull HeaderBinding headerInclude,
      @NonNull TextView hide1, @NonNull TextView hide2, @NonNull ImageView infoButton1,
      @NonNull ImageView infoButton2, @Nullable ConstraintLayout innerConstraintLayout,
      @NonNull LinearLayout line1, @NonNull LinearLayout linearDefaultHourlyRate,
      @NonNull LinearLayout linearDefaultHourlyRate2,
      @NonNull LinearLayout linearDefaultHourlyRate3,
      @NonNull LinearLayout linearDefaultProfitOverhead,
      @NonNull LinearLayout linearDefaultProfitOverhead2,
      @NonNull LinearLayout linearDefaultProfitOverhead3, @NonNull ScrollView scrollable,
      @NonNull TextView tvDefaultRate) {
    this.rootView = rootView;
    this.btnAddEmployee = btnAddEmployee;
    this.btnContinue = btnContinue;
    this.checkBox1 = checkBox1;
    this.checkBox2 = checkBox2;
    this.constraint = constraint;
    this.container = container;
    this.edTxtHourlyRate = edTxtHourlyRate;
    this.edTxtProfitOverhead = edTxtProfitOverhead;
    this.employeeRecylerView = employeeRecylerView;
    this.headerInclude = headerInclude;
    this.hide1 = hide1;
    this.hide2 = hide2;
    this.infoButton1 = infoButton1;
    this.infoButton2 = infoButton2;
    this.innerConstraintLayout = innerConstraintLayout;
    this.line1 = line1;
    this.linearDefaultHourlyRate = linearDefaultHourlyRate;
    this.linearDefaultHourlyRate2 = linearDefaultHourlyRate2;
    this.linearDefaultHourlyRate3 = linearDefaultHourlyRate3;
    this.linearDefaultProfitOverhead = linearDefaultProfitOverhead;
    this.linearDefaultProfitOverhead2 = linearDefaultProfitOverhead2;
    this.linearDefaultProfitOverhead3 = linearDefaultProfitOverhead3;
    this.scrollable = scrollable;
    this.tvDefaultRate = tvDefaultRate;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentCompanysetupBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentCompanysetupBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_companysetup, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentCompanysetupBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnAddEmployee;
      MaterialButton btnAddEmployee = ViewBindings.findChildViewById(rootView, id);
      if (btnAddEmployee == null) {
        break missingId;
      }

      id = R.id.btnContinue;
      MaterialButton btnContinue = ViewBindings.findChildViewById(rootView, id);
      if (btnContinue == null) {
        break missingId;
      }

      id = R.id.checkBox1;
      CheckBox checkBox1 = ViewBindings.findChildViewById(rootView, id);
      if (checkBox1 == null) {
        break missingId;
      }

      id = R.id.checkBox2;
      CheckBox checkBox2 = ViewBindings.findChildViewById(rootView, id);
      if (checkBox2 == null) {
        break missingId;
      }

      ConstraintLayout constraint = (ConstraintLayout) rootView;

      id = R.id.container;
      RelativeLayout container = ViewBindings.findChildViewById(rootView, id);

      id = R.id.edTxtHourlyRate;
      EditText edTxtHourlyRate = ViewBindings.findChildViewById(rootView, id);
      if (edTxtHourlyRate == null) {
        break missingId;
      }

      id = R.id.edTxtProfitOverhead;
      EditText edTxtProfitOverhead = ViewBindings.findChildViewById(rootView, id);
      if (edTxtProfitOverhead == null) {
        break missingId;
      }

      id = R.id.employeeRecylerView;
      RecyclerView employeeRecylerView = ViewBindings.findChildViewById(rootView, id);
      if (employeeRecylerView == null) {
        break missingId;
      }

      id = R.id.headerInclude;
      View headerInclude = ViewBindings.findChildViewById(rootView, id);
      if (headerInclude == null) {
        break missingId;
      }
      HeaderBinding binding_headerInclude = HeaderBinding.bind(headerInclude);

      id = R.id.hide1;
      TextView hide1 = ViewBindings.findChildViewById(rootView, id);
      if (hide1 == null) {
        break missingId;
      }

      id = R.id.hide2;
      TextView hide2 = ViewBindings.findChildViewById(rootView, id);
      if (hide2 == null) {
        break missingId;
      }

      id = R.id.infoButton1;
      ImageView infoButton1 = ViewBindings.findChildViewById(rootView, id);
      if (infoButton1 == null) {
        break missingId;
      }

      id = R.id.infoButton2;
      ImageView infoButton2 = ViewBindings.findChildViewById(rootView, id);
      if (infoButton2 == null) {
        break missingId;
      }

      id = R.id.innerConstraintLayout;
      ConstraintLayout innerConstraintLayout = ViewBindings.findChildViewById(rootView, id);

      id = R.id.line1;
      LinearLayout line1 = ViewBindings.findChildViewById(rootView, id);
      if (line1 == null) {
        break missingId;
      }

      id = R.id.linearDefaultHourlyRate;
      LinearLayout linearDefaultHourlyRate = ViewBindings.findChildViewById(rootView, id);
      if (linearDefaultHourlyRate == null) {
        break missingId;
      }

      id = R.id.linearDefaultHourlyRate2;
      LinearLayout linearDefaultHourlyRate2 = ViewBindings.findChildViewById(rootView, id);
      if (linearDefaultHourlyRate2 == null) {
        break missingId;
      }

      id = R.id.linearDefaultHourlyRate3;
      LinearLayout linearDefaultHourlyRate3 = ViewBindings.findChildViewById(rootView, id);
      if (linearDefaultHourlyRate3 == null) {
        break missingId;
      }

      id = R.id.linearDefaultProfitOverhead;
      LinearLayout linearDefaultProfitOverhead = ViewBindings.findChildViewById(rootView, id);
      if (linearDefaultProfitOverhead == null) {
        break missingId;
      }

      id = R.id.linearDefaultProfitOverhead2;
      LinearLayout linearDefaultProfitOverhead2 = ViewBindings.findChildViewById(rootView, id);
      if (linearDefaultProfitOverhead2 == null) {
        break missingId;
      }

      id = R.id.linearDefaultProfitOverhead3;
      LinearLayout linearDefaultProfitOverhead3 = ViewBindings.findChildViewById(rootView, id);
      if (linearDefaultProfitOverhead3 == null) {
        break missingId;
      }

      id = R.id.scrollable;
      ScrollView scrollable = ViewBindings.findChildViewById(rootView, id);
      if (scrollable == null) {
        break missingId;
      }

      id = R.id.tvDefaultRate;
      TextView tvDefaultRate = ViewBindings.findChildViewById(rootView, id);
      if (tvDefaultRate == null) {
        break missingId;
      }

      return new FragmentCompanysetupBinding((ConstraintLayout) rootView, btnAddEmployee,
          btnContinue, checkBox1, checkBox2, constraint, container, edTxtHourlyRate,
          edTxtProfitOverhead, employeeRecylerView, binding_headerInclude, hide1, hide2,
          infoButton1, infoButton2, innerConstraintLayout, line1, linearDefaultHourlyRate,
          linearDefaultHourlyRate2, linearDefaultHourlyRate3, linearDefaultProfitOverhead,
          linearDefaultProfitOverhead2, linearDefaultProfitOverhead3, scrollable, tvDefaultRate);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
