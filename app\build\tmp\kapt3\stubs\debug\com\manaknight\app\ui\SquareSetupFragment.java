package com.manaknight.app.ui;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0005\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u001d\u001a\u00020\u001eH\u0016J\u0018\u0010\u001f\u001a\u00020\u001e2\u0006\u0010 \u001a\u00020\u001b2\u0006\u0010!\u001a\u00020\"H\u0002J\u0018\u0010#\u001a\u00020\u001e2\u0006\u0010 \u001a\u00020\u001b2\u0006\u0010!\u001a\u00020\"H\u0002J\b\u0010$\u001a\u00020\u001eH\u0016J\u001a\u0010%\u001a\u00020\u001e2\u0006\u0010&\u001a\u00020\'2\b\u0010(\u001a\u0004\u0018\u00010)H\u0016J\u0018\u0010*\u001a\u00020\u001e2\u000e\u0010+\u001a\n\u0012\u0004\u0012\u00020\u001b\u0018\u00010,H\u0002J\b\u0010-\u001a\u00020\u001eH\u0002J\b\u0010.\u001a\u00020\u001eH\u0002J\u0018\u0010/\u001a\u00020\u001e2\u0006\u0010 \u001a\u00020\u001b2\u0006\u00100\u001a\u00020\"H\u0002R\u001b\u0010\u0003\u001a\u00020\u00048BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0007\u0010\b\u001a\u0004\b\u0005\u0010\u0006R\u001b\u0010\t\u001a\u00020\n8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\r\u0010\u000e\u001a\u0004\b\u000b\u0010\fR\u001b\u0010\u000f\u001a\u00020\u00108BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0013\u0010\b\u001a\u0004\b\u0011\u0010\u0012R\u001b\u0010\u0014\u001a\u00020\u00158BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0018\u0010\b\u001a\u0004\b\u0016\u0010\u0017R\u001e\u0010\u0019\u001a\u0012\u0012\u0004\u0012\u00020\u001b0\u001aj\b\u0012\u0004\u0012\u00020\u001b`\u001cX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00061"}, d2 = {"Lcom/manaknight/app/ui/SquareSetupFragment;", "Landroidx/fragment/app/Fragment;", "()V", "baasViewModel", "Lcom/manaknight/app/viewmodels/BaasViewModel;", "getBaasViewModel", "()Lcom/manaknight/app/viewmodels/BaasViewModel;", "baasViewModel$delegate", "Lkotlin/Lazy;", "binding", "LManaknight/databinding/FragmentSquresetupBinding;", "getBinding", "()LManaknight/databinding/FragmentSquresetupBinding;", "binding$delegate", "Lcom/manaknight/app/extensions/FragmentDelegate;", "pref", "Lcom/manaknight/app/data/local/AppPreferences;", "getPref", "()Lcom/manaknight/app/data/local/AppPreferences;", "pref$delegate", "sqaureAdapter", "Lcom/manaknight/app/adapter/SquareAdapter;", "getSqaureAdapter", "()Lcom/manaknight/app/adapter/SquareAdapter;", "sqaureAdapter$delegate", "sqaureList", "Ljava/util/ArrayList;", "Lcom/manaknight/app/model/remote/LinealModel;", "Lkotlin/collections/ArrayList;", "onResume", "", "onSquareDeleteClick", "item", "position", "", "onSquareEditClick", "onStop", "onViewCreated", "view", "Landroid/view/View;", "savedInstanceState", "Landroid/os/Bundle;", "setSqaureList", "message", "", "showAddSqaure", "showDialog", "showEditSqaure", "index", "app_debug"})
public final class SquareSetupFragment extends androidx.fragment.app.Fragment {
    @org.jetbrains.annotations.NotNull()
    private final com.manaknight.app.extensions.FragmentDelegate binding$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy baasViewModel$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy pref$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy sqaureAdapter$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<com.manaknight.app.model.remote.LinealModel> sqaureList = null;
    
    public SquareSetupFragment() {
        super();
    }
    
    private final Manaknight.databinding.FragmentSquresetupBinding getBinding() {
        return null;
    }
    
    private final com.manaknight.app.viewmodels.BaasViewModel getBaasViewModel() {
        return null;
    }
    
    private final com.manaknight.app.data.local.AppPreferences getPref() {
        return null;
    }
    
    private final com.manaknight.app.adapter.SquareAdapter getSqaureAdapter() {
        return null;
    }
    
    private final void onSquareEditClick(com.manaknight.app.model.remote.LinealModel item, int position) {
    }
    
    private final void onSquareDeleteClick(com.manaknight.app.model.remote.LinealModel item, int position) {
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setSqaureList(java.util.List<com.manaknight.app.model.remote.LinealModel> message) {
    }
    
    private final void showDialog() {
    }
    
    private final void showAddSqaure() {
    }
    
    private final void showEditSqaure(com.manaknight.app.model.remote.LinealModel item, int index) {
    }
    
    @java.lang.Override()
    public void onResume() {
    }
    
    @java.lang.Override()
    public void onStop() {
    }
}