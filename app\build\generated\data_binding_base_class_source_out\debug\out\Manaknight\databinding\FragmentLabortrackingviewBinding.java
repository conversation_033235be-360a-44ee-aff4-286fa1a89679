// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentLabortrackingviewBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView appCompatTextView2;

  @NonNull
  public final LinearLayout linearlayout8e83f3a7;

  @NonNull
  public final LinearLayout lytHead;

  private FragmentLabortrackingviewBinding(@NonNull LinearLayout rootView,
      @NonNull TextView appCompatTextView2, @NonNull LinearLayout linearlayout8e83f3a7,
      @NonNull LinearLayout lytHead) {
    this.rootView = rootView;
    this.appCompatTextView2 = appCompatTextView2;
    this.linearlayout8e83f3a7 = linearlayout8e83f3a7;
    this.lytHead = lytHead;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentLabortrackingviewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentLabortrackingviewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_labortrackingview, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentLabortrackingviewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.appCompatTextView2;
      TextView appCompatTextView2 = ViewBindings.findChildViewById(rootView, id);
      if (appCompatTextView2 == null) {
        break missingId;
      }

      id = R.id.linearlayout_8e83f3a7;
      LinearLayout linearlayout8e83f3a7 = ViewBindings.findChildViewById(rootView, id);
      if (linearlayout8e83f3a7 == null) {
        break missingId;
      }

      id = R.id.lytHead;
      LinearLayout lytHead = ViewBindings.findChildViewById(rootView, id);
      if (lytHead == null) {
        break missingId;
      }

      return new FragmentLabortrackingviewBinding((LinearLayout) rootView, appCompatTextView2,
          linearlayout8e83f3a7, lytHead);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
