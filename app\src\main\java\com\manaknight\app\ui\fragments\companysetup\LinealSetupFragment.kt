
package com.manaknight.app.ui

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import Manaknight.R
import Manaknight.databinding.BottomAddEmployeeBinding
import Manaknight.databinding.BottomAddLinealBinding
import Manaknight.databinding.BottomAddMaterialBinding
import Manaknight.databinding.DialogAddLinealBinding
import Manaknight.databinding.DialogAddMaterialBinding
import Manaknight.databinding.DialogForgetpasswordBinding
import Manaknight.databinding.FragmentCompanysetupBinding
import Manaknight.databinding.FragmentLinealsetupBinding
import Manaknight.databinding.FragmentMaterialsetupBinding
import Manaknight.databinding.FragmentSubscriptionBinding
import android.app.Activity
import android.app.AlertDialog
import android.content.ContentValues.TAG
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.ViewGroup
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.Toast
import androidx.core.widget.doAfterTextChanged
import androidx.transition.Visibility
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.manaknight.app.adapter.AlertsAdapter
import com.manaknight.app.adapter.EmployeeAdapter
import com.manaknight.app.adapter.LinealAdapter
import com.manaknight.app.adapter.MaterialAdapter
import com.manaknight.app.data.local.AppPreferences
import com.manaknight.app.extensions.hide
import com.manaknight.app.extensions.hideSoftKeyboard
import com.manaknight.app.extensions.setVerticalLayout
import com.manaknight.app.extensions.show
import com.manaknight.app.extensions.viewBinding
import com.manaknight.app.model.remote.AlertModel
import com.manaknight.app.model.remote.EmplyeeModel
import com.manaknight.app.model.remote.LinealModel
import com.manaknight.app.viewmodels.BaasViewModel
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class LinealSetupFragment : Fragment(R.layout.fragment_linealsetup)  {

    private val binding by viewBinding(FragmentLinealsetupBinding::bind)
    private val baasViewModel: BaasViewModel by viewModel()
    private val pref by inject<AppPreferences>()
    private val linealAdapter by lazy { LinealAdapter(this::onLinealEditClick, this::onLinealDeleteClick) }
    private val lineallList: ArrayList<LinealModel> = ArrayList()


    private fun onLinealEditClick(item: LinealModel, position: Int) {
        Log.d(TAG, "onAlertClick: $position")
        showEditLineal(item, position)

    }

    private fun onLinealDeleteClick(item: LinealModel, position: Int) {
        Log.d(TAG, "onAlertClick: $position")
        lineallList.removeAt(position)
        linealAdapter.refresh(lineallList)
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        // binding.icBack.setOnClickListener { findNavController().popBackStack() }

        binding.linealRecylerView.apply {
            setVerticalLayout()
            adapter = linealAdapter
        }

        binding.headerInclude.backButton.setOnClickListener {
            findNavController().popBackStack()
        }

        binding.headerInclude.addPlaterTitle.text = "Lineal Foot Cost"


        binding.btnContinue.setOnClickListener {
            findNavController().navigate(R.id.action_linealsetupFragment_tosquaresetupFragment)
        }

        binding.btnAddLineal.setOnClickListener {
            showAddLineal()
        }

        showDialog()
    }

    private fun setLinealList(message: List<LinealModel>?) {
        linealAdapter.refresh(lineallList)


    }

    private fun showDialog() {
        val builder = AlertDialog.Builder(requireContext())
        val binding = DialogAddLinealBinding.inflate(layoutInflater)
        val dialog = builder.create()

        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

        binding.btnSkip.setOnClickListener {
            dialog.dismiss()
            findNavController().navigate(R.id.action_linealsetupFragment_tosquaresetupFragment)
        }

        binding.btnGotIt.setOnClickListener {
            dialog.dismiss()
        }

        dialog.setView(binding.root)
        dialog.setCancelable(false)
        dialog.show()
    }



    private fun showAddLineal(){
        val sheet = BottomSheetDialog(requireContext(), R.style.BottomSheetDialogTheme)
        val view = BottomAddLinealBinding.inflate(layoutInflater)

        sheet.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
        sheet.setOnShowListener { dialogInterface ->
            val bottomSheetDialog = dialogInterface as BottomSheetDialog
            val bottomSheet = bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)

            if (bottomSheet != null) {
                // Set height to full screen
                bottomSheet.layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT

                // Add a top margin of 100px
                val layoutParams = bottomSheet.layoutParams as ViewGroup.MarginLayoutParams
                layoutParams.topMargin = 0 // Adjust margin as needed
                layoutParams.bottomMargin = 0
                bottomSheet.layoutParams = layoutParams

                // Ensure the bottom sheet is expanded
                val behavior = BottomSheetBehavior.from(bottomSheet)
                behavior.state = BottomSheetBehavior.STATE_EXPANDED
                behavior.isFitToContents = false
                behavior.skipCollapsed = true
            }

//            bottomSheet?.layoutParams?.height = ViewGroup.LayoutParams.MATCH_PARENT
//
//            val behavior = BottomSheetBehavior.from(bottomSheet!!)
//            behavior.state = BottomSheetBehavior.STATE_EXPANDED
//            behavior.isFitToContents = false
//            behavior.skipCollapsed = true
        }

        view.apply {

            edTxtProfitOverHead.setText("")
            remaingCost.setText("0 Remaining")

            btnSave.isEnabled = false
            btnSave.alpha = 0.5f

            /*val textWatcher = object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    // Enable Save button if both fields are non-empty, otherwise disable it


                }

                override fun afterTextChanged(s: Editable?) {


                    val isLinealNameValid = edTxtLinealName.text.isNotEmpty()

                    val isLinealCostValid = edTxtLinealCost.text.isNotEmpty() && try {
                        val value = edTxtLinealCost.text.toString().toFloat()
                        value > 0
                    } catch (e: NumberFormatException) {
                        false
                    }

                    val isLabourCostValid = edTxtLabourCost.text.isNotEmpty() && try {
                        val value = edTxtLabourCost.text.toString().toFloat()
                        value > 0
                    } catch (e: NumberFormatException) {
                        false
                    }


                    // Calculate profit over and remaining cost
                    val profitOver = (edTxtLinealCost.text.toString().toIntOrNull() ?: 0 * 30) / 100
                    val remainingCost = (edTxtLinealCost.text.toString().toIntOrNull() ?: 0) - profitOver
                    val materialCost = remainingCost - (edTxtLabourCost.text.toString().toIntOrNull() ?: 0)

                    // Check if material cost is negative, disable the button if true
                    btnSave.isEnabled = isLinealNameValid && isLinealCostValid && isLabourCostValid && materialCost >= 0
                    btnSave.alpha = if (btnSave.isEnabled) 1f else 0.5f
                }
            }
            edTxtLinealName.addTextChangedListener(textWatcher)
            edTxtLinealCost.addTextChangedListener(textWatcher)
            edTxtLabourCost.addTextChangedListener(textWatcher)*/

            edTxtLinealName.doAfterTextChanged {

                var profitOver = it.toString().isNotEmpty()
                btnSave.isEnabled = profitOver
                btnSave.alpha = if (btnSave.isEnabled) 1f else 0.5f
            }

            edTxtLinealCost.doAfterTextChanged {

                var profitOver = it.toString().toIntOrNull() ?: 0

                val overheadPercentage = if (pref.defaultProfitOverhead == 0) 1 else pref.defaultProfitOverhead
                profitOver = (profitOver * overheadPercentage) / 100
                val remaining = (edTxtLinealCost.text.toString().toIntOrNull() ?: 0) -  profitOver


                remaingCost.setText("$remaining Remaining")
                edTxtProfitOverHead.setText(""+profitOver)

                var laboutCost = edTxtLabourCost.text.toString().toIntOrNull() ?: 0
                val materialCost = remaining - laboutCost
                edTxtMaterialCost.setText(""+materialCost)

                val isLinealCostValid = it.toString().isNotEmpty() && try {
                    val value = edTxtLinealCost.text.toString().toFloat()
                    value > 0
                } catch (e: NumberFormatException) {
                    false
                }

                btnSave.isEnabled = isLinealCostValid && materialCost > 0
                btnSave.alpha = if (btnSave.isEnabled) 1f else 0.5f
                tvFullNameError.visibility = if(isLinealCostValid) View.GONE else View.VISIBLE
                tvMaterialCostError.visibility = if(materialCost >= 0) View.GONE else View.VISIBLE
            }

            edTxtLabourCost.doAfterTextChanged {

                var laboutCost = it.toString().toIntOrNull() ?: 0

                var profitOver = edTxtLinealCost.text.toString().toIntOrNull() ?: 0
                val overheadPercentage = if (pref.defaultProfitOverhead == 0) 1 else pref.defaultProfitOverhead
                profitOver = (profitOver * overheadPercentage) / 100
                val remaining = (edTxtLinealCost.text.toString().toIntOrNull() ?: 0) -  profitOver
                val materialCost = remaining - laboutCost
                edTxtMaterialCost.setText(""+materialCost)

                val isLabourCostValid = edTxtLabourCost.text.isNotEmpty() && try {
                    val value = edTxtLabourCost.text.toString().toFloat()
                    value > 0
                } catch (e: NumberFormatException) {
                    false
                }

                btnSave.isEnabled = isLabourCostValid && materialCost > 0
                btnSave.alpha = if (btnSave.isEnabled) 1f else 0.5f

                tvMaterialCostError.visibility = if(materialCost >= 0) View.GONE else View.VISIBLE
            }

            backButton.setOnClickListener {
                sheet.dismiss()
            }
            btnSave.setOnClickListener {


                if(edTxtLinealName.text.toString().isEmpty()) {
                    Toast.makeText(context, "Please enter lineal name", Toast.LENGTH_SHORT).show();
                } else if(edTxtLinealCost.text.isEmpty()) {
                    Toast.makeText(context, "Please enter lineal foot cost", Toast.LENGTH_SHORT).show();
                } else if(edTxtLabourCost.text.isEmpty()) {
                    Toast.makeText(context, "Please enter labour foot cost", Toast.LENGTH_SHORT).show();
                } else {

                    var profitOver = edTxtLinealCost.text.toString().toIntOrNull() ?: 0

                    val overheadPercentage = if (pref.defaultProfitOverhead == 0) 1 else pref.defaultProfitOverhead
                    profitOver = (profitOver * overheadPercentage) / 100
                    val remaining = (edTxtLinealCost.text.toString().toIntOrNull() ?: 0) -  profitOver


                    val item = LinealModel(0,
                        0,
                        "",
                        0,
                        "",
                        "",
                        "",
                        edTxtLinealName.text.toString(),
                        edTxtLinealCost.text.toString(),
                    ""+profitOver,
                    edTxtLabourCost.text.toString(),
                        edTxtMaterialCost.text.toString(),
                    ""+remaining)



                    lineallList.add(item)
                    setLinealList(lineallList)
                    sheet.dismiss()
                }
            }

            line1.setOnClickListener() {
                val imm = requireActivity().getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
                imm.hideSoftInputFromWindow(view.root.windowToken, 0)

            }

        }

        sheet.setOnDismissListener {
            requireActivity().hideSoftKeyboard()
        }
        sheet.setCancelable(true)
        sheet.setContentView(view.root)
        sheet.show()
    }

    private fun showEditLineal(item: LinealModel, index: Int) {
        val sheet = BottomSheetDialog(requireContext(), R.style.BottomSheetDialogTheme)
        val view = BottomAddLinealBinding.inflate(layoutInflater)

        sheet.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
        sheet.setOnShowListener { dialogInterface ->
            val bottomSheetDialog = dialogInterface as BottomSheetDialog
            val bottomSheet = bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)

            if (bottomSheet != null) {
                // Set height to full screen
                bottomSheet.layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT

                // Add a top margin of 100px
                val layoutParams = bottomSheet.layoutParams as ViewGroup.MarginLayoutParams
                layoutParams.topMargin = 0 // Adjust margin as needed
                layoutParams.bottomMargin = 0
                bottomSheet.layoutParams = layoutParams

                // Ensure the bottom sheet is expanded
                val behavior = BottomSheetBehavior.from(bottomSheet)
                behavior.state = BottomSheetBehavior.STATE_EXPANDED
                behavior.isFitToContents = false
                behavior.skipCollapsed = true
            }

//            bottomSheet?.layoutParams?.height = ViewGroup.LayoutParams.MATCH_PARENT
//
//            val behavior = BottomSheetBehavior.from(bottomSheet!!)
//            behavior.state = BottomSheetBehavior.STATE_EXPANDED
//            behavior.isFitToContents = false
//            behavior.skipCollapsed = true
        }

        view.apply {


            addHeading.text = "Edit Lineal Foot Cost"
            edTxtLinealName.setText(item.name)
            edTxtLinealCost.setText(item.lieanlCost)
            edTxtProfitOverHead.setText(item.profitOverHead)
            remaingCost.setText(item.remainingCost + " Remaining")
            edTxtLabourCost.setText(item.labourCost)
            edTxtMaterialCost.setText(item.materialCost)

            /*val textWatcher = object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    // Enable Save button if both fields are non-empty, otherwise disable it
                    val isLinealNameValid = edTxtLinealName.text.isNotEmpty()

                    val isLinealCostValid = edTxtLinealCost.text.isNotEmpty() && try {
                        val value = edTxtLinealCost.text.toString().toFloat()
                        value > 0
                    } catch (e: NumberFormatException) {
                        false
                    }

                    val isLabourCostValid = edTxtLabourCost.text.isNotEmpty() && try {
                        val value = edTxtLabourCost.text.toString().toFloat()
                        value > 0
                    } catch (e: NumberFormatException) {
                        false
                    }


                    // Calculate profit over and remaining cost
                    val profitOver = (edTxtLinealCost.text.toString().toIntOrNull() ?: 0 * 30) / 100
                    val remainingCost = (edTxtLinealCost.text.toString().toIntOrNull() ?: 0) - profitOver
                    val materialCost = remainingCost - (edTxtLabourCost.text.toString().toIntOrNull() ?: 0)

                    // Check if material cost is negative, disable the button if true
                    btnSave.isEnabled = isLinealNameValid && isLinealCostValid && isLabourCostValid && materialCost >= 0
                    btnSave.alpha = if (btnSave.isEnabled) 1f else 0.5f

                }

                override fun afterTextChanged(s: Editable?) {}
            }
            edTxtLinealName.addTextChangedListener(textWatcher)
            edTxtLinealCost.addTextChangedListener(textWatcher)
            edTxtLabourCost.addTextChangedListener(textWatcher)*/

            edTxtLinealName.doAfterTextChanged {

                var profitOver = it.toString().isNotEmpty()
                btnSave.isEnabled = profitOver
                btnSave.alpha = if (btnSave.isEnabled) 1f else 0.5f
            }

            edTxtLinealCost.doAfterTextChanged {

                var profitOver = it.toString().toIntOrNull() ?: 0

                val overheadPercentage = if (pref.defaultProfitOverhead == 0) 1 else pref.defaultProfitOverhead
                profitOver = (profitOver * overheadPercentage) / 100
                val remaining = (edTxtLinealCost.text.toString().toIntOrNull() ?: 0) -  profitOver


                remaingCost.setText("$remaining Remaining")
                edTxtProfitOverHead.setText(""+profitOver)

                var laboutCost = edTxtLabourCost.text.toString().toIntOrNull() ?: 0
                val materialCost = remaining - laboutCost
                edTxtMaterialCost.setText(""+materialCost)

                val isLinealCostValid = it.toString().isNotEmpty() && try {
                    val value = edTxtLinealCost.text.toString().toFloat()
                    value > 0
                } catch (e: NumberFormatException) {
                    false
                }

                btnSave.isEnabled = isLinealCostValid && materialCost > 0
                btnSave.alpha = if (btnSave.isEnabled) 1f else 0.5f

                tvFullNameError.visibility = if(isLinealCostValid) View.GONE else View.VISIBLE
                tvMaterialCostError.visibility = if(materialCost >= 0) View.GONE else View.VISIBLE

            }

            edTxtLabourCost.doAfterTextChanged {

                var laboutCost = it.toString().toIntOrNull() ?: 0

                var profitOver = edTxtLinealCost.text.toString().toIntOrNull() ?: 0
                val overheadPercentage = if (pref.defaultProfitOverhead == 0) 1 else pref.defaultProfitOverhead
                profitOver = (profitOver * overheadPercentage) / 100
                val remaining = (edTxtLinealCost.text.toString().toIntOrNull() ?: 0) -  profitOver
                val materialCost = remaining - laboutCost
                edTxtMaterialCost.setText(""+materialCost)

                val isLabourCostValid = edTxtLabourCost.text.isNotEmpty() && try {
                    val value = edTxtLabourCost.text.toString().toFloat()
                    value > 0
                } catch (e: NumberFormatException) {
                    false
                }

                btnSave.isEnabled = isLabourCostValid && materialCost > 0
                btnSave.alpha = if (btnSave.isEnabled) 1f else 0.5f

                tvMaterialCostError.visibility = if(materialCost >= 0) View.GONE else View.VISIBLE
            }

            backButton.setOnClickListener {
                sheet.dismiss()
            }
            btnSave.setOnClickListener {


                if(edTxtLinealName.text.toString().isEmpty()) {
                    Toast.makeText(context, "Please enter lineal name", Toast.LENGTH_SHORT).show();
                } else if(edTxtLinealCost.text.isEmpty()) {
                    Toast.makeText(context, "Please enter lineal foot cost", Toast.LENGTH_SHORT).show();
                } else if(edTxtLabourCost.text.isEmpty()) {
                    Toast.makeText(context, "Please enter labour foot cost", Toast.LENGTH_SHORT).show();
                } else {


                    var profitOver = edTxtLinealCost.text.toString().toIntOrNull() ?: 0

                    val overheadPercentage = if (pref.defaultProfitOverhead == 0) 1 else pref.defaultProfitOverhead
                    profitOver = (profitOver * overheadPercentage) / 100
                    val remaining = (edTxtLinealCost.text.toString().toIntOrNull() ?: 0) -  profitOver

                    val newItem = LinealModel(0,
                        0,
                        "",
                        0,
                        "",
                        "",
                        "",
                        edTxtLinealName.text.toString(),
                        edTxtLinealCost.text.toString(),
                        ""+profitOver,
                        edTxtLabourCost.text.toString(),
                        edTxtMaterialCost.text.toString(),
                        ""+remaining)


                    lineallList.removeAt(index)
                    lineallList.add(index, newItem)
                    setLinealList(lineallList)
                    sheet.dismiss()
                }
            }

            line1.setOnClickListener() {
                val imm = requireActivity().getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
                imm.hideSoftInputFromWindow(view.root.windowToken, 0)

            }

        }

        sheet.setOnDismissListener {
            requireActivity().hideSoftKeyboard()
        }
        sheet.setCancelable(true)
        sheet.setContentView(view.root)
        sheet.show()
    }

    override fun onResume() {
        super.onResume()
        binding.headerInclude.backButton.show()
        // (activity as AppCompatActivity?)!!.supportActionBar!!.hide()
    }

    override fun onStop() {
        super.onStop()
        // (activity as AppCompatActivity?)!!.supportActionBar!!.show()
    }
}
