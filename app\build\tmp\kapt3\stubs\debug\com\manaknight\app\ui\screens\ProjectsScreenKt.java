package com.manaknight.app.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000d\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u000f\n\u0002\u0010\"\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0007\u001a\u001c\u0010\u0005\u001a\u00020\u00062\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00060\bH\u0007\u001a\u0010\u0010\n\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\tH\u0007\u001a\b\u0010\f\u001a\u00020\u0006H\u0007\u001a\b\u0010\r\u001a\u00020\u0006H\u0007\u001a\u0016\u0010\u000e\u001a\u00020\u00062\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00060\u0010H\u0007\u001a\b\u0010\u0011\u001a\u00020\u0006H\u0007\u001a\u001e\u0010\u0012\u001a\u00020\u00062\u0006\u0010\u0013\u001a\u00020\t2\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00060\u0010H\u0007\u001a2\u0010\u0015\u001a\u00020\u00062\u0006\u0010\u0013\u001a\u00020\t2\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00060\u00102\u0012\u0010\u0017\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00060\bH\u0007\u001a*\u0010\u0018\u001a\u00020\u00062\f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00060\u00102\u0012\u0010\u0017\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00060\bH\u0007\u001a$\u0010\u001a\u001a\u00020\u00062\u0006\u0010\u001b\u001a\u00020\t2\u0012\u0010\u0017\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00060\bH\u0007\u001a*\u0010\u001c\u001a\u00020\u00062\f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00060\u00102\u0012\u0010\u001d\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00060\bH\u0007\u001a>\u0010\u001e\u001a\u00020\u00062\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\t0 2\f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00060\u00102\u0018\u0010!\u001a\u0014\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0 \u0012\u0004\u0012\u00020\u00060\bH\u0007\u001a,\u0010\"\u001a\u00020\u00062\u0006\u0010#\u001a\u00020\t2\u0006\u0010$\u001a\u00020%2\u0012\u0010&\u001a\u000e\u0012\u0004\u0012\u00020%\u0012\u0004\u0012\u00020\u00060\bH\u0007\u001a\u001c\u0010\'\u001a\u00020\u00062\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00060\bH\u0007\u001a\u0016\u0010(\u001a\u00020\u00062\f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00060\u0010H\u0007\u001a,\u0010)\u001a\u00020\u00062\u0006\u0010*\u001a\u00020+2\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00060\u00102\f\u0010,\u001a\b\u0012\u0004\u0012\u00020\u00060\u0010H\u0007\u001a>\u0010-\u001a\u00020\u00062\f\u0010.\u001a\b\u0012\u0004\u0012\u00020+0/2\u0012\u00100\u001a\u000e\u0012\u0004\u0012\u000201\u0012\u0004\u0012\u00020\u00060\b2\u0012\u0010,\u001a\u000e\u0012\u0004\u0012\u00020+\u0012\u0004\u0012\u00020\u00060\bH\u0007\u001ax\u00102\u001a\u00020\u00062\u000e\u00103\u001a\n\u0012\u0004\u0012\u00020\u0002\u0018\u00010\u00012\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\t0 2\u0012\u00100\u001a\u000e\u0012\u0004\u0012\u000201\u0012\u0004\u0012\u00020\u00060\b2\u0006\u00104\u001a\u0002052\u0006\u0010\u0013\u001a\u00020\t2\u0018\u00106\u001a\u0014\u0012\u0004\u0012\u000201\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u0006072\u0012\u00108\u001a\u000e\u0012\u0004\u0012\u000201\u0012\u0004\u0012\u00020\u00060\bH\u0007\u001az\u00109\u001a\u00020\u00062\u0006\u0010:\u001a\u0002012\u0006\u0010;\u001a\u00020<2\u0012\u00100\u001a\u000e\u0012\u0004\u0012\u000201\u0012\u0004\u0012\u00020\u00060\b2\u0006\u00104\u001a\u0002052\u0018\u00106\u001a\u0014\u0012\u0004\u0012\u000201\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u0006072\u0012\u00108\u001a\u000e\u0012\u0004\u0012\u000201\u0012\u0004\u0012\u00020\u00060\b2\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00060\u00102\b\b\u0002\u0010=\u001a\u00020\tH\u0007\u001a\b\u0010>\u001a\u00020\u0006H\u0007\u001a\b\u0010?\u001a\u00020\u0006H\u0007\u001a\u0010\u0010@\u001a\u00020\u00062\u0006\u0010#\u001a\u00020\tH\u0007\u001a$\u0010A\u001a\u00020\u00062\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\t0 2\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00060\u0010H\u0007\u001a$\u0010B\u001a\u00020\u00062\u0006\u0010#\u001a\u00020\t2\u0012\u0010\u001d\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00060\bH\u0007\"\u0017\u0010\u0000\u001a\b\u0012\u0004\u0012\u00020\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0003\u0010\u0004\u00a8\u0006C"}, d2 = {"mockProjectApiResponse", "Lcom/manaknight/app/network/Resource;", "Lcom/manaknight/app/model/remote/ProjectResponseModel;", "getMockProjectApiResponse", "()Lcom/manaknight/app/network/Resource;", "BottomSheetContent", "", "onOptionClick", "Lkotlin/Function1;", "", "DropdownFilter", "label", "DropdownFilterPreview", "Filters", "Header", "onNavigateToCreateEstimate", "Lkotlin/Function0;", "HeaderPreview", "MonthFilterDropdown", "selectedMonth", "onClick", "MonthFilterSection", "onShowMonthFilter", "onMonthSelected", "MonthFilterSheet", "onDismiss", "MonthOption", "month", "MonthlyFilterSheet", "onStatusSelected", "MultiSelectStatusFilterSheet", "selectedStatuses", "", "onStatusesChanged", "MultiSelectStatusOption", "status", "isSelected", "", "onSelectionChanged", "ProjectBottomSheetContentBody", "ProjectBottomSheetHeader", "ProjectCard", "project", "Lcom/manaknight/app/model/remote/list;", "onMoreClick", "ProjectList", "projects", "", "onProjectClick", "", "ProjectListContent", "projectsResource", "dialog", "Landroid/app/Dialog;", "onNavigateToLineItems", "Lkotlin/Function2;", "onNavigateToViewEstimate", "ProjectsScreen", "userId", "viewModel", "Lcom/manaknight/app/viewmodels/BaasViewModel;", "initialSelectedStatus", "ProjectsScreenMobilePreview", "ProjectsScreenTabletPreview", "StatusBadge", "StatusFilterDropdown", "StatusOption", "app_debug"})
public final class ProjectsScreenKt {
    @org.jetbrains.annotations.NotNull()
    private static final com.manaknight.app.network.Resource<com.manaknight.app.model.remote.ProjectResponseModel> mockProjectApiResponse = null;
    
    @androidx.compose.runtime.Composable()
    public static final void ProjectsScreen(int userId, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.viewmodels.BaasViewModel viewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onProjectClick, @org.jetbrains.annotations.NotNull()
    android.app.Dialog dialog, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Integer, ? super java.lang.String, kotlin.Unit> onNavigateToLineItems, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onNavigateToViewEstimate, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToCreateEstimate, @org.jetbrains.annotations.NotNull()
    java.lang.String initialSelectedStatus) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void Header(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToCreateEstimate) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void Filters() {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DropdownFilter(@org.jetbrains.annotations.NotNull()
    java.lang.String label) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void StatusFilterDropdown(@org.jetbrains.annotations.NotNull()
    java.util.Set<java.lang.String> selectedStatuses, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void MonthFilterSection(@org.jetbrains.annotations.NotNull()
    java.lang.String selectedMonth, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onShowMonthFilter, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onMonthSelected) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void MonthFilterDropdown(@org.jetbrains.annotations.NotNull()
    java.lang.String selectedMonth, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void MonthFilterSheet(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onMonthSelected) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void MonthlyFilterSheet(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onStatusSelected) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void MultiSelectStatusFilterSheet(@org.jetbrains.annotations.NotNull()
    java.util.Set<java.lang.String> selectedStatuses, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.util.Set<java.lang.String>, kotlin.Unit> onStatusesChanged) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void MultiSelectStatusOption(@org.jetbrains.annotations.NotNull()
    java.lang.String status, boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onSelectionChanged) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void StatusOption(@org.jetbrains.annotations.NotNull()
    java.lang.String status, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onStatusSelected) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void MonthOption(@org.jetbrains.annotations.NotNull()
    java.lang.String month, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onMonthSelected) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void ProjectListContent(@org.jetbrains.annotations.Nullable()
    com.manaknight.app.network.Resource<com.manaknight.app.model.remote.ProjectResponseModel> projectsResource, @org.jetbrains.annotations.NotNull()
    java.util.Set<java.lang.String> selectedStatuses, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onProjectClick, @org.jetbrains.annotations.NotNull()
    android.app.Dialog dialog, @org.jetbrains.annotations.NotNull()
    java.lang.String selectedMonth, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Integer, ? super java.lang.String, kotlin.Unit> onNavigateToLineItems, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onNavigateToViewEstimate) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ProjectList(@org.jetbrains.annotations.NotNull()
    java.util.List<com.manaknight.app.model.remote.list> projects, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onProjectClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.list, kotlin.Unit> onMoreClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ProjectCard(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.list project, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onMoreClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void BottomSheetContent(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onOptionClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ProjectBottomSheetHeader(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ProjectBottomSheetContentBody(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onOptionClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void StatusBadge(@org.jetbrains.annotations.NotNull()
    java.lang.String status) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final com.manaknight.app.network.Resource<com.manaknight.app.model.remote.ProjectResponseModel> getMockProjectApiResponse() {
        return null;
    }
    
    @androidx.compose.ui.tooling.preview.Preview(showBackground = true, widthDp = 360, heightDp = 640)
    @androidx.compose.runtime.Composable()
    public static final void ProjectsScreenMobilePreview() {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(showBackground = true, widthDp = 800, heightDp = 600, name = "Tablet Layout - Single Column")
    @androidx.compose.runtime.Composable()
    public static final void ProjectsScreenTabletPreview() {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(showBackground = true)
    @androidx.compose.runtime.Composable()
    public static final void HeaderPreview() {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(showBackground = true)
    @androidx.compose.runtime.Composable()
    public static final void DropdownFilterPreview() {
    }
}