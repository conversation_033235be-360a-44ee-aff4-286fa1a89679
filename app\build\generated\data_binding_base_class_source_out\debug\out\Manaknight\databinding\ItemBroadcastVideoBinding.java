// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemBroadcastVideoBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final CardView cvImage;

  @NonNull
  public final ImageView imageView;

  @NonNull
  public final TextView tvCountry;

  @NonNull
  public final TextView tvName;

  @NonNull
  public final TextView tvPrice;

  @NonNull
  public final TextView tvUploadTime;

  private ItemBroadcastVideoBinding(@NonNull ConstraintLayout rootView, @NonNull CardView cvImage,
      @NonNull ImageView imageView, @NonNull TextView tvCountry, @NonNull TextView tvName,
      @NonNull TextView tvPrice, @NonNull TextView tvUploadTime) {
    this.rootView = rootView;
    this.cvImage = cvImage;
    this.imageView = imageView;
    this.tvCountry = tvCountry;
    this.tvName = tvName;
    this.tvPrice = tvPrice;
    this.tvUploadTime = tvUploadTime;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemBroadcastVideoBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemBroadcastVideoBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_broadcast_video, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemBroadcastVideoBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cvImage;
      CardView cvImage = ViewBindings.findChildViewById(rootView, id);
      if (cvImage == null) {
        break missingId;
      }

      id = R.id.imageView;
      ImageView imageView = ViewBindings.findChildViewById(rootView, id);
      if (imageView == null) {
        break missingId;
      }

      id = R.id.tvCountry;
      TextView tvCountry = ViewBindings.findChildViewById(rootView, id);
      if (tvCountry == null) {
        break missingId;
      }

      id = R.id.tvName;
      TextView tvName = ViewBindings.findChildViewById(rootView, id);
      if (tvName == null) {
        break missingId;
      }

      id = R.id.tvPrice;
      TextView tvPrice = ViewBindings.findChildViewById(rootView, id);
      if (tvPrice == null) {
        break missingId;
      }

      id = R.id.tvUploadTime;
      TextView tvUploadTime = ViewBindings.findChildViewById(rootView, id);
      if (tvUploadTime == null) {
        break missingId;
      }

      return new ItemBroadcastVideoBinding((ConstraintLayout) rootView, cvImage, imageView,
          tvCountry, tvName, tvPrice, tvUploadTime);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
