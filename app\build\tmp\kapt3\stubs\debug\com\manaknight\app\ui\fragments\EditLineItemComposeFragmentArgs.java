package com.manaknight.app.ui.fragments;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0015\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\b\u0018\u0000 &2\u00020\u0001:\u0001&B?\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\u0005\u0012\b\b\u0002\u0010\t\u001a\u00020\u0007\u0012\b\b\u0002\u0010\n\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\u000bJ\t\u0010\u0015\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0007H\u00c6\u0003JE\u0010\u001b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00052\b\b\u0002\u0010\t\u001a\u00020\u00072\b\b\u0002\u0010\n\u001a\u00020\u0007H\u00c6\u0001J\u0013\u0010\u001c\u001a\u00020\u001d2\b\u0010\u001e\u001a\u0004\u0018\u00010\u001fH\u00d6\u0003J\t\u0010 \u001a\u00020\u0005H\u00d6\u0001J\u0006\u0010!\u001a\u00020\"J\u0006\u0010#\u001a\u00020$J\t\u0010%\u001a\u00020\u0007H\u00d6\u0001R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\t\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\rR\u0011\u0010\n\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\rR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0013\u00a8\u0006\'"}, d2 = {"Lcom/manaknight/app/ui/fragments/EditLineItemComposeFragmentArgs;", "Landroidx/navigation/NavArgs;", "item", "Lcom/manaknight/app/model/remote/profitPro/JobDetailsRespModel;", "projectID", "", "customerName", "", "lineItemID", "initialDescription", "initialType", "(Lcom/manaknight/app/model/remote/profitPro/JobDetailsRespModel;ILjava/lang/String;ILjava/lang/String;Ljava/lang/String;)V", "getCustomerName", "()Ljava/lang/String;", "getInitialDescription", "getInitialType", "getItem", "()Lcom/manaknight/app/model/remote/profitPro/JobDetailsRespModel;", "getLineItemID", "()I", "getProjectID", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "", "hashCode", "toBundle", "Landroid/os/Bundle;", "toSavedStateHandle", "Landroidx/lifecycle/SavedStateHandle;", "toString", "Companion", "app_debug"})
public final class EditLineItemComposeFragmentArgs implements androidx.navigation.NavArgs {
    @org.jetbrains.annotations.NotNull()
    private final com.manaknight.app.model.remote.profitPro.JobDetailsRespModel item = null;
    private final int projectID = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String customerName = null;
    private final int lineItemID = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String initialDescription = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String initialType = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.manaknight.app.ui.fragments.EditLineItemComposeFragmentArgs.Companion Companion = null;
    
    public EditLineItemComposeFragmentArgs(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.JobDetailsRespModel item, int projectID, @org.jetbrains.annotations.NotNull()
    java.lang.String customerName, int lineItemID, @org.jetbrains.annotations.NotNull()
    java.lang.String initialDescription, @org.jetbrains.annotations.NotNull()
    java.lang.String initialType) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.manaknight.app.model.remote.profitPro.JobDetailsRespModel getItem() {
        return null;
    }
    
    public final int getProjectID() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCustomerName() {
        return null;
    }
    
    public final int getLineItemID() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getInitialDescription() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getInitialType() {
        return null;
    }
    
    @kotlin.Suppress(names = {"CAST_NEVER_SUCCEEDS"})
    @org.jetbrains.annotations.NotNull()
    public final android.os.Bundle toBundle() {
        return null;
    }
    
    @kotlin.Suppress(names = {"CAST_NEVER_SUCCEEDS"})
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.SavedStateHandle toSavedStateHandle() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.manaknight.app.model.remote.profitPro.JobDetailsRespModel component1() {
        return null;
    }
    
    public final int component2() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    public final int component4() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.manaknight.app.ui.fragments.EditLineItemComposeFragmentArgs copy(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.JobDetailsRespModel item, int projectID, @org.jetbrains.annotations.NotNull()
    java.lang.String customerName, int lineItemID, @org.jetbrains.annotations.NotNull()
    java.lang.String initialDescription, @org.jetbrains.annotations.NotNull()
    java.lang.String initialType) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @kotlin.jvm.JvmStatic()
    @kotlin.Suppress(names = {"DEPRECATION"})
    @org.jetbrains.annotations.NotNull()
    public static final com.manaknight.app.ui.fragments.EditLineItemComposeFragmentArgs fromBundle(@org.jetbrains.annotations.NotNull()
    android.os.Bundle bundle) {
        return null;
    }
    
    @kotlin.jvm.JvmStatic()
    @org.jetbrains.annotations.NotNull()
    public static final com.manaknight.app.ui.fragments.EditLineItemComposeFragmentArgs fromSavedStateHandle(@org.jetbrains.annotations.NotNull()
    androidx.lifecycle.SavedStateHandle savedStateHandle) {
        return null;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0007\u001a\u00020\u00042\u0006\u0010\b\u001a\u00020\tH\u0007\u00a8\u0006\n"}, d2 = {"Lcom/manaknight/app/ui/fragments/EditLineItemComposeFragmentArgs$Companion;", "", "()V", "fromBundle", "Lcom/manaknight/app/ui/fragments/EditLineItemComposeFragmentArgs;", "bundle", "Landroid/os/Bundle;", "fromSavedStateHandle", "savedStateHandle", "Landroidx/lifecycle/SavedStateHandle;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @kotlin.jvm.JvmStatic()
        @kotlin.Suppress(names = {"DEPRECATION"})
        @org.jetbrains.annotations.NotNull()
        public final com.manaknight.app.ui.fragments.EditLineItemComposeFragmentArgs fromBundle(@org.jetbrains.annotations.NotNull()
        android.os.Bundle bundle) {
            return null;
        }
        
        @kotlin.jvm.JvmStatic()
        @org.jetbrains.annotations.NotNull()
        public final com.manaknight.app.ui.fragments.EditLineItemComposeFragmentArgs fromSavedStateHandle(@org.jetbrains.annotations.NotNull()
        androidx.lifecycle.SavedStateHandle savedStateHandle) {
            return null;
        }
    }
}