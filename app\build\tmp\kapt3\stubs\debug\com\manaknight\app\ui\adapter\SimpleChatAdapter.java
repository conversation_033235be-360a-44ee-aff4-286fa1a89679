package com.manaknight.app.ui.adapter;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0013\u0018\u0000 )2\b\u0012\u0004\u0012\u00020\u00020\u0001:\b)*+,-./0B\r\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\u0002\u0010\u0005J\u000e\u0010\u0013\u001a\u00020\f2\u0006\u0010\u0006\u001a\u00020\bJ\u0014\u0010\u0013\u001a\u00020\f2\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\b0\u0015J\b\u0010\u0016\u001a\u00020\fH\u0007J\b\u0010\u0017\u001a\u00020\u0012H\u0016J\u0010\u0010\u0018\u001a\u00020\u00122\u0006\u0010\u0019\u001a\u00020\u0012H\u0016J\u0018\u0010\u001a\u001a\u00020\f2\u0006\u0010\u001b\u001a\u00020\u00022\u0006\u0010\u0019\u001a\u00020\u0012H\u0016J\u0018\u0010\u001c\u001a\u00020\u00022\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020\u0012H\u0016J\u0010\u0010 \u001a\u00020\f2\u0006\u0010\u0006\u001a\u00020\bH\u0007J\u0010\u0010 \u001a\u00020\f2\u0006\u0010\u0019\u001a\u00020\u0012H\u0007J\u001a\u0010!\u001a\u00020\f2\u0012\u0010\"\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\f0\u000bJ\u001a\u0010#\u001a\u00020\f2\u0012\u0010\"\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\f0\u000bJ\u001a\u0010$\u001a\u00020\f2\u0012\u0010\"\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\f0\u000bJ\u001a\u0010%\u001a\u00020\f2\u0012\u0010\"\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\f0\u000bJ\u001a\u0010&\u001a\u00020\f2\u0012\u0010\"\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\f0\u000bJ\u000e\u0010\'\u001a\u00020\f2\u0006\u0010(\u001a\u00020\u0012R\u001e\u0010\u0006\u001a\u0012\u0012\u0004\u0012\u00020\b0\u0007j\b\u0012\u0004\u0012\u00020\b`\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001c\u0010\n\u001a\u0010\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\f\u0018\u00010\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\r\u001a\u0010\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\f\u0018\u00010\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u000e\u001a\u0010\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\f\u0018\u00010\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u000f\u001a\u0010\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\f\u0018\u00010\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0010\u001a\u0010\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\f\u0018\u00010\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u00061"}, d2 = {"Lcom/manaknight/app/ui/adapter/SimpleChatAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "chatMessage", "Ljava/util/ArrayList;", "Lcom/manaknight/app/model/remote/SingleChatResponse;", "Lkotlin/collections/ArrayList;", "onChatImageClickListener", "Lkotlin/Function1;", "", "onChatUserImageClickListener", "onChatUsernameClickListener", "onChatVideoClickListener", "onMessageClickListener", "userId", "", "addData", "newList", "", "clearMessages", "getItemCount", "getItemViewType", "position", "onBindViewHolder", "holder", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "remove", "setOnChatImageClickListener", "listener", "setOnChatUserImageClickListener", "setOnChatUsernameClickListener", "setOnChatVideoClickListener", "setOnMessageClickListener", "setUserID", "userID", "Companion", "SimpleChatDiffUtil", "TypeImageReceive", "TypeImageSend", "TypeTextReceive", "TypeTextSend", "TypeVideoReceive", "TypeVideoSend", "app_debug"})
public final class SimpleChatAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<androidx.recyclerview.widget.RecyclerView.ViewHolder> {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private java.util.ArrayList<com.manaknight.app.model.remote.SingleChatResponse> chatMessage;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.SingleChatResponse, kotlin.Unit> onChatImageClickListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.SingleChatResponse, kotlin.Unit> onChatVideoClickListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.SingleChatResponse, kotlin.Unit> onChatUserImageClickListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.SingleChatResponse, kotlin.Unit> onChatUsernameClickListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.SingleChatResponse, kotlin.Unit> onMessageClickListener;
    private int userId = 0;
    public static final int TYPE_TEXT_RIGHT = 0;
    public static final int TYPE_TEXT_LEFT = 1;
    public static final int TYPE_IMAGE_RIGHT = 2;
    public static final int TYPE_IMAGE_LEFT = 3;
    public static final int TYPE_VIDEO_RIGHT = 4;
    public static final int TYPE_VIDEO_LEFT = 5;
    @org.jetbrains.annotations.NotNull()
    public static final com.manaknight.app.ui.adapter.SimpleChatAdapter.Companion Companion = null;
    
    public SimpleChatAdapter(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public androidx.recyclerview.widget.RecyclerView.ViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    androidx.recyclerview.widget.RecyclerView.ViewHolder holder, int position) {
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    @java.lang.Override()
    public int getItemViewType(int position) {
        return 0;
    }
    
    public final void setUserID(int userID) {
    }
    
    public final void setOnChatImageClickListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.SingleChatResponse, kotlin.Unit> listener) {
    }
    
    public final void setOnChatVideoClickListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.SingleChatResponse, kotlin.Unit> listener) {
    }
    
    public final void setOnChatUserImageClickListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.SingleChatResponse, kotlin.Unit> listener) {
    }
    
    public final void setOnChatUsernameClickListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.SingleChatResponse, kotlin.Unit> listener) {
    }
    
    public final void setOnMessageClickListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.SingleChatResponse, kotlin.Unit> listener) {
    }
    
    public final void addData(@org.jetbrains.annotations.NotNull()
    java.util.List<com.manaknight.app.model.remote.SingleChatResponse> newList) {
    }
    
    public final void addData(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.SingleChatResponse chatMessage) {
    }
    
    @android.annotation.SuppressLint(value = {"NotifyDataSetChanged"})
    public final void remove(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.SingleChatResponse chatMessage) {
    }
    
    @android.annotation.SuppressLint(value = {"NotifyDataSetChanged"})
    public final void remove(int position) {
    }
    
    @android.annotation.SuppressLint(value = {"NotifyDataSetChanged"})
    public final void clearMessages() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0006\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/manaknight/app/ui/adapter/SimpleChatAdapter$Companion;", "", "()V", "TYPE_IMAGE_LEFT", "", "TYPE_IMAGE_RIGHT", "TYPE_TEXT_LEFT", "TYPE_TEXT_RIGHT", "TYPE_VIDEO_LEFT", "TYPE_VIDEO_RIGHT", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0002\b\u0005\b\u0086\u0004\u0018\u00002\u00020\u0001B!\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\u0002\u0010\u0006J\u0018\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\nH\u0016J\u0018\u0010\f\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\nH\u0016J\b\u0010\r\u001a\u00020\nH\u0016J\b\u0010\u000e\u001a\u00020\nH\u0016R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/manaknight/app/ui/adapter/SimpleChatAdapter$SimpleChatDiffUtil;", "Landroidx/recyclerview/widget/DiffUtil$Callback;", "oldList", "", "Lcom/manaknight/app/model/remote/SingleChatResponse;", "newList", "(Lcom/manaknight/app/ui/adapter/SimpleChatAdapter;Ljava/util/List;Ljava/util/List;)V", "areContentsTheSame", "", "oldItemPosition", "", "newItemPosition", "areItemsTheSame", "getNewListSize", "getOldListSize", "app_debug"})
    public final class SimpleChatDiffUtil extends androidx.recyclerview.widget.DiffUtil.Callback {
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<com.manaknight.app.model.remote.SingleChatResponse> oldList = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<com.manaknight.app.model.remote.SingleChatResponse> newList = null;
        
        public SimpleChatDiffUtil(@org.jetbrains.annotations.NotNull()
        java.util.List<com.manaknight.app.model.remote.SingleChatResponse> oldList, @org.jetbrains.annotations.NotNull()
        java.util.List<com.manaknight.app.model.remote.SingleChatResponse> newList) {
            super();
        }
        
        @java.lang.Override()
        public int getOldListSize() {
            return 0;
        }
        
        @java.lang.Override()
        public int getNewListSize() {
            return 0;
        }
        
        @java.lang.Override()
        public boolean areItemsTheSame(int oldItemPosition, int newItemPosition) {
            return false;
        }
        
        @java.lang.Override()
        public boolean areContentsTheSame(int oldItemPosition, int newItemPosition) {
            return false;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0000\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Lcom/manaknight/app/ui/adapter/SimpleChatAdapter$TypeImageReceive;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "binding", "LManaknight/databinding/ReceiveImageMessageItemBinding;", "(Lcom/manaknight/app/ui/adapter/SimpleChatAdapter;LManaknight/databinding/ReceiveImageMessageItemBinding;)V", "bind", "", "position", "", "app_debug"})
    public final class TypeImageReceive extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final Manaknight.databinding.ReceiveImageMessageItemBinding binding = null;
        
        public TypeImageReceive(@org.jetbrains.annotations.NotNull()
        Manaknight.databinding.ReceiveImageMessageItemBinding binding) {
            super(null);
        }
        
        public final void bind(int position) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0000\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Lcom/manaknight/app/ui/adapter/SimpleChatAdapter$TypeImageSend;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "binding", "LManaknight/databinding/SendImageMessageItemBinding;", "(Lcom/manaknight/app/ui/adapter/SimpleChatAdapter;LManaknight/databinding/SendImageMessageItemBinding;)V", "bind", "", "position", "", "app_debug"})
    public final class TypeImageSend extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final Manaknight.databinding.SendImageMessageItemBinding binding = null;
        
        public TypeImageSend(@org.jetbrains.annotations.NotNull()
        Manaknight.databinding.SendImageMessageItemBinding binding) {
            super(null);
        }
        
        public final void bind(int position) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0000\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Lcom/manaknight/app/ui/adapter/SimpleChatAdapter$TypeTextReceive;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "binding", "LManaknight/databinding/ReceiveTextMessageItemBinding;", "(Lcom/manaknight/app/ui/adapter/SimpleChatAdapter;LManaknight/databinding/ReceiveTextMessageItemBinding;)V", "bind", "", "position", "", "app_debug"})
    public final class TypeTextReceive extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final Manaknight.databinding.ReceiveTextMessageItemBinding binding = null;
        
        public TypeTextReceive(@org.jetbrains.annotations.NotNull()
        Manaknight.databinding.ReceiveTextMessageItemBinding binding) {
            super(null);
        }
        
        public final void bind(int position) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0000\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Lcom/manaknight/app/ui/adapter/SimpleChatAdapter$TypeTextSend;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "binding", "LManaknight/databinding/SendTextMessageItemBinding;", "(Lcom/manaknight/app/ui/adapter/SimpleChatAdapter;LManaknight/databinding/SendTextMessageItemBinding;)V", "bind", "", "position", "", "app_debug"})
    public final class TypeTextSend extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final Manaknight.databinding.SendTextMessageItemBinding binding = null;
        
        public TypeTextSend(@org.jetbrains.annotations.NotNull()
        Manaknight.databinding.SendTextMessageItemBinding binding) {
            super(null);
        }
        
        public final void bind(int position) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0000\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Lcom/manaknight/app/ui/adapter/SimpleChatAdapter$TypeVideoReceive;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "binding", "LManaknight/databinding/ReceiveVideoMessageItemBinding;", "(Lcom/manaknight/app/ui/adapter/SimpleChatAdapter;LManaknight/databinding/ReceiveVideoMessageItemBinding;)V", "bind", "", "position", "", "app_debug"})
    public final class TypeVideoReceive extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final Manaknight.databinding.ReceiveVideoMessageItemBinding binding = null;
        
        public TypeVideoReceive(@org.jetbrains.annotations.NotNull()
        Manaknight.databinding.ReceiveVideoMessageItemBinding binding) {
            super(null);
        }
        
        public final void bind(int position) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0000\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Lcom/manaknight/app/ui/adapter/SimpleChatAdapter$TypeVideoSend;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "binding", "LManaknight/databinding/SendVideoMessageItemBinding;", "(Lcom/manaknight/app/ui/adapter/SimpleChatAdapter;LManaknight/databinding/SendVideoMessageItemBinding;)V", "bind", "", "position", "", "app_debug"})
    public final class TypeVideoSend extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final Manaknight.databinding.SendVideoMessageItemBinding binding = null;
        
        public TypeVideoSend(@org.jetbrains.annotations.NotNull()
        Manaknight.databinding.SendVideoMessageItemBinding binding) {
            super(null);
        }
        
        public final void bind(int position) {
        }
    }
}