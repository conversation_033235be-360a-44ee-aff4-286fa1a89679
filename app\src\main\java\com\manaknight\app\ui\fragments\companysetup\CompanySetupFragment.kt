
package com.manaknight.app.ui

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import Manaknight.R
import Manaknight.databinding.BottomAddEmployeeBinding
import Manaknight.databinding.FragmentCompanysetupBinding
import Manaknight.databinding.FragmentSubscriptionBinding
import android.app.Activity
import android.app.Dialog
import android.content.ContentValues.TAG
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.ViewGroup
import android.view.WindowManager
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.Toast
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.manaknight.app.adapter.AlertsAdapter
import com.manaknight.app.adapter.EmployeeAdapter
import com.manaknight.app.data.local.AppPreferences
import com.manaknight.app.extensions.hide
import com.manaknight.app.extensions.hideKeyboard
import com.manaknight.app.extensions.hideSoftKeyboard
import com.manaknight.app.extensions.setVerticalLayout
import com.manaknight.app.extensions.show
import com.manaknight.app.extensions.snackBar
import com.manaknight.app.extensions.viewBinding
import com.manaknight.app.model.remote.AlertModel
import com.manaknight.app.model.remote.EmplyeeModel
import com.manaknight.app.model.remote.profitPro.DefaultModel
import com.manaknight.app.network.Status
import com.manaknight.app.ui.fragments.AddEmployeeFragment
import com.manaknight.app.utils.ProgressDialog.Companion.progressDialog
import com.manaknight.app.viewmodels.BaasViewModel
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class CompanySetupFragment : Fragment(R.layout.fragment_companysetup)  {

    private val binding by viewBinding(FragmentCompanysetupBinding::bind)
    private val baasViewModel: BaasViewModel by viewModel()
    private val pref by inject<AppPreferences>()
    private val employeeAdapter by lazy { EmployeeAdapter(this::onEmployeeEditClick, this::onEmployeeDeleteClick) }
    private val employeeList: ArrayList<EmplyeeModel> = ArrayList()

    private var isShowFirst = true
    private var isShowSecond = true
    private lateinit var dialog: Dialog


    private fun onEmployeeEditClick(item: EmplyeeModel, position: Int) {
        Log.d(TAG, "onAlertClick: $position")
        showEditEmployee(item, position)

    }

    private fun onEmployeeDeleteClick(item: EmplyeeModel, position: Int) {
        Log.d(TAG, "onAlertClick: $position")
        employeeList.removeAt(position)
        employeeAdapter.refresh(employeeList)
    }

    override fun onPause() {
        super.onPause()
        pref.defaultProfitOverhead = binding.edTxtProfitOverhead.text.toString().toIntOrNull() ?: 1
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        // binding.icBack.setOnClickListener { findNavController().popBackStack() }
        dialog = progressDialog(requireContext())

        binding.employeeRecylerView.apply {
            setVerticalLayout()
            adapter = employeeAdapter
        }

        binding.headerInclude.backButton.setOnClickListener {
            findNavController().popBackStack(R.id.login, false)
        }

        binding.headerInclude.addPlaterTitle.text = "Company Setup"

        binding.infoButton1.setOnClickListener {
            requireActivity().hideSoftKeyboard()
            if (isShowFirst) {
                binding.linearDefaultHourlyRate3.show()
            }
        }

        binding.infoButton2.setOnClickListener {
            requireActivity().hideSoftKeyboard()
            if (isShowSecond) {
                binding.linearDefaultProfitOverhead3.show()
            }
        }

        binding.hide1.setOnClickListener {
            requireActivity().hideSoftKeyboard()
            binding.linearDefaultHourlyRate3.hide()
        }

        binding.hide2.setOnClickListener {
            requireActivity().hideSoftKeyboard()
            binding.linearDefaultProfitOverhead3.hide()
        }

        binding.checkBox1.setOnCheckedChangeListener { compoundButton, b ->
            requireActivity().hideSoftKeyboard()
            if (b == true) {
                isShowFirst = false
            } else {
                isShowFirst = true
            }
        }

        binding.checkBox2.setOnCheckedChangeListener { compoundButton, b ->
            requireActivity().hideSoftKeyboard()
            if (b == true) {
                isShowSecond = false
            } else {
                isShowSecond = true
            }
        }

        binding.btnContinue.setOnClickListener {
            requireActivity().hideSoftKeyboard()
            companySetup()
        }

        binding.btnAddEmployee.setOnClickListener {
            requireActivity().hideSoftKeyboard()
            showAddEmployee()
        }


        binding.edTxtProfitOverhead.setOnEditorActionListener { v, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_DONE) {
                // The "Done" button was pressed on the keyboard
                requireActivity().hideSoftKeyboard()
                binding.edTxtProfitOverhead.clearFocus()
                // Perform any other action, like submitting the data or moving to the next field
                true  // Return true to indicate that the event was handled
            } else if (actionId == EditorInfo.IME_ACTION_NEXT) {
                // The "Next" button was pressed on the keyboard (if you have multiple EditText fields)
                // Move the focus to the next EditText

                true
            } else {
                false
            }
        }


        if (employeeList.isEmpty()) {
            employeeList.add(EmplyeeModel(0, 0, "", 0, "", "", "", "Kapil Kumar", "30"))
        }
        setEmployeeList(employeeList)

        binding.line1.setOnClickListener() {
            requireActivity().hideSoftKeyboard()
        }

        binding.scrollable.setOnClickListener() {
            requireActivity().hideSoftKeyboard()
        }

        binding.employeeRecylerView.setOnClickListener() {
            requireActivity().hideSoftKeyboard()
        }
    }

    private fun setEmployeeList(message: List<EmplyeeModel>?) {
        employeeAdapter.refresh(employeeList)


    }



    private fun showAddEmployee() {

//        val addEmployeeFragment = AddEmployeeFragment()
//        addEmployeeFragment.show(requireActivity().supportFragmentManager, addEmployeeFragment.tag)


        val sheet = BottomSheetDialog(requireContext(), R.style.BottomSheetDialogTheme)
        val view = BottomAddEmployeeBinding.inflate(layoutInflater)

        sheet.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
        sheet.setOnShowListener { dialogInterface ->
            val bottomSheetDialog = dialogInterface as BottomSheetDialog
            val bottomSheet = bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)

            if (bottomSheet != null) {
                // Set height to full screen
                bottomSheet.layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT

                // Add a top margin of 100px
                val layoutParams = bottomSheet.layoutParams as ViewGroup.MarginLayoutParams
                layoutParams.topMargin = 0 // Adjust margin as needed
                layoutParams.bottomMargin = 0
                bottomSheet.layoutParams = layoutParams

                // Ensure the bottom sheet is expanded
                val behavior = BottomSheetBehavior.from(bottomSheet)
                behavior.state = BottomSheetBehavior.STATE_EXPANDED
                behavior.isFitToContents = false
                behavior.skipCollapsed = true
            }

//            bottomSheet?.layoutParams?.height = ViewGroup.LayoutParams.MATCH_PARENT
//
//            val behavior = BottomSheetBehavior.from(bottomSheet!!)
//            behavior.state = BottomSheetBehavior.STATE_EXPANDED
//            behavior.isFitToContents = false
//            behavior.skipCollapsed = true
        }
        view.apply {

            btnSave.isEnabled = false
            btnSave.alpha = 0.5f

            val textWatcher = object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    // Enable Save button if both fields are non-empty, otherwise disable it
                    btnSave.isEnabled = edTxtFullName.text.isNotEmpty() && edTxtHourlyRate.text.isNotEmpty()
                    btnSave.alpha = if (btnSave.isEnabled) 1f else 0.5f
                }

                override fun afterTextChanged(s: Editable?) {}
            }

            edTxtFullName.addTextChangedListener(textWatcher)
            edTxtHourlyRate.addTextChangedListener(textWatcher)

            backButton.setOnClickListener {
                sheet.dismiss()
            }
            btnSave.setOnClickListener {


                if(edTxtFullName.text.toString().isEmpty()) {
                    Toast.makeText(context, "Please enter full name", Toast.LENGTH_SHORT).show();
                } else if(edTxtHourlyRate.text.isEmpty()) {
                    Toast.makeText(context, "Please enter hourly rate", Toast.LENGTH_SHORT).show();
                } else {

                    employeeList.add(EmplyeeModel(0, 0, "", 0, "", "", "", edTxtFullName.text.toString(), edTxtHourlyRate.text.toString()))
                    setEmployeeList(employeeList)
                    sheet.dismiss()
                }
            }

            line1.setOnClickListener() {
                val imm = requireActivity().getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
                imm.hideSoftInputFromWindow(view.root.windowToken, 0)

            }


        }


        sheet.setOnDismissListener {
            requireActivity().hideSoftKeyboard()
        }

        sheet.setCancelable(true)
        sheet.setContentView(view.root)
        sheet.show()
    }

    private fun showEditEmployee(item: EmplyeeModel, index: Int) {
        val sheet = BottomSheetDialog(requireContext(), R.style.BottomSheetDialogTheme)

        val view = BottomAddEmployeeBinding.inflate(layoutInflater)
        sheet.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
        sheet.setOnShowListener { dialogInterface ->
            val bottomSheetDialog = dialogInterface as BottomSheetDialog
            val bottomSheet = bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)

            if (bottomSheet != null) {
                // Set height to full screen
                bottomSheet.layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT

                // Add a top margin of 100px
                val layoutParams = bottomSheet.layoutParams as ViewGroup.MarginLayoutParams
                layoutParams.topMargin = 0 // Adjust margin as needed
                layoutParams.bottomMargin = 0
                bottomSheet.layoutParams = layoutParams

                // Ensure the bottom sheet is expanded
                val behavior = BottomSheetBehavior.from(bottomSheet)
                behavior.state = BottomSheetBehavior.STATE_EXPANDED
                behavior.isFitToContents = false
                behavior.skipCollapsed = true
            }

//            bottomSheet?.layoutParams?.height = ViewGroup.LayoutParams.MATCH_PARENT
//
//            val behavior = BottomSheetBehavior.from(bottomSheet!!)
//            behavior.state = BottomSheetBehavior.STATE_EXPANDED
//            behavior.isFitToContents = false
//            behavior.skipCollapsed = true
        }
        view.apply {


            addHeading.text = "Edit Employee"
            edTxtFullName.setText(item.name)
            edTxtHourlyRate.setText(item.hourlyRate)

            val textWatcher = object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    // Enable Save button if both fields are non-empty, otherwise disable it
                    btnSave.isEnabled = edTxtFullName.text.isNotEmpty() && edTxtHourlyRate.text.isNotEmpty()
                    btnSave.alpha = if (btnSave.isEnabled) 1f else 0.5f
                }

                override fun afterTextChanged(s: Editable?) {}
            }

            edTxtFullName.addTextChangedListener(textWatcher)
            edTxtHourlyRate.addTextChangedListener(textWatcher)

            backButton.setOnClickListener {
                sheet.dismiss()
            }
            btnSave.setOnClickListener {


                if(edTxtFullName.text.toString().isEmpty()) {
                    Toast.makeText(context, "Please enter full name", Toast.LENGTH_SHORT).show();
                } else if(edTxtHourlyRate.text.isEmpty()) {
                    Toast.makeText(context, "Please enter hourly rate", Toast.LENGTH_SHORT).show();
                } else {


                    employeeList.removeAt(index)
                    employeeList.add(index, EmplyeeModel(0, 0, "", 0, "", "", "", edTxtFullName.text.toString(), edTxtHourlyRate.text.toString()))
                    setEmployeeList(employeeList)
                    sheet.dismiss()
                }
            }

            line1.setOnClickListener() {
                val imm = requireActivity().getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
                imm.hideSoftInputFromWindow(view.root.windowToken, 0)

            }


        }

        sheet.setOnDismissListener {
            requireActivity().hideSoftKeyboard()
        }

        sheet.setCancelable(true)
        sheet.setContentView(view.root)
        sheet.show()
    }

    override fun onResume() {
        super.onResume()
        binding.headerInclude.backButton.show()
        // (activity as AppCompatActivity?)!!.supportActionBar!!.hide()
    }

    override fun onStop() {
        super.onStop()
        // (activity as AppCompatActivity?)!!.supportActionBar!!.show()
    }

    private fun companySetup() {
        val hourlyRate = binding.edTxtHourlyRate.text.toString().toIntOrNull() ?: 0
        val profitOverhead = binding.edTxtProfitOverhead.text.toString().toIntOrNull() ?: 0

        baasViewModel.updateCompanyDefault(DefaultModel(hourlyRate, profitOverhead))
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> {
                        snackBar(it.message ?: getString(R.string.something_went_wrong))
                        dialog.hide()
                    }
                    Status.LOADING -> {
                        dialog.show()
                    }

                    Status.SUCCESS -> {
                        dialog.hide()
                        findNavController().navigate(R.id.action_companysetupFragment_to_materialsetupFragment)
                    }
                }
            }
    }
}
