// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.HorizontalScrollView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentHomeBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final LinearLayout activeCard;

  @NonNull
  public final ImageView addNewEstimation;

  @NonNull
  public final MaterialButton btnViewDetails;

  @NonNull
  public final LinearLayout completedCard;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final RelativeLayout container;

  @NonNull
  public final LinearLayout draftsCard;

  @NonNull
  public final LinearLayout firstRow;

  @NonNull
  public final HeaderBinding headerInclude;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final ConstraintLayout innerConstraintLayout;

  @NonNull
  public final LinearLayout outstandingCard;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final HorizontalScrollView scrollCompanyHealth;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final HorizontalScrollView scrollTotalProfitOverhead;

  @NonNull
  public final TextView txtActiveCount;

  @NonNull
  public final TextView txtCompanyHealth;

  @NonNull
  public final TextView txtCompletedCount;

  @NonNull
  public final TextView txtDraftsCount;

  @NonNull
  public final TextView txtLaborBalance;

  @NonNull
  public final TextView txtLaborSpent;

  @NonNull
  public final TextView txtMaterialBalance;

  @NonNull
  public final TextView txtMaterialSpent;

  @NonNull
  public final TextView txtOutstandingCount;

  @NonNull
  public final TextView txtTotalAR;

  @NonNull
  public final TextView txtTotalContracts;

  @NonNull
  public final TextView txtTotalProfitOverhead;

  private FragmentHomeBinding(@NonNull ConstraintLayout rootView, @NonNull LinearLayout activeCard,
      @NonNull ImageView addNewEstimation, @NonNull MaterialButton btnViewDetails,
      @NonNull LinearLayout completedCard, @Nullable RelativeLayout container,
      @NonNull LinearLayout draftsCard, @NonNull LinearLayout firstRow,
      @NonNull HeaderBinding headerInclude, @Nullable ConstraintLayout innerConstraintLayout,
      @NonNull LinearLayout outstandingCard, @Nullable HorizontalScrollView scrollCompanyHealth,
      @Nullable HorizontalScrollView scrollTotalProfitOverhead, @NonNull TextView txtActiveCount,
      @NonNull TextView txtCompanyHealth, @NonNull TextView txtCompletedCount,
      @NonNull TextView txtDraftsCount, @NonNull TextView txtLaborBalance,
      @NonNull TextView txtLaborSpent, @NonNull TextView txtMaterialBalance,
      @NonNull TextView txtMaterialSpent, @NonNull TextView txtOutstandingCount,
      @NonNull TextView txtTotalAR, @NonNull TextView txtTotalContracts,
      @NonNull TextView txtTotalProfitOverhead) {
    this.rootView = rootView;
    this.activeCard = activeCard;
    this.addNewEstimation = addNewEstimation;
    this.btnViewDetails = btnViewDetails;
    this.completedCard = completedCard;
    this.container = container;
    this.draftsCard = draftsCard;
    this.firstRow = firstRow;
    this.headerInclude = headerInclude;
    this.innerConstraintLayout = innerConstraintLayout;
    this.outstandingCard = outstandingCard;
    this.scrollCompanyHealth = scrollCompanyHealth;
    this.scrollTotalProfitOverhead = scrollTotalProfitOverhead;
    this.txtActiveCount = txtActiveCount;
    this.txtCompanyHealth = txtCompanyHealth;
    this.txtCompletedCount = txtCompletedCount;
    this.txtDraftsCount = txtDraftsCount;
    this.txtLaborBalance = txtLaborBalance;
    this.txtLaborSpent = txtLaborSpent;
    this.txtMaterialBalance = txtMaterialBalance;
    this.txtMaterialSpent = txtMaterialSpent;
    this.txtOutstandingCount = txtOutstandingCount;
    this.txtTotalAR = txtTotalAR;
    this.txtTotalContracts = txtTotalContracts;
    this.txtTotalProfitOverhead = txtTotalProfitOverhead;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentHomeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentHomeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_home, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentHomeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.activeCard;
      LinearLayout activeCard = ViewBindings.findChildViewById(rootView, id);
      if (activeCard == null) {
        break missingId;
      }

      id = R.id.addNewEstimation;
      ImageView addNewEstimation = ViewBindings.findChildViewById(rootView, id);
      if (addNewEstimation == null) {
        break missingId;
      }

      id = R.id.btnViewDetails;
      MaterialButton btnViewDetails = ViewBindings.findChildViewById(rootView, id);
      if (btnViewDetails == null) {
        break missingId;
      }

      id = R.id.completedCard;
      LinearLayout completedCard = ViewBindings.findChildViewById(rootView, id);
      if (completedCard == null) {
        break missingId;
      }

      id = R.id.container;
      RelativeLayout container = ViewBindings.findChildViewById(rootView, id);

      id = R.id.draftsCard;
      LinearLayout draftsCard = ViewBindings.findChildViewById(rootView, id);
      if (draftsCard == null) {
        break missingId;
      }

      id = R.id.firstRow;
      LinearLayout firstRow = ViewBindings.findChildViewById(rootView, id);
      if (firstRow == null) {
        break missingId;
      }

      id = R.id.headerInclude;
      View headerInclude = ViewBindings.findChildViewById(rootView, id);
      if (headerInclude == null) {
        break missingId;
      }
      HeaderBinding binding_headerInclude = HeaderBinding.bind(headerInclude);

      id = R.id.innerConstraintLayout;
      ConstraintLayout innerConstraintLayout = ViewBindings.findChildViewById(rootView, id);

      id = R.id.outstandingCard;
      LinearLayout outstandingCard = ViewBindings.findChildViewById(rootView, id);
      if (outstandingCard == null) {
        break missingId;
      }

      id = R.id.scrollCompanyHealth;
      HorizontalScrollView scrollCompanyHealth = ViewBindings.findChildViewById(rootView, id);

      id = R.id.scrollTotalProfitOverhead;
      HorizontalScrollView scrollTotalProfitOverhead = ViewBindings.findChildViewById(rootView, id);

      id = R.id.txtActiveCount;
      TextView txtActiveCount = ViewBindings.findChildViewById(rootView, id);
      if (txtActiveCount == null) {
        break missingId;
      }

      id = R.id.txtCompanyHealth;
      TextView txtCompanyHealth = ViewBindings.findChildViewById(rootView, id);
      if (txtCompanyHealth == null) {
        break missingId;
      }

      id = R.id.txtCompletedCount;
      TextView txtCompletedCount = ViewBindings.findChildViewById(rootView, id);
      if (txtCompletedCount == null) {
        break missingId;
      }

      id = R.id.txtDraftsCount;
      TextView txtDraftsCount = ViewBindings.findChildViewById(rootView, id);
      if (txtDraftsCount == null) {
        break missingId;
      }

      id = R.id.txtLaborBalance;
      TextView txtLaborBalance = ViewBindings.findChildViewById(rootView, id);
      if (txtLaborBalance == null) {
        break missingId;
      }

      id = R.id.txtLaborSpent;
      TextView txtLaborSpent = ViewBindings.findChildViewById(rootView, id);
      if (txtLaborSpent == null) {
        break missingId;
      }

      id = R.id.txtMaterialBalance;
      TextView txtMaterialBalance = ViewBindings.findChildViewById(rootView, id);
      if (txtMaterialBalance == null) {
        break missingId;
      }

      id = R.id.txtMaterialSpent;
      TextView txtMaterialSpent = ViewBindings.findChildViewById(rootView, id);
      if (txtMaterialSpent == null) {
        break missingId;
      }

      id = R.id.txtOutstandingCount;
      TextView txtOutstandingCount = ViewBindings.findChildViewById(rootView, id);
      if (txtOutstandingCount == null) {
        break missingId;
      }

      id = R.id.txtTotalAR;
      TextView txtTotalAR = ViewBindings.findChildViewById(rootView, id);
      if (txtTotalAR == null) {
        break missingId;
      }

      id = R.id.txtTotalContracts;
      TextView txtTotalContracts = ViewBindings.findChildViewById(rootView, id);
      if (txtTotalContracts == null) {
        break missingId;
      }

      id = R.id.txtTotalProfitOverhead;
      TextView txtTotalProfitOverhead = ViewBindings.findChildViewById(rootView, id);
      if (txtTotalProfitOverhead == null) {
        break missingId;
      }

      return new FragmentHomeBinding((ConstraintLayout) rootView, activeCard, addNewEstimation,
          btnViewDetails, completedCard, container, draftsCard, firstRow, binding_headerInclude,
          innerConstraintLayout, outstandingCard, scrollCompanyHealth, scrollTotalProfitOverhead,
          txtActiveCount, txtCompanyHealth, txtCompletedCount, txtDraftsCount, txtLaborBalance,
          txtLaborSpent, txtMaterialBalance, txtMaterialSpent, txtOutstandingCount, txtTotalAR,
          txtTotalContracts, txtTotalProfitOverhead);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
