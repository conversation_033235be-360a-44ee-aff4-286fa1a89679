// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentProjectviewBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final HeaderBinding headerInclude;

  @NonNull
  public final LinearLayout linearlayout95f48c05;

  private FragmentProjectviewBinding(@NonNull LinearLayout rootView,
      @NonNull HeaderBinding headerInclude, @NonNull LinearLayout linearlayout95f48c05) {
    this.rootView = rootView;
    this.headerInclude = headerInclude;
    this.linearlayout95f48c05 = linearlayout95f48c05;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentProjectviewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentProjectviewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_projectview, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentProjectviewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.headerInclude;
      View headerInclude = ViewBindings.findChildViewById(rootView, id);
      if (headerInclude == null) {
        break missingId;
      }
      HeaderBinding binding_headerInclude = HeaderBinding.bind(headerInclude);

      id = R.id.linearlayout_95f48c05;
      LinearLayout linearlayout95f48c05 = ViewBindings.findChildViewById(rootView, id);
      if (linearlayout95f48c05 == null) {
        break missingId;
      }

      return new FragmentProjectviewBinding((LinearLayout) rootView, binding_headerInclude,
          linearlayout95f48c05);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
