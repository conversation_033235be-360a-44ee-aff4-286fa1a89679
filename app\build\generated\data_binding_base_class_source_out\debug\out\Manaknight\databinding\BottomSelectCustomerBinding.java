// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class BottomSelectCustomerBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView addHeading;

  @NonNull
  public final ImageView backButton;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final MaterialButton btnSave;

  @NonNull
  public final MaterialButton createCustomer;

  @NonNull
  public final EditText edTxtMaterialName;

  @NonNull
  public final RecyclerView employeeRecylerView;

  @NonNull
  public final LinearLayout header;

  @NonNull
  public final RelativeLayout noCustomer;

  @NonNull
  public final TextView noMatch;

  private BottomSelectCustomerBinding(@NonNull LinearLayout rootView, @NonNull TextView addHeading,
      @NonNull ImageView backButton, @Nullable MaterialButton btnSave,
      @NonNull MaterialButton createCustomer, @NonNull EditText edTxtMaterialName,
      @NonNull RecyclerView employeeRecylerView, @NonNull LinearLayout header,
      @NonNull RelativeLayout noCustomer, @NonNull TextView noMatch) {
    this.rootView = rootView;
    this.addHeading = addHeading;
    this.backButton = backButton;
    this.btnSave = btnSave;
    this.createCustomer = createCustomer;
    this.edTxtMaterialName = edTxtMaterialName;
    this.employeeRecylerView = employeeRecylerView;
    this.header = header;
    this.noCustomer = noCustomer;
    this.noMatch = noMatch;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static BottomSelectCustomerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static BottomSelectCustomerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.bottom_select_customer, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static BottomSelectCustomerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addHeading;
      TextView addHeading = ViewBindings.findChildViewById(rootView, id);
      if (addHeading == null) {
        break missingId;
      }

      id = R.id.backButton;
      ImageView backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.btnSave;
      MaterialButton btnSave = ViewBindings.findChildViewById(rootView, id);

      id = R.id.createCustomer;
      MaterialButton createCustomer = ViewBindings.findChildViewById(rootView, id);
      if (createCustomer == null) {
        break missingId;
      }

      id = R.id.edTxtMaterialName;
      EditText edTxtMaterialName = ViewBindings.findChildViewById(rootView, id);
      if (edTxtMaterialName == null) {
        break missingId;
      }

      id = R.id.employeeRecylerView;
      RecyclerView employeeRecylerView = ViewBindings.findChildViewById(rootView, id);
      if (employeeRecylerView == null) {
        break missingId;
      }

      id = R.id.header;
      LinearLayout header = ViewBindings.findChildViewById(rootView, id);
      if (header == null) {
        break missingId;
      }

      id = R.id.noCustomer;
      RelativeLayout noCustomer = ViewBindings.findChildViewById(rootView, id);
      if (noCustomer == null) {
        break missingId;
      }

      id = R.id.noMatch;
      TextView noMatch = ViewBindings.findChildViewById(rootView, id);
      if (noMatch == null) {
        break missingId;
      }

      return new BottomSelectCustomerBinding((LinearLayout) rootView, addHeading, backButton,
          btnSave, createCustomer, edTxtMaterialName, employeeRecylerView, header, noCustomer,
          noMatch);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
