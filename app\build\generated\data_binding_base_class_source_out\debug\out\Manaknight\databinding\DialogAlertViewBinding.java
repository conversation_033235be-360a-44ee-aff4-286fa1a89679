// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatButton;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAlertViewBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final AppCompatButton buttonAlertDialogClose;

  @NonNull
  public final CardView dialogCardView;

  @NonNull
  public final ImageView imageViewAlertDialog;

  @NonNull
  public final TextView tvAlertDialogMsg;

  private DialogAlertViewBinding(@NonNull FrameLayout rootView,
      @NonNull AppCompatButton buttonAlertDialogClose, @NonNull CardView dialogCardView,
      @NonNull ImageView imageViewAlertDialog, @NonNull TextView tvAlertDialogMsg) {
    this.rootView = rootView;
    this.buttonAlertDialogClose = buttonAlertDialogClose;
    this.dialogCardView = dialogCardView;
    this.imageViewAlertDialog = imageViewAlertDialog;
    this.tvAlertDialogMsg = tvAlertDialogMsg;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAlertViewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAlertViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_alert_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAlertViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonAlertDialogClose;
      AppCompatButton buttonAlertDialogClose = ViewBindings.findChildViewById(rootView, id);
      if (buttonAlertDialogClose == null) {
        break missingId;
      }

      id = R.id.dialogCardView;
      CardView dialogCardView = ViewBindings.findChildViewById(rootView, id);
      if (dialogCardView == null) {
        break missingId;
      }

      id = R.id.imageViewAlertDialog;
      ImageView imageViewAlertDialog = ViewBindings.findChildViewById(rootView, id);
      if (imageViewAlertDialog == null) {
        break missingId;
      }

      id = R.id.tvAlertDialogMsg;
      TextView tvAlertDialogMsg = ViewBindings.findChildViewById(rootView, id);
      if (tvAlertDialogMsg == null) {
        break missingId;
      }

      return new DialogAlertViewBinding((FrameLayout) rootView, buttonAlertDialogClose,
          dialogCardView, imageViewAlertDialog, tvAlertDialogMsg);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
