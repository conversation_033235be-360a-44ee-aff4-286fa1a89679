
@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000N\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u001c\u0010\u0000\u001a\u00020\u00012\u0012\u0010\u0002\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a\u0010\u0010\u0005\u001a\u00020\u00012\u0006\u0010\u0006\u001a\u00020\u0007H\u0007\u001a\b\u0010\b\u001a\u00020\u0001H\u0007\u001a\u0012\u0010\t\u001a\u00020\u00012\b\u0010\n\u001a\u0004\u0018\u00010\u000bH\u0007\u001a \u0010\f\u001a\u00020\u00012\b\u0010\n\u001a\u0004\u0018\u00010\u000b2\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\u000eH\u0007\u001a\b\u0010\u000f\u001a\u00020\u0001H\u0007\u001a\u0010\u0010\u0010\u001a\u00020\u00012\u0006\u0010\u0011\u001a\u00020\u0004H\u0007\u001a\u001c\u0010\u0012\u001a\u00020\u00012\u0012\u0010\u0002\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a\u0016\u0010\u0013\u001a\u00020\u00012\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\u000eH\u0007\u001aV\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00182\u0012\u0010\u0019\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u00032\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001d2\u0018\u0010\u001e\u001a\u0014\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u001fH\u0007\u001a\b\u0010 \u001a\u00020\u0001H\u0007\u00a8\u0006!"}, d2 = {"BottomSheetContent", "", "onOptionClick", "Lkotlin/Function1;", "", "DrawItem", "draw", "Lcom/manaknight/app/model/remote/profitPro/DrawsRespModel;", "DrawItemPreview", "JobDetailsSheetContentBody", "jobDetails", "Lcom/manaknight/app/model/remote/profitPro/JobDetailsRespModel;", "JobDetailsSheetHeader", "onDismiss", "Lkotlin/Function0;", "JobDetailsSheetPreview", "PaymentStatusBadge", "status", "ProjectDetailsBottomSheetContentBody", "ProjectDetailsBottomSheetHeader", "ProjectDetailsScreen", "projectId", "", "baasViewModel", "Lcom/manaknight/app/viewmodels/BaasViewModel;", "onTrackingClick", "dialog", "Landroid/app/Dialog;", "navController", "Landroidx/navigation/NavController;", "onNavigateToLineItems", "Lkotlin/Function2;", "ProjectDetailsScreenPreview", "app_debug"})
public final class ProjectDetailsScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void ProjectDetailsScreen(int projectId, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.viewmodels.BaasViewModel baasViewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onTrackingClick, @org.jetbrains.annotations.NotNull()
    android.app.Dialog dialog, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Integer, ? super java.lang.String, kotlin.Unit> onNavigateToLineItems) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void BottomSheetContent(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onOptionClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ProjectDetailsBottomSheetHeader(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ProjectDetailsBottomSheetContentBody(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onOptionClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DrawItem(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.DrawsRespModel draw) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void PaymentStatusBadge(@org.jetbrains.annotations.NotNull()
    java.lang.String status) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void JobDetailsSheetHeader(@org.jetbrains.annotations.Nullable()
    com.manaknight.app.model.remote.profitPro.JobDetailsRespModel jobDetails, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void JobDetailsSheetContentBody(@org.jetbrains.annotations.Nullable()
    com.manaknight.app.model.remote.profitPro.JobDetailsRespModel jobDetails) {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(showBackground = true)
    @androidx.compose.runtime.Composable()
    public static final void ProjectDetailsScreenPreview() {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(showBackground = true)
    @androidx.compose.runtime.Composable()
    public static final void JobDetailsSheetPreview() {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(showBackground = true)
    @androidx.compose.runtime.Composable()
    public static final void DrawItemPreview() {
    }
}