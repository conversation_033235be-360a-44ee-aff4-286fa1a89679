// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentDashboardviewBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final HeaderBinding headerInclude;

  @NonNull
  public final TextView monthFilterButton;

  @NonNull
  public final RecyclerView recyclerViewProjects;

  @NonNull
  public final TextView statusFilterButton;

  @NonNull
  public final TextView txtDashboardCompanyHealth;

  @NonNull
  public final TextView txtDashboardLaborBalance;

  @NonNull
  public final TextView txtDashboardLaborSpent;

  @NonNull
  public final TextView txtDashboardMaterialBalance;

  @NonNull
  public final TextView txtDashboardMaterialSpent;

  @NonNull
  public final TextView txtDashboardTotalAR;

  @NonNull
  public final TextView txtDashboardTotalContracts;

  @NonNull
  public final TextView txtDashboardTotalProfitOverhead;

  private FragmentDashboardviewBinding(@NonNull ConstraintLayout rootView,
      @NonNull HeaderBinding headerInclude, @NonNull TextView monthFilterButton,
      @NonNull RecyclerView recyclerViewProjects, @NonNull TextView statusFilterButton,
      @NonNull TextView txtDashboardCompanyHealth, @NonNull TextView txtDashboardLaborBalance,
      @NonNull TextView txtDashboardLaborSpent, @NonNull TextView txtDashboardMaterialBalance,
      @NonNull TextView txtDashboardMaterialSpent, @NonNull TextView txtDashboardTotalAR,
      @NonNull TextView txtDashboardTotalContracts,
      @NonNull TextView txtDashboardTotalProfitOverhead) {
    this.rootView = rootView;
    this.headerInclude = headerInclude;
    this.monthFilterButton = monthFilterButton;
    this.recyclerViewProjects = recyclerViewProjects;
    this.statusFilterButton = statusFilterButton;
    this.txtDashboardCompanyHealth = txtDashboardCompanyHealth;
    this.txtDashboardLaborBalance = txtDashboardLaborBalance;
    this.txtDashboardLaborSpent = txtDashboardLaborSpent;
    this.txtDashboardMaterialBalance = txtDashboardMaterialBalance;
    this.txtDashboardMaterialSpent = txtDashboardMaterialSpent;
    this.txtDashboardTotalAR = txtDashboardTotalAR;
    this.txtDashboardTotalContracts = txtDashboardTotalContracts;
    this.txtDashboardTotalProfitOverhead = txtDashboardTotalProfitOverhead;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentDashboardviewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentDashboardviewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_dashboardview, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentDashboardviewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.headerInclude;
      View headerInclude = ViewBindings.findChildViewById(rootView, id);
      if (headerInclude == null) {
        break missingId;
      }
      HeaderBinding binding_headerInclude = HeaderBinding.bind(headerInclude);

      id = R.id.monthFilterButton;
      TextView monthFilterButton = ViewBindings.findChildViewById(rootView, id);
      if (monthFilterButton == null) {
        break missingId;
      }

      id = R.id.recyclerViewProjects;
      RecyclerView recyclerViewProjects = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewProjects == null) {
        break missingId;
      }

      id = R.id.statusFilterButton;
      TextView statusFilterButton = ViewBindings.findChildViewById(rootView, id);
      if (statusFilterButton == null) {
        break missingId;
      }

      id = R.id.txtDashboardCompanyHealth;
      TextView txtDashboardCompanyHealth = ViewBindings.findChildViewById(rootView, id);
      if (txtDashboardCompanyHealth == null) {
        break missingId;
      }

      id = R.id.txtDashboardLaborBalance;
      TextView txtDashboardLaborBalance = ViewBindings.findChildViewById(rootView, id);
      if (txtDashboardLaborBalance == null) {
        break missingId;
      }

      id = R.id.txtDashboardLaborSpent;
      TextView txtDashboardLaborSpent = ViewBindings.findChildViewById(rootView, id);
      if (txtDashboardLaborSpent == null) {
        break missingId;
      }

      id = R.id.txtDashboardMaterialBalance;
      TextView txtDashboardMaterialBalance = ViewBindings.findChildViewById(rootView, id);
      if (txtDashboardMaterialBalance == null) {
        break missingId;
      }

      id = R.id.txtDashboardMaterialSpent;
      TextView txtDashboardMaterialSpent = ViewBindings.findChildViewById(rootView, id);
      if (txtDashboardMaterialSpent == null) {
        break missingId;
      }

      id = R.id.txtDashboardTotalAR;
      TextView txtDashboardTotalAR = ViewBindings.findChildViewById(rootView, id);
      if (txtDashboardTotalAR == null) {
        break missingId;
      }

      id = R.id.txtDashboardTotalContracts;
      TextView txtDashboardTotalContracts = ViewBindings.findChildViewById(rootView, id);
      if (txtDashboardTotalContracts == null) {
        break missingId;
      }

      id = R.id.txtDashboardTotalProfitOverhead;
      TextView txtDashboardTotalProfitOverhead = ViewBindings.findChildViewById(rootView, id);
      if (txtDashboardTotalProfitOverhead == null) {
        break missingId;
      }

      return new FragmentDashboardviewBinding((ConstraintLayout) rootView, binding_headerInclude,
          monthFilterButton, recyclerViewProjects, statusFilterButton, txtDashboardCompanyHealth,
          txtDashboardLaborBalance, txtDashboardLaborSpent, txtDashboardMaterialBalance,
          txtDashboardMaterialSpent, txtDashboardTotalAR, txtDashboardTotalContracts,
          txtDashboardTotalProfitOverhead);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
