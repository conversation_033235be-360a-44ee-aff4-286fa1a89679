// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class BottomEditProfileBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView addHeading;

  @NonNull
  public final ImageView backButton;

  @NonNull
  public final MaterialButton btnSave;

  @NonNull
  public final EditText edTxtCompanyName;

  @NonNull
  public final EditText edTxtEmail;

  @NonNull
  public final EditText edTxtFirstName;

  @NonNull
  public final EditText edTxtLastName;

  @NonNull
  public final LinearLayout header;

  @NonNull
  public final LinearLayout line1;

  @NonNull
  public final ScrollView mainLayout;

  private BottomEditProfileBinding(@NonNull LinearLayout rootView, @NonNull TextView addHeading,
      @NonNull ImageView backButton, @NonNull MaterialButton btnSave,
      @NonNull EditText edTxtCompanyName, @NonNull EditText edTxtEmail,
      @NonNull EditText edTxtFirstName, @NonNull EditText edTxtLastName,
      @NonNull LinearLayout header, @NonNull LinearLayout line1, @NonNull ScrollView mainLayout) {
    this.rootView = rootView;
    this.addHeading = addHeading;
    this.backButton = backButton;
    this.btnSave = btnSave;
    this.edTxtCompanyName = edTxtCompanyName;
    this.edTxtEmail = edTxtEmail;
    this.edTxtFirstName = edTxtFirstName;
    this.edTxtLastName = edTxtLastName;
    this.header = header;
    this.line1 = line1;
    this.mainLayout = mainLayout;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static BottomEditProfileBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static BottomEditProfileBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.bottom_edit_profile, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static BottomEditProfileBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addHeading;
      TextView addHeading = ViewBindings.findChildViewById(rootView, id);
      if (addHeading == null) {
        break missingId;
      }

      id = R.id.backButton;
      ImageView backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.btnSave;
      MaterialButton btnSave = ViewBindings.findChildViewById(rootView, id);
      if (btnSave == null) {
        break missingId;
      }

      id = R.id.edTxtCompanyName;
      EditText edTxtCompanyName = ViewBindings.findChildViewById(rootView, id);
      if (edTxtCompanyName == null) {
        break missingId;
      }

      id = R.id.edTxtEmail;
      EditText edTxtEmail = ViewBindings.findChildViewById(rootView, id);
      if (edTxtEmail == null) {
        break missingId;
      }

      id = R.id.edTxtFirstName;
      EditText edTxtFirstName = ViewBindings.findChildViewById(rootView, id);
      if (edTxtFirstName == null) {
        break missingId;
      }

      id = R.id.edTxtLastName;
      EditText edTxtLastName = ViewBindings.findChildViewById(rootView, id);
      if (edTxtLastName == null) {
        break missingId;
      }

      id = R.id.header;
      LinearLayout header = ViewBindings.findChildViewById(rootView, id);
      if (header == null) {
        break missingId;
      }

      id = R.id.line1;
      LinearLayout line1 = ViewBindings.findChildViewById(rootView, id);
      if (line1 == null) {
        break missingId;
      }

      id = R.id.mainLayout;
      ScrollView mainLayout = ViewBindings.findChildViewById(rootView, id);
      if (mainLayout == null) {
        break missingId;
      }

      return new BottomEditProfileBinding((LinearLayout) rootView, addHeading, backButton, btnSave,
          edTxtCompanyName, edTxtEmail, edTxtFirstName, edTxtLastName, header, line1, mainLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
