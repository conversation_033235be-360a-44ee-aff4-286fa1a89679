package com.manaknight.app.model.remote.profitPro;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B)\u0012\u000e\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003\u0012\b\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\b\u0010\u0007\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0002\u0010\bJ\u0011\u0010\u000e\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u000f\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010\u0010\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J3\u0010\u0011\u001a\u00020\u00002\u0010\b\u0002\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0006H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0006H\u00d6\u0001R\u0019\u0010\u0002\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0018\u0010\u0007\u001a\u0004\u0018\u00010\u00068\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0018\u0010\u0005\u001a\u0004\u0018\u00010\u00068\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\f\u00a8\u0006\u0018"}, d2 = {"Lcom/manaknight/app/model/remote/profitPro/Draws;", "", "list", "", "Lcom/manaknight/app/model/remote/profitPro/DrawItem;", "unaccountedAmount", "", "salePrice", "(Ljava/util/List;Ljava/lang/String;Ljava/lang/String;)V", "getList", "()Ljava/util/List;", "getSalePrice", "()Ljava/lang/String;", "getUnaccountedAmount", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
public final class Draws {
    @org.jetbrains.annotations.Nullable()
    private final java.util.List<com.manaknight.app.model.remote.profitPro.DrawItem> list = null;
    @com.google.gson.annotations.SerializedName(value = "unaccounted_amount")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String unaccountedAmount = null;
    @com.google.gson.annotations.SerializedName(value = "sale_price")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String salePrice = null;
    
    public Draws(@org.jetbrains.annotations.Nullable()
    java.util.List<com.manaknight.app.model.remote.profitPro.DrawItem> list, @org.jetbrains.annotations.Nullable()
    java.lang.String unaccountedAmount, @org.jetbrains.annotations.Nullable()
    java.lang.String salePrice) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.manaknight.app.model.remote.profitPro.DrawItem> getList() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getUnaccountedAmount() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSalePrice() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.manaknight.app.model.remote.profitPro.DrawItem> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.manaknight.app.model.remote.profitPro.Draws copy(@org.jetbrains.annotations.Nullable()
    java.util.List<com.manaknight.app.model.remote.profitPro.DrawItem> list, @org.jetbrains.annotations.Nullable()
    java.lang.String unaccountedAmount, @org.jetbrains.annotations.Nullable()
    java.lang.String salePrice) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}