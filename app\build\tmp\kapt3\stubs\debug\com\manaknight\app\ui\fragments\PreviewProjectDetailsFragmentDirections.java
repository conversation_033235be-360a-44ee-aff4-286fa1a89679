package com.manaknight.app.ui.fragments;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0005\u0018\u0000 \u00052\u00020\u0001:\u0003\u0003\u0004\u0005B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0006"}, d2 = {"Lcom/manaknight/app/ui/fragments/PreviewProjectDetailsFragmentDirections;", "", "()V", "ActionPreviewProjectDetailsFragmentToInvoiceFragment", "ActionPreviewProjectDetailsFragmentToLineItemView", "Companion", "app_debug"})
public final class PreviewProjectDetailsFragmentDirections {
    @org.jetbrains.annotations.NotNull()
    public static final com.manaknight.app.ui.fragments.PreviewProjectDetailsFragmentDirections.Companion Companion = null;
    
    private PreviewProjectDetailsFragmentDirections() {
        super();
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0082\b\u0018\u00002\u00020\u0001B\u000f\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\u000e\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0012H\u00d6\u0003J\t\u0010\u0013\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001R\u0014\u0010\u0005\u001a\u00020\u0003X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0014\u0010\b\u001a\u00020\t8VX\u0096\u0004\u00a2\u0006\u0006\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u0007\u00a8\u0006\u0016"}, d2 = {"Lcom/manaknight/app/ui/fragments/PreviewProjectDetailsFragmentDirections$ActionPreviewProjectDetailsFragmentToInvoiceFragment;", "Landroidx/navigation/NavDirections;", "projectID", "", "(I)V", "actionId", "getActionId", "()I", "arguments", "Landroid/os/Bundle;", "getArguments", "()Landroid/os/Bundle;", "getProjectID", "component1", "copy", "equals", "", "other", "", "hashCode", "toString", "", "app_debug"})
    static final class ActionPreviewProjectDetailsFragmentToInvoiceFragment implements androidx.navigation.NavDirections {
        private final int projectID = 0;
        private final int actionId = 0;
        
        public ActionPreviewProjectDetailsFragmentToInvoiceFragment(int projectID) {
            super();
        }
        
        public final int getProjectID() {
            return 0;
        }
        
        @java.lang.Override()
        public int getActionId() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public android.os.Bundle getArguments() {
            return null;
        }
        
        public ActionPreviewProjectDetailsFragmentToInvoiceFragment() {
            super();
        }
        
        public final int component1() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.manaknight.app.ui.fragments.PreviewProjectDetailsFragmentDirections.ActionPreviewProjectDetailsFragmentToInvoiceFragment copy(int projectID) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0003\b\u0082\b\u0018\u00002\u00020\u0001B\u0019\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0005H\u00c6\u0003J\u001d\u0010\u0013\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u0014\u001a\u00020\u00152\b\u0010\u0016\u001a\u0004\u0018\u00010\u0017H\u00d6\u0003J\t\u0010\u0018\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u0019\u001a\u00020\u0005H\u00d6\u0001R\u0014\u0010\u0007\u001a\u00020\u0003X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0014\u0010\n\u001a\u00020\u000b8VX\u0096\u0004\u00a2\u0006\u0006\u001a\u0004\b\f\u0010\rR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\t\u00a8\u0006\u001a"}, d2 = {"Lcom/manaknight/app/ui/fragments/PreviewProjectDetailsFragmentDirections$ActionPreviewProjectDetailsFragmentToLineItemView;", "Landroidx/navigation/NavDirections;", "projectID", "", "customerName", "", "(ILjava/lang/String;)V", "actionId", "getActionId", "()I", "arguments", "Landroid/os/Bundle;", "getArguments", "()Landroid/os/Bundle;", "getCustomerName", "()Ljava/lang/String;", "getProjectID", "component1", "component2", "copy", "equals", "", "other", "", "hashCode", "toString", "app_debug"})
    static final class ActionPreviewProjectDetailsFragmentToLineItemView implements androidx.navigation.NavDirections {
        private final int projectID = 0;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String customerName = null;
        private final int actionId = 0;
        
        public ActionPreviewProjectDetailsFragmentToLineItemView(int projectID, @org.jetbrains.annotations.NotNull()
        java.lang.String customerName) {
            super();
        }
        
        public final int getProjectID() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getCustomerName() {
            return null;
        }
        
        @java.lang.Override()
        public int getActionId() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public android.os.Bundle getArguments() {
            return null;
        }
        
        public ActionPreviewProjectDetailsFragmentToLineItemView() {
            super();
        }
        
        public final int component1() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.manaknight.app.ui.fragments.PreviewProjectDetailsFragmentDirections.ActionPreviewProjectDetailsFragmentToLineItemView copy(int projectID, @org.jetbrains.annotations.NotNull()
        java.lang.String customerName) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\b\b\u0002\u0010\u0005\u001a\u00020\u0006J\u001a\u0010\u0007\u001a\u00020\u00042\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\b\u001a\u00020\t\u00a8\u0006\n"}, d2 = {"Lcom/manaknight/app/ui/fragments/PreviewProjectDetailsFragmentDirections$Companion;", "", "()V", "actionPreviewProjectDetailsFragmentToInvoiceFragment", "Landroidx/navigation/NavDirections;", "projectID", "", "actionPreviewProjectDetailsFragmentToLineItemView", "customerName", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.navigation.NavDirections actionPreviewProjectDetailsFragmentToInvoiceFragment(int projectID) {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.navigation.NavDirections actionPreviewProjectDetailsFragmentToLineItemView(int projectID, @org.jetbrains.annotations.NotNull()
        java.lang.String customerName) {
            return null;
        }
    }
}