1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.manaknight.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="28"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
11-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:6:5-76
11-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:6:22-74
12    <uses-permission android:name="android.permission.VIBRATE" />
12-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:7:5-66
12-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:7:22-63
13    <uses-permission android:name="android.permission.INTERNET" />
13-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:8:5-67
13-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:8:22-64
14    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
14-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:9:5-79
14-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:9:22-76
15    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
15-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:10:5-79
15-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:10:22-76
16    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
16-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:11:5-81
16-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:11:22-78
17    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
17-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:12:5-76
17-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:12:22-73
18    <uses-permission android:name="android.permission.CAMERA" />
18-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:13:5-65
18-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:13:22-62
19    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
19-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:14:5-80
19-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:14:22-78
20    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" />
20-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:15:5-79
20-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:15:22-76
21    <uses-permission android:name="android.permission.WAKE_LOCK" />
21-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:16:5-68
21-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:16:22-65
22    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" />
22-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:15:5-79
22-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:15:22-76
23    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
23-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:18:5-81
23-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:18:22-78
24    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
24-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:19:5-80
24-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:19:22-77
25    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
25-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:20:5-81
25-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:20:22-79
26    <uses-permission android:name="com.android.vending.BILLING" />
26-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:21:5-67
26-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:21:22-64
27
28    <!-- Biometric Authentication (Face ID) Permissions -->
29    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
29-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:24:5-72
29-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:24:22-69
30    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
30-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:25:5-74
30-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:25:22-71
31
32    <queries>
32-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:30:5-32:15
33        <package android:name="com.facebook.katana" />
33-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:31:9-55
33-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:31:18-52
34        <!-- Needs to be explicitly declared on Android R+ -->
35        <package android:name="com.google.android.apps.maps" />
35-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:33:9-64
35-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:33:18-61
36
37        <intent>
37-->[androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:23:9-25:18
38            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
38-->[androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:24:13-86
38-->[androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:24:21-83
39        </intent>
40        <intent>
40-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:13:9-15:18
41            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
41-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:14:13-91
41-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:14:21-88
42        </intent>
43    </queries>
44
45    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
45-->[com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:11:5-76
45-->[com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:11:22-73
46
47    <uses-feature
47-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:26:5-28:35
48        android:glEsVersion="0x00020000"
48-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:27:9-41
49        android:required="true" /> <!-- Required by older versions of Google Play services to create IID tokens -->
49-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:28:9-32
50    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
50-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:28:5-82
50-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:28:22-79
51    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
51-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:25:5-79
51-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:25:22-76
52    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
52-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:26:5-110
52-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:26:22-107
53
54    <permission
54-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
55        android:name="com.manaknight.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
55-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
56        android:protectionLevel="signature" />
56-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
57
58    <uses-permission android:name="com.manaknight.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
58-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
58-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
59
60    <application
60-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:34:5-88:19
61        android:name="com.manaknight.app.App"
61-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:35:9-46
62        android:allowBackup="false"
62-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:36:9-36
63        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
63-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b7543783d7e597efaf2f46cf5f3847\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
64        android:dataExtractionRules="@xml/data_extraction_rules"
64-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:37:9-65
65        android:debuggable="true"
66        android:extractNativeLibs="false"
67        android:fullBackupContent="@xml/backup_rules"
67-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:38:9-54
68        android:icon="@mipmap/ic_launcher"
68-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:39:9-43
69        android:label="@string/app_name"
69-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:40:9-41
70        android:requestLegacyExternalStorage="true"
70-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:43:9-52
71        android:roundIcon="@mipmap/ic_launcher_round"
71-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:41:9-54
72        android:supportsRtl="true"
72-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:42:9-35
73        android:testOnly="true"
74        android:theme="@style/AppTheme"
74-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:44:9-40
75        android:usesCleartextTraffic="true" >
75-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:45:9-44
76        <activity
76-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:47:9-64:20
77            android:name="com.manaknight.app.MainActivity"
77-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:48:13-59
78            android:exported="true"
78-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:49:13-36
79            android:hardwareAccelerated="true"
79-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:53:13-47
80            android:launchMode="singleTask"
80-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:54:13-44
81            android:screenOrientation="portrait"
81-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:50:13-49
82            android:windowSoftInputMode="adjustResize" >
82-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:52:13-55
83            <intent-filter>
83-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:55:13-59:29
84                <action android:name="android.intent.action.MAIN" />
84-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:56:17-69
84-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:56:25-66
85
86                <category android:name="android.intent.category.LAUNCHER" />
86-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:58:17-77
86-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:58:27-74
87            </intent-filter>
88
89            <meta-data
89-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:61:13-63:36
90                android:name="android.app.lib_name"
90-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:62:17-52
91                android:value="" />
91-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:63:17-33
92        </activity>
93
94        <meta-data
94-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:66:9-68:58
95            android:name="com.google.firebase.messaging.default_notification_icon"
95-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:67:13-83
96            android:resource="@drawable/ic_loc_active" />
96-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:68:13-55
97
98        <!-- <provider -->
99        <!-- android:name="androidx.core.content.FileProvider" -->
100        <!-- android:authorities="com.manaknight.app.provider" -->
101        <!-- android:exported="false" -->
102        <!-- android:grantUriPermissions="true"> -->
103        <!-- <meta-data -->
104        <!-- android:name="android.support.FILE_PROVIDER_PATHS" -->
105        <!-- android:resource="@xml/file_provider" /> -->
106        <!-- </provider> -->
107
108        <service
108-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:80:9-87:19
109            android:name="com.manaknight.app.fcm.MyFirebasePushNotifications"
109-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:81:13-78
110            android:exported="false" >
110-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:82:13-37
111            <intent-filter>
111-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:84:13-86:29
112                <action android:name="com.google.firebase.MESSAGING_EVENT" />
112-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:85:17-78
112-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:85:25-75
113            </intent-filter>
114        </service>
115
116        <activity
116-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:12:9-14:57
117            android:name="com.stripe.android.view.AddPaymentMethodActivity"
117-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:13:13-76
118            android:theme="@style/StripeDefaultTheme" />
118-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:14:13-54
119        <activity
119-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:15:9-17:57
120            android:name="com.stripe.android.view.PaymentMethodsActivity"
120-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:16:13-74
121            android:theme="@style/StripeDefaultTheme" />
121-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:17:13-54
122        <activity
122-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:18:9-20:57
123            android:name="com.stripe.android.view.PaymentFlowActivity"
123-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:19:13-71
124            android:theme="@style/StripeDefaultTheme" />
124-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:20:13-54
125        <activity
125-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:21:9-23:57
126            android:name="com.stripe.android.view.PaymentAuthWebViewActivity"
126-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:22:13-78
127            android:theme="@style/StripeDefaultTheme" />
127-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:23:13-54
128        <activity
128-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:24:9-26:57
129            android:name="com.stripe.android.view.PaymentRelayActivity"
129-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:25:13-72
130            android:theme="@style/StripeDefaultTheme" />
130-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:26:13-54
131        <activity
131-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:27:9-29:57
132            android:name="com.stripe.android.view.Stripe3ds2CompletionActivity"
132-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:28:13-80
133            android:theme="@style/StripeDefaultTheme" />
133-->[com.stripe:stripe-android:14.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eee9885f49b6b7ddb37dd5867cc2ddbf\transformed\jetified-stripe-android-14.2.1\AndroidManifest.xml:29:13-54
134        <activity
134-->[com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:12:9-14:54
135            android:name="com.stripe.android.stripe3ds2.views.ChallengeActivity"
135-->[com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:13:13-81
136            android:theme="@style/Stripe3DS2Theme" />
136-->[com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:14:13-51
137        <activity
137-->[com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:15:9-17:54
138            android:name="com.stripe.android.stripe3ds2.views.ChallengeProgressDialogActivity"
138-->[com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:16:13-95
139            android:theme="@style/Stripe3DS2Theme" />
139-->[com.stripe:stripe-3ds2-android:2.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\7f344a36bb06c1b19b3d8dc344e8ef3c\transformed\jetified-stripe-3ds2-android-2.7.2\AndroidManifest.xml:17:13-51
140        <activity
140-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\dfce37622a563a24f0c8e9b3351fbc6a\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:27:9-29:72
141            android:name="com.karumi.dexter.DexterActivity"
141-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\dfce37622a563a24f0c8e9b3351fbc6a\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:28:13-60
142            android:theme="@style/Dexter.Internal.Theme.Transparent" />
142-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\dfce37622a563a24f0c8e9b3351fbc6a\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:29:13-69
143        <activity
143-->[com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:15:9-21:20
144            android:name="com.google.android.libraries.places.widget.AutocompleteActivity"
144-->[com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:16:13-91
145            android:exported="false"
145-->[com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:17:13-37
146            android:label="@string/places_autocomplete_label"
146-->[com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:18:13-62
147            android:theme="@style/PlacesAutocompleteOverlay"
147-->[com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:19:13-61
148            android:windowSoftInputMode="adjustResize" >
148-->[com.google.android.libraries.places:places:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\245b102fe60f0031ab708afcac9ce0b9\transformed\jetified-places-3.0.0\AndroidManifest.xml:20:13-55
149        </activity>
150        <activity
150-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:23:9-27:75
151            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
151-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:24:13-93
152            android:excludeFromRecents="true"
152-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:25:13-46
153            android:exported="false"
153-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:26:13-37
154            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
154-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:27:13-72
155        <!--
156            Service handling Google Sign-In user revocation. For apps that do not integrate with
157            Google Sign-In, this service will never be started.
158        -->
159        <service
159-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:33:9-37:51
160            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
160-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:34:13-89
161            android:exported="true"
161-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:35:13-36
162            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
162-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:36:13-107
163            android:visibleToInstantApps="true" /> <!-- Needs to be explicitly declared on P+ -->
163-->[com.google.android.gms:play-services-auth:20.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f6fa1e240580d875b9faf8d016b434a\transformed\jetified-play-services-auth-20.4.1\AndroidManifest.xml:37:13-48
164        <uses-library
164-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:39:9-41:40
165            android:name="org.apache.http.legacy"
165-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:40:13-50
166            android:required="false" />
166-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca5bb2c25cf61ac91ecd47130eaf591\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:41:13-37
167
168        <meta-data
168-->[com.google.android.gms:play-services-fitness:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4018ae0a1966300199c2b3e05a6d81e9\transformed\jetified-play-services-fitness-20.0.0\AndroidManifest.xml:23:9-25:38
169            android:name="com.google.gms.fitness.sdk.version"
169-->[com.google.android.gms:play-services-fitness:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4018ae0a1966300199c2b3e05a6d81e9\transformed\jetified-play-services-fitness-20.0.0\AndroidManifest.xml:24:13-62
170            android:value="20.0.0" />
170-->[com.google.android.gms:play-services-fitness:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4018ae0a1966300199c2b3e05a6d81e9\transformed\jetified-play-services-fitness-20.0.0\AndroidManifest.xml:25:13-35
171
172        <service
172-->[com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:9:9-15:19
173            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
173-->[com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:10:13-91
174            android:directBootAware="true"
174-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:17:13-43
175            android:exported="false" >
175-->[com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:11:13-37
176            <meta-data
176-->[com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:12:13-14:85
177                android:name="com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar"
177-->[com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:13:17-114
178                android:value="com.google.firebase.components.ComponentRegistrar" />
178-->[com.google.android.gms:play-services-mlkit-text-recognition-common:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b4bd5f6b063655bfefc8564cd709cc40\transformed\jetified-play-services-mlkit-text-recognition-common-18.0.0\AndroidManifest.xml:14:17-82
179            <meta-data
179-->[com.google.mlkit:vision-common:17.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c3691d46c450c8405e6a322b681b222a\transformed\jetified-vision-common-17.2.1\AndroidManifest.xml:12:13-14:85
180                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
180-->[com.google.mlkit:vision-common:17.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c3691d46c450c8405e6a322b681b222a\transformed\jetified-vision-common-17.2.1\AndroidManifest.xml:13:17-124
181                android:value="com.google.firebase.components.ComponentRegistrar" />
181-->[com.google.mlkit:vision-common:17.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c3691d46c450c8405e6a322b681b222a\transformed\jetified-vision-common-17.2.1\AndroidManifest.xml:14:17-82
182            <meta-data
182-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:20:13-22:85
183                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
183-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:21:17-120
184                android:value="com.google.firebase.components.ComponentRegistrar" />
184-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:22:17-82
185        </service>
186
187        <provider
187-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:9:9-13:38
188            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
188-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:10:13-78
189            android:authorities="com.manaknight.app.mlkitinitprovider"
189-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:11:13-69
190            android:exported="false"
190-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:12:13-37
191            android:initOrder="99" />
191-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c505a1ac40223f3f4347c0c22e87fce\transformed\jetified-common-18.5.0\AndroidManifest.xml:13:13-35
192
193        <service
193-->[com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:8:9-14:19
194            android:name="com.google.firebase.components.ComponentDiscoveryService"
194-->[com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:9:13-84
195            android:directBootAware="true"
195-->[com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:34:13-43
196            android:exported="false" >
196-->[com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:10:13-37
197            <meta-data
197-->[com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:11:13-13:85
198                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthKtxRegistrar"
198-->[com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:12:17-116
199                android:value="com.google.firebase.components.ComponentRegistrar" />
199-->[com.google.firebase:firebase-auth-ktx:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\15d4f429e740d2d9a84daaaefe71c783\transformed\jetified-firebase-auth-ktx-21.1.0\AndroidManifest.xml:13:17-82
200            <meta-data
200-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:67:13-69:85
201                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
201-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:68:17-109
202                android:value="com.google.firebase.components.ComponentRegistrar" />
202-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:69:17-82
203            <meta-data
203-->[com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c80935087eb2baf2ce8d5fda4c1695fb\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:28:13-30:85
204                android:name="com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingKtxRegistrar"
204-->[com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c80935087eb2baf2ce8d5fda4c1695fb\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:29:17-126
205                android:value="com.google.firebase.components.ComponentRegistrar" />
205-->[com.google.firebase:firebase-messaging-ktx:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c80935087eb2baf2ce8d5fda4c1695fb\transformed\jetified-firebase-messaging-ktx-23.1.1\AndroidManifest.xml:30:17-82
206            <meta-data
206-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:55:13-57:85
207                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
207-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:56:17-119
208                android:value="com.google.firebase.components.ComponentRegistrar" />
208-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:57:17-82
209            <meta-data
209-->[com.google.firebase:firebase-crashlytics-ktx:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\7a08c1f471d4aed10f5a62d4bdfd7cc0\transformed\jetified-firebase-crashlytics-ktx-18.3.5\AndroidManifest.xml:26:13-28:85
210                android:name="com.google.firebase.components:com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar"
210-->[com.google.firebase:firebase-crashlytics-ktx:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\7a08c1f471d4aed10f5a62d4bdfd7cc0\transformed\jetified-firebase-crashlytics-ktx-18.3.5\AndroidManifest.xml:27:17-130
211                android:value="com.google.firebase.components.ComponentRegistrar" />
211-->[com.google.firebase:firebase-crashlytics-ktx:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\7a08c1f471d4aed10f5a62d4bdfd7cc0\transformed\jetified-firebase-crashlytics-ktx-18.3.5\AndroidManifest.xml:28:17-82
212            <meta-data
212-->[com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d0398be7ec17550451ee5b14d880cfc\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:11:13-13:85
213                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsKtxRegistrar"
213-->[com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d0398be7ec17550451ee5b14d880cfc\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:12:17-126
214                android:value="com.google.firebase.components.ComponentRegistrar" />
214-->[com.google.firebase:firebase-analytics-ktx:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d0398be7ec17550451ee5b14d880cfc\transformed\jetified-firebase-analytics-ktx-21.2.0\AndroidManifest.xml:13:17-82
215            <meta-data
215-->[com.google.firebase:firebase-common-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b2a903c9b0acab1e7698691d1a2c986\transformed\jetified-firebase-common-ktx-20.3.0\AndroidManifest.xml:14:13-16:85
216                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonKtxRegistrar"
216-->[com.google.firebase:firebase-common-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b2a903c9b0acab1e7698691d1a2c986\transformed\jetified-firebase-common-ktx-20.3.0\AndroidManifest.xml:15:17-113
217                android:value="com.google.firebase.components.ComponentRegistrar" />
217-->[com.google.firebase:firebase-common-ktx:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b2a903c9b0acab1e7698691d1a2c986\transformed\jetified-firebase-common-ktx-20.3.0\AndroidManifest.xml:16:17-82
218            <meta-data
218-->[com.google.firebase:firebase-crashlytics:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\967d8355743d170a894a29b4b1a80382\transformed\jetified-firebase-crashlytics-18.3.5\AndroidManifest.xml:17:13-19:85
219                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
219-->[com.google.firebase:firebase-crashlytics:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\967d8355743d170a894a29b4b1a80382\transformed\jetified-firebase-crashlytics-18.3.5\AndroidManifest.xml:18:17-115
220                android:value="com.google.firebase.components.ComponentRegistrar" />
220-->[com.google.firebase:firebase-crashlytics:18.3.5] C:\Users\<USER>\.gradle\caches\transforms-3\967d8355743d170a894a29b4b1a80382\transformed\jetified-firebase-crashlytics-18.3.5\AndroidManifest.xml:19:17-82
221            <meta-data
221-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:31:13-33:85
222                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
222-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:32:17-139
223                android:value="com.google.firebase.components.ComponentRegistrar" />
223-->[com.google.android.gms:play-services-measurement-api:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\168037942f327435dcf9fbba3d0dde22\transformed\jetified-play-services-measurement-api-21.2.0\AndroidManifest.xml:33:17-82
224            <meta-data
224-->[com.google.firebase:firebase-installations:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f175c9d0e7eca696c235389c9c14325d\transformed\jetified-firebase-installations-17.1.2\AndroidManifest.xml:17:13-19:85
225                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
225-->[com.google.firebase:firebase-installations:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f175c9d0e7eca696c235389c9c14325d\transformed\jetified-firebase-installations-17.1.2\AndroidManifest.xml:18:17-127
226                android:value="com.google.firebase.components.ComponentRegistrar" />
226-->[com.google.firebase:firebase-installations:17.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f175c9d0e7eca696c235389c9c14325d\transformed\jetified-firebase-installations-17.1.2\AndroidManifest.xml:19:17-82
227            <meta-data
227-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b6cf1c5e33857039c8761daab9336c00\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
228                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
228-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b6cf1c5e33857039c8761daab9336c00\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
229                android:value="com.google.firebase.components.ComponentRegistrar" />
229-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\b6cf1c5e33857039c8761daab9336c00\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
230        </service>
231
232        <activity
232-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:27:9-44:20
233            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
233-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:28:13-80
234            android:excludeFromRecents="true"
234-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:29:13-46
235            android:exported="true"
235-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:30:13-36
236            android:launchMode="singleTask"
236-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:31:13-44
237            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
237-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:32:13-72
238            <intent-filter>
238-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:33:13-43:29
239                <action android:name="android.intent.action.VIEW" />
239-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:34:17-69
239-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:34:25-66
240
241                <category android:name="android.intent.category.DEFAULT" />
241-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:36:17-76
241-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:36:27-73
242                <category android:name="android.intent.category.BROWSABLE" />
242-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:37:17-78
242-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:37:27-75
243
244                <data
244-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:39:17-42:51
245                    android:host="firebase.auth"
245-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:40:21-49
246                    android:path="/"
246-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:41:21-37
247                    android:scheme="genericidp" />
247-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:42:21-48
248            </intent-filter>
249        </activity>
250        <activity
250-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:45:9-62:20
251            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
251-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:46:13-79
252            android:excludeFromRecents="true"
252-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:47:13-46
253            android:exported="true"
253-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:48:13-36
254            android:launchMode="singleTask"
254-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:49:13-44
255            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
255-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:50:13-72
256            <intent-filter>
256-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:51:13-61:29
257                <action android:name="android.intent.action.VIEW" />
257-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:34:17-69
257-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:34:25-66
258
259                <category android:name="android.intent.category.DEFAULT" />
259-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:36:17-76
259-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:36:27-73
260                <category android:name="android.intent.category.BROWSABLE" />
260-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:37:17-78
260-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:37:27-75
261
262                <data
262-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:39:17-42:51
263                    android:host="firebase.auth"
263-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:40:21-49
264                    android:path="/"
264-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:41:21-37
265                    android:scheme="recaptcha" />
265-->[com.google.firebase:firebase-auth:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c27c4486f6ba1799a7d7d6b04e3d594e\transformed\jetified-firebase-auth-21.1.0\AndroidManifest.xml:42:21-48
266            </intent-filter>
267        </activity>
268
269        <receiver
269-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:31:9-38:20
270            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
270-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:32:13-78
271            android:exported="true"
271-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:33:13-36
272            android:permission="com.google.android.c2dm.permission.SEND" >
272-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:34:13-73
273            <intent-filter>
273-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:35:13-37:29
274                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
274-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:36:17-81
274-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:36:25-78
275            </intent-filter>
276        </receiver>
277        <!--
278             FirebaseMessagingService performs security checks at runtime,
279             but set to not exported to explicitly avoid allowing another app to call it.
280        -->
281        <service
281-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:44:9-51:19
282            android:name="com.google.firebase.messaging.FirebaseMessagingService"
282-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:45:13-82
283            android:directBootAware="true"
283-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:46:13-43
284            android:exported="false" >
284-->[com.google.firebase:firebase-messaging:23.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\451f8d17dc67984066076151222a5388\transformed\jetified-firebase-messaging-23.1.1\AndroidManifest.xml:47:13-37
285            <intent-filter android:priority="-500" >
285-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:84:13-86:29
286                <action android:name="com.google.firebase.MESSAGING_EVENT" />
286-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:85:17-78
286-->C:\Users\<USER>\AndroidStudioProjects\profitpro_android\app\src\main\AndroidManifest.xml:85:25-75
287            </intent-filter>
288        </service>
289
290        <activity
290-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5296fb8d536108245faa30c6d8197de1\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
291            android:name="com.google.android.gms.common.api.GoogleApiActivity"
291-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5296fb8d536108245faa30c6d8197de1\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
292            android:exported="false"
292-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5296fb8d536108245faa30c6d8197de1\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
293            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
293-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5296fb8d536108245faa30c6d8197de1\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
294
295        <provider
295-->[com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:25:9-30:39
296            android:name="com.google.firebase.provider.FirebaseInitProvider"
296-->[com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:26:13-77
297            android:authorities="com.manaknight.app.firebaseinitprovider"
297-->[com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:27:13-72
298            android:directBootAware="true"
298-->[com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:28:13-43
299            android:exported="false"
299-->[com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:29:13-37
300            android:initOrder="100" />
300-->[com.google.firebase:firebase-common:20.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\422e0883ce3188039e67d46c46566547\transformed\jetified-firebase-common-20.3.0\AndroidManifest.xml:30:13-36
301
302        <uses-library
302-->[androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:29:9-31:40
303            android:name="androidx.camera.extensions.impl"
303-->[androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:30:13-59
304            android:required="false" />
304-->[androidx.camera:camera-extensions:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\54b9491a3c7a79413b727cd19f2cf1e8\transformed\jetified-camera-extensions-1.2.2\AndroidManifest.xml:31:13-37
305
306        <service
306-->[androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:24:9-33:19
307            android:name="androidx.camera.core.impl.MetadataHolderService"
307-->[androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:25:13-75
308            android:enabled="false"
308-->[androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:26:13-36
309            android:exported="false" >
309-->[androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:27:13-37
310            <meta-data
310-->[androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:30:13-32:89
311                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
311-->[androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:31:17-103
312                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
312-->[androidx.camera:camera-camera2:1.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\ee28ca93c07bf81d90ff8f6019728918\transformed\jetified-camera-camera2-1.2.2\AndroidManifest.xml:32:17-86
313        </service>
314
315        <receiver
315-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:29:9-33:20
316            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
316-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:30:13-85
317            android:enabled="true"
317-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:31:13-35
318            android:exported="false" >
318-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:32:13-37
319        </receiver>
320
321        <service
321-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:35:9-38:40
322            android:name="com.google.android.gms.measurement.AppMeasurementService"
322-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:36:13-84
323            android:enabled="true"
323-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:37:13-35
324            android:exported="false" />
324-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:38:13-37
325        <service
325-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:39:9-43:72
326            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
326-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:40:13-87
327            android:enabled="true"
327-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:41:13-35
328            android:exported="false"
328-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:42:13-37
329            android:permission="android.permission.BIND_JOB_SERVICE" />
329-->[com.google.android.gms:play-services-measurement:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7955e5e73b6db12342392acdeae62840\transformed\jetified-play-services-measurement-21.2.0\AndroidManifest.xml:43:13-69
330
331        <activity
331-->[androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\549ede32db9c3acb303de4fa6d2a7dfa\transformed\jetified-ui-tooling-release\AndroidManifest.xml:23:9-25:39
332            android:name="androidx.compose.ui.tooling.PreviewActivity"
332-->[androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\549ede32db9c3acb303de4fa6d2a7dfa\transformed\jetified-ui-tooling-release\AndroidManifest.xml:24:13-71
333            android:exported="true" />
333-->[androidx.compose.ui:ui-tooling-android:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\549ede32db9c3acb303de4fa6d2a7dfa\transformed\jetified-ui-tooling-release\AndroidManifest.xml:25:13-36
334
335        <provider
335-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
336            android:name="androidx.startup.InitializationProvider"
336-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
337            android:authorities="com.manaknight.app.androidx-startup"
337-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
338            android:exported="false" >
338-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
339            <meta-data
339-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
340                android:name="androidx.emoji2.text.EmojiCompatInitializer"
340-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
341                android:value="androidx.startup" />
341-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ada1d27f1fad82fa455b667be34e866\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
342            <meta-data
342-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\078811cffc78e28bc7870a96ed097257\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
343                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
343-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\078811cffc78e28bc7870a96ed097257\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
344                android:value="androidx.startup" />
344-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\078811cffc78e28bc7870a96ed097257\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
345            <meta-data
345-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
346                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
346-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
347                android:value="androidx.startup" />
347-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
348        </provider>
349
350        <uses-library
350-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
351            android:name="androidx.window.extensions"
351-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
352            android:required="false" />
352-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
353        <uses-library
353-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
354            android:name="androidx.window.sidecar"
354-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
355            android:required="false" />
355-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\415c161ff15d16996587df02a2a7fdc1\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
356
357        <meta-data
357-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3eddf5b8a9d33eb11d1830cb5e4fd1aa\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
358            android:name="com.google.android.gms.version"
358-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3eddf5b8a9d33eb11d1830cb5e4fd1aa\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
359            android:value="@integer/google_play_services_version" />
359-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3eddf5b8a9d33eb11d1830cb5e4fd1aa\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
360
361        <receiver
361-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
362            android:name="androidx.profileinstaller.ProfileInstallReceiver"
362-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
363            android:directBootAware="false"
363-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
364            android:enabled="true"
364-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
365            android:exported="true"
365-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
366            android:permission="android.permission.DUMP" >
366-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
367            <intent-filter>
367-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
368                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
368-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
368-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
369            </intent-filter>
370            <intent-filter>
370-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
371                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
371-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
371-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
372            </intent-filter>
373            <intent-filter>
373-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
374                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
374-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
374-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
375            </intent-filter>
376            <intent-filter>
376-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
377                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
377-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
377-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\64416a7d817f91ee7330c434470bf72d\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
378            </intent-filter>
379        </receiver>
380
381        <meta-data
381-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:19:9-21:37
382            android:name="com.google.android.play.billingclient.version"
382-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:20:13-73
383            android:value="6.0.1" />
383-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:21:13-34
384
385        <activity
385-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:23:9-27:75
386            android:name="com.android.billingclient.api.ProxyBillingActivity"
386-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:24:13-78
387            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
387-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:25:13-96
388            android:exported="false"
388-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:26:13-37
389            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
389-->[com.android.billingclient:billing:6.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\23890166d6a8b14dee3091bd6ffdea5c\transformed\jetified-billing-6.0.1\AndroidManifest.xml:27:13-72
390
391        <service
391-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
392            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
392-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
393            android:exported="false" >
393-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
394            <meta-data
394-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
395                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
395-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
396                android:value="cct" />
396-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e0f5c7c79df0748d229f2beda36ab74d\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
397        </service>
398        <service
398-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
399            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
399-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
400            android:exported="false"
400-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
401            android:permission="android.permission.BIND_JOB_SERVICE" >
401-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
402        </service>
403
404        <receiver
404-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
405            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
405-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
406            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
406-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\e3b38192b92eeb3fc9e8187da50ce6a9\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
407        <activity
407-->[com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\dc72e552015300076bb41fda69eedbd2\transformed\jetified-core-common-2.0.2\AndroidManifest.xml:14:9-18:65
408            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
408-->[com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\dc72e552015300076bb41fda69eedbd2\transformed\jetified-core-common-2.0.2\AndroidManifest.xml:15:13-93
409            android:exported="false"
409-->[com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\dc72e552015300076bb41fda69eedbd2\transformed\jetified-core-common-2.0.2\AndroidManifest.xml:16:13-37
410            android:stateNotNeeded="true"
410-->[com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\dc72e552015300076bb41fda69eedbd2\transformed\jetified-core-common-2.0.2\AndroidManifest.xml:17:13-42
411            android:theme="@style/Theme.PlayCore.Transparent" />
411-->[com.google.android.play:core-common:2.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\dc72e552015300076bb41fda69eedbd2\transformed\jetified-core-common-2.0.2\AndroidManifest.xml:18:13-62
412    </application>
413
414</manifest>
