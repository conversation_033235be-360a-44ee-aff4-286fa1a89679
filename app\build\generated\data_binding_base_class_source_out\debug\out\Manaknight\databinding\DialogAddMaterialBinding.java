// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAddMaterialBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final MaterialButton btnGotIt;

  @NonNull
  public final MaterialButton btnSkip;

  @NonNull
  public final CardView dialogCardView;

  private DialogAddMaterialBinding(@NonNull FrameLayout rootView, @NonNull MaterialButton btnGotIt,
      @NonNull MaterialButton btnSkip, @NonNull CardView dialogCardView) {
    this.rootView = rootView;
    this.btnGotIt = btnGotIt;
    this.btnSkip = btnSkip;
    this.dialogCardView = dialogCardView;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAddMaterialBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAddMaterialBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_add_material, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAddMaterialBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnGotIt;
      MaterialButton btnGotIt = ViewBindings.findChildViewById(rootView, id);
      if (btnGotIt == null) {
        break missingId;
      }

      id = R.id.btnSkip;
      MaterialButton btnSkip = ViewBindings.findChildViewById(rootView, id);
      if (btnSkip == null) {
        break missingId;
      }

      id = R.id.dialogCardView;
      CardView dialogCardView = ViewBindings.findChildViewById(rootView, id);
      if (dialogCardView == null) {
        break missingId;
      }

      return new DialogAddMaterialBinding((FrameLayout) rootView, btnGotIt, btnSkip,
          dialogCardView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
