// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentWorkerviewBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final HeaderBinding headerInclude;

  @NonNull
  public final LinearLayout linearlayout839e9f22;

  private FragmentWorkerviewBinding(@NonNull LinearLayout rootView,
      @NonNull HeaderBinding headerInclude, @NonNull LinearLayout linearlayout839e9f22) {
    this.rootView = rootView;
    this.headerInclude = headerInclude;
    this.linearlayout839e9f22 = linearlayout839e9f22;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentWorkerviewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentWorkerviewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_workerview, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentWorkerviewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.headerInclude;
      View headerInclude = ViewBindings.findChildViewById(rootView, id);
      if (headerInclude == null) {
        break missingId;
      }
      HeaderBinding binding_headerInclude = HeaderBinding.bind(headerInclude);

      id = R.id.linearlayout_839e9f22;
      LinearLayout linearlayout839e9f22 = ViewBindings.findChildViewById(rootView, id);
      if (linearlayout839e9f22 == null) {
        break missingId;
      }

      return new FragmentWorkerviewBinding((LinearLayout) rootView, binding_headerInclude,
          linearlayout839e9f22);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
