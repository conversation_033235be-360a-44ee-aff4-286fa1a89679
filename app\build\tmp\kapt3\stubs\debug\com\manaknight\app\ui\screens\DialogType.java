package com.manaknight.app.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0005\b\u0082\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005\u00a8\u0006\u0006"}, d2 = {"Lcom/manaknight/app/ui/screens/DialogType;", "", "(Ljava/lang/String;I)V", "NONE", "CANCEL_CONFIRMATION", "CANCEL_REASON", "app_debug"})
enum DialogType {
    /*public static final*/ NONE /* = new NONE() */,
    /*public static final*/ CANCEL_CONFIRMATION /* = new CANCEL_CONFIRMATION() */,
    /*public static final*/ CANCEL_REASON /* = new CANCEL_REASON() */;
    
    DialogType() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.manaknight.app.ui.screens.DialogType> getEntries() {
        return null;
    }
}