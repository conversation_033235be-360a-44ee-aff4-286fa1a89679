package com.manaknight.app.adapter;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0005\u0018\u00002\f\u0012\b\u0012\u00060\u0002R\u00020\u00000\u0001:\u0001\u001aB9\u0012\u0018\u0010\u0003\u001a\u0014\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0004\u0012\u0018\u0010\b\u001a\u0014\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0004\u00a2\u0006\u0002\u0010\tJ\b\u0010\u0010\u001a\u00020\u0006H\u0016J\u001c\u0010\u0011\u001a\u00020\u00072\n\u0010\u0012\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u0013\u001a\u00020\u0006H\u0016J\u001c\u0010\u0014\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0006H\u0016J\u001e\u0010\u0018\u001a\u00020\u00072\u0016\u0010\u0019\u001a\u0012\u0012\u0004\u0012\u00020\u00050\u000bj\b\u0012\u0004\u0012\u00020\u0005`\fR\u001e\u0010\n\u001a\u0012\u0012\u0004\u0012\u00020\u00050\u000bj\b\u0012\u0004\u0012\u00020\u0005`\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R#\u0010\b\u001a\u0014\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR#\u0010\u0003\u001a\u0014\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000e\u00a8\u0006\u001b"}, d2 = {"Lcom/manaknight/app/adapter/SquareAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/manaknight/app/adapter/SquareAdapter$ViewHolder;", "onEditClick", "Lkotlin/Function2;", "Lcom/manaknight/app/model/remote/LinealModel;", "", "", "onDeleteClick", "(Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function2;)V", "list", "Ljava/util/ArrayList;", "Lkotlin/collections/ArrayList;", "getOnDeleteClick", "()Lkotlin/jvm/functions/Function2;", "getOnEditClick", "getItemCount", "onBindViewHolder", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "refresh", "newList", "ViewHolder", "app_debug"})
public final class SquareAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.manaknight.app.adapter.SquareAdapter.ViewHolder> {
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function2<com.manaknight.app.model.remote.LinealModel, java.lang.Integer, kotlin.Unit> onEditClick = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function2<com.manaknight.app.model.remote.LinealModel, java.lang.Integer, kotlin.Unit> onDeleteClick = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<com.manaknight.app.model.remote.LinealModel> list = null;
    
    public SquareAdapter(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super com.manaknight.app.model.remote.LinealModel, ? super java.lang.Integer, kotlin.Unit> onEditClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super com.manaknight.app.model.remote.LinealModel, ? super java.lang.Integer, kotlin.Unit> onDeleteClick) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlin.jvm.functions.Function2<com.manaknight.app.model.remote.LinealModel, java.lang.Integer, kotlin.Unit> getOnEditClick() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlin.jvm.functions.Function2<com.manaknight.app.model.remote.LinealModel, java.lang.Integer, kotlin.Unit> getOnDeleteClick() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.manaknight.app.adapter.SquareAdapter.ViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.adapter.SquareAdapter.ViewHolder holder, int position) {
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    public final void refresh(@org.jetbrains.annotations.NotNull()
    java.util.ArrayList<com.manaknight.app.model.remote.LinealModel> newList) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/manaknight/app/adapter/SquareAdapter$ViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "binding", "LManaknight/databinding/ItemLinealBinding;", "(Lcom/manaknight/app/adapter/SquareAdapter;LManaknight/databinding/ItemLinealBinding;)V", "getBinding", "()LManaknight/databinding/ItemLinealBinding;", "app_debug"})
    public final class ViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final Manaknight.databinding.ItemLinealBinding binding = null;
        
        public ViewHolder(@org.jetbrains.annotations.NotNull()
        Manaknight.databinding.ItemLinealBinding binding) {
            super(null);
        }
        
        @org.jetbrains.annotations.NotNull()
        public final Manaknight.databinding.ItemLinealBinding getBinding() {
            return null;
        }
    }
}