// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentAddLineItemsBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnLinearFt;

  @NonNull
  public final MaterialButton btnMaterial;

  @NonNull
  public final MaterialButton btnSquareFt;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final RelativeLayout container;

  @NonNull
  public final ConstraintLayout coss;

  @NonNull
  public final LinearLayout dataLayout;

  @NonNull
  public final EditText descInput;

  @NonNull
  public final HeaderBinding headerInclude;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final ConstraintLayout innerConstraintLayout;

  @NonNull
  public final TextView lineItem;

  private FragmentAddLineItemsBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnLinearFt, @NonNull MaterialButton btnMaterial,
      @NonNull MaterialButton btnSquareFt, @Nullable RelativeLayout container,
      @NonNull ConstraintLayout coss, @NonNull LinearLayout dataLayout, @NonNull EditText descInput,
      @NonNull HeaderBinding headerInclude, @Nullable ConstraintLayout innerConstraintLayout,
      @NonNull TextView lineItem) {
    this.rootView = rootView;
    this.btnLinearFt = btnLinearFt;
    this.btnMaterial = btnMaterial;
    this.btnSquareFt = btnSquareFt;
    this.container = container;
    this.coss = coss;
    this.dataLayout = dataLayout;
    this.descInput = descInput;
    this.headerInclude = headerInclude;
    this.innerConstraintLayout = innerConstraintLayout;
    this.lineItem = lineItem;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentAddLineItemsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentAddLineItemsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_add_line_items, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentAddLineItemsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnLinearFt;
      MaterialButton btnLinearFt = ViewBindings.findChildViewById(rootView, id);
      if (btnLinearFt == null) {
        break missingId;
      }

      id = R.id.btnMaterial;
      MaterialButton btnMaterial = ViewBindings.findChildViewById(rootView, id);
      if (btnMaterial == null) {
        break missingId;
      }

      id = R.id.btnSquareFt;
      MaterialButton btnSquareFt = ViewBindings.findChildViewById(rootView, id);
      if (btnSquareFt == null) {
        break missingId;
      }

      id = R.id.container;
      RelativeLayout container = ViewBindings.findChildViewById(rootView, id);

      ConstraintLayout coss = (ConstraintLayout) rootView;

      id = R.id.dataLayout;
      LinearLayout dataLayout = ViewBindings.findChildViewById(rootView, id);
      if (dataLayout == null) {
        break missingId;
      }

      id = R.id.descInput;
      EditText descInput = ViewBindings.findChildViewById(rootView, id);
      if (descInput == null) {
        break missingId;
      }

      id = R.id.headerInclude;
      View headerInclude = ViewBindings.findChildViewById(rootView, id);
      if (headerInclude == null) {
        break missingId;
      }
      HeaderBinding binding_headerInclude = HeaderBinding.bind(headerInclude);

      id = R.id.innerConstraintLayout;
      ConstraintLayout innerConstraintLayout = ViewBindings.findChildViewById(rootView, id);

      id = R.id.lineItem;
      TextView lineItem = ViewBindings.findChildViewById(rootView, id);
      if (lineItem == null) {
        break missingId;
      }

      return new FragmentAddLineItemsBinding((ConstraintLayout) rootView, btnLinearFt, btnMaterial,
          btnSquareFt, container, coss, dataLayout, descInput, binding_headerInclude,
          innerConstraintLayout, lineItem);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
