// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemLinealBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageView btnDelete;

  @NonNull
  public final ImageView btnEdit;

  @NonNull
  public final TextView name;

  @NonNull
  public final TextView txtLabourCost;

  @NonNull
  public final TextView txtLinealCost;

  @NonNull
  public final TextView txtMName;

  @NonNull
  public final TextView txtMaterialCost;

  @NonNull
  public final TextView txtProfitOverhead;

  @NonNull
  public final TextView txtRemaing;

  private ItemLinealBinding(@NonNull ConstraintLayout rootView, @NonNull ImageView btnDelete,
      @NonNull ImageView btnEdit, @NonNull TextView name, @NonNull TextView txtLabourCost,
      @NonNull TextView txtLinealCost, @NonNull TextView txtMName,
      @NonNull TextView txtMaterialCost, @NonNull TextView txtProfitOverhead,
      @NonNull TextView txtRemaing) {
    this.rootView = rootView;
    this.btnDelete = btnDelete;
    this.btnEdit = btnEdit;
    this.name = name;
    this.txtLabourCost = txtLabourCost;
    this.txtLinealCost = txtLinealCost;
    this.txtMName = txtMName;
    this.txtMaterialCost = txtMaterialCost;
    this.txtProfitOverhead = txtProfitOverhead;
    this.txtRemaing = txtRemaing;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemLinealBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemLinealBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_lineal, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemLinealBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnDelete;
      ImageView btnDelete = ViewBindings.findChildViewById(rootView, id);
      if (btnDelete == null) {
        break missingId;
      }

      id = R.id.btnEdit;
      ImageView btnEdit = ViewBindings.findChildViewById(rootView, id);
      if (btnEdit == null) {
        break missingId;
      }

      id = R.id.name;
      TextView name = ViewBindings.findChildViewById(rootView, id);
      if (name == null) {
        break missingId;
      }

      id = R.id.txtLabourCost;
      TextView txtLabourCost = ViewBindings.findChildViewById(rootView, id);
      if (txtLabourCost == null) {
        break missingId;
      }

      id = R.id.txtLinealCost;
      TextView txtLinealCost = ViewBindings.findChildViewById(rootView, id);
      if (txtLinealCost == null) {
        break missingId;
      }

      id = R.id.txtMName;
      TextView txtMName = ViewBindings.findChildViewById(rootView, id);
      if (txtMName == null) {
        break missingId;
      }

      id = R.id.txtMaterialCost;
      TextView txtMaterialCost = ViewBindings.findChildViewById(rootView, id);
      if (txtMaterialCost == null) {
        break missingId;
      }

      id = R.id.txtProfitOverhead;
      TextView txtProfitOverhead = ViewBindings.findChildViewById(rootView, id);
      if (txtProfitOverhead == null) {
        break missingId;
      }

      id = R.id.txtRemaing;
      TextView txtRemaing = ViewBindings.findChildViewById(rootView, id);
      if (txtRemaing == null) {
        break missingId;
      }

      return new ItemLinealBinding((ConstraintLayout) rootView, btnDelete, btnEdit, name,
          txtLabourCost, txtLinealCost, txtMName, txtMaterialCost, txtProfitOverhead, txtRemaing);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
