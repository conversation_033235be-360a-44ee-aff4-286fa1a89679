package com.manaknight.app.model.remote.profitPro;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u000f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B3\u0012\b\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u0012\u000e\u0010\u0006\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0007\u00a2\u0006\u0002\u0010\tJ\u0010\u0010\u0011\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u000bJ\u0010\u0010\u0012\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u000bJ\u0010\u0010\u0013\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u000bJ\u0011\u0010\u0014\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0007H\u00c6\u0003JD\u0010\u0015\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0006\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0007H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0016J\u0013\u0010\u0017\u001a\u00020\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001a\u001a\u00020\u001bH\u00d6\u0001J\t\u0010\u001c\u001a\u00020\u001dH\u00d6\u0001R\u001a\u0010\u0005\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\n\n\u0002\u0010\f\u001a\u0004\b\n\u0010\u000bR\u001a\u0010\u0002\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\n\n\u0002\u0010\f\u001a\u0004\b\r\u0010\u000bR\u001a\u0010\u0004\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\n\n\u0002\u0010\f\u001a\u0004\b\u000e\u0010\u000bR\u0019\u0010\u0006\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010\u00a8\u0006\u001e"}, d2 = {"Lcom/manaknight/app/model/remote/profitPro/Materials;", "", "materialBudget", "", "materialSpent", "materialBalance", "materials", "", "Lcom/manaknight/app/model/remote/profitPro/MaterialItem;", "(Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;Ljava/util/List;)V", "getMaterialBalance", "()Ljava/lang/Double;", "Ljava/lang/Double;", "getMaterialBudget", "getMaterialSpent", "getMaterials", "()Ljava/util/List;", "component1", "component2", "component3", "component4", "copy", "(Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;Ljava/util/List;)Lcom/manaknight/app/model/remote/profitPro/Materials;", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
public final class Materials {
    @com.google.gson.annotations.SerializedName(value = "material_budget")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Double materialBudget = null;
    @com.google.gson.annotations.SerializedName(value = "material_spent")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Double materialSpent = null;
    @com.google.gson.annotations.SerializedName(value = "material_balance")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Double materialBalance = null;
    @org.jetbrains.annotations.Nullable()
    private final java.util.List<com.manaknight.app.model.remote.profitPro.MaterialItem> materials = null;
    
    public Materials(@org.jetbrains.annotations.Nullable()
    java.lang.Double materialBudget, @org.jetbrains.annotations.Nullable()
    java.lang.Double materialSpent, @org.jetbrains.annotations.Nullable()
    java.lang.Double materialBalance, @org.jetbrains.annotations.Nullable()
    java.util.List<com.manaknight.app.model.remote.profitPro.MaterialItem> materials) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double getMaterialBudget() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double getMaterialSpent() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double getMaterialBalance() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.manaknight.app.model.remote.profitPro.MaterialItem> getMaterials() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Double component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.manaknight.app.model.remote.profitPro.MaterialItem> component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.manaknight.app.model.remote.profitPro.Materials copy(@org.jetbrains.annotations.Nullable()
    java.lang.Double materialBudget, @org.jetbrains.annotations.Nullable()
    java.lang.Double materialSpent, @org.jetbrains.annotations.Nullable()
    java.lang.Double materialBalance, @org.jetbrains.annotations.Nullable()
    java.util.List<com.manaknight.app.model.remote.profitPro.MaterialItem> materials) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}