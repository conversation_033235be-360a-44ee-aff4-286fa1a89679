// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemMultiSelectFilterOptionBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final CheckBox cbFilterOption;

  @NonNull
  public final TextView tvFilterOption;

  private ItemMultiSelectFilterOptionBinding(@NonNull LinearLayout rootView,
      @NonNull CheckBox cbFilterOption, @NonNull TextView tvFilterOption) {
    this.rootView = rootView;
    this.cbFilterOption = cbFilterOption;
    this.tvFilterOption = tvFilterOption;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemMultiSelectFilterOptionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemMultiSelectFilterOptionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_multi_select_filter_option, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemMultiSelectFilterOptionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cbFilterOption;
      CheckBox cbFilterOption = ViewBindings.findChildViewById(rootView, id);
      if (cbFilterOption == null) {
        break missingId;
      }

      id = R.id.tvFilterOption;
      TextView tvFilterOption = ViewBindings.findChildViewById(rootView, id);
      if (tvFilterOption == null) {
        break missingId;
      }

      return new ItemMultiSelectFilterOptionBinding((LinearLayout) rootView, cbFilterOption,
          tvFilterOption);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
