package com.manaknight.app.network;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00b0\u0015\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010$\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0004\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\bf\u0018\u00002\u00020\u0001J\u001e\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\u0007J\u001e\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\b\b\u0001\u0010\u0005\u001a\u00020\nH\u00a7@\u00a2\u0006\u0002\u0010\u000bJ\u001e\u0010\f\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\b\b\u0001\u0010\u0005\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u000eJ\u001e\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\b\b\u0001\u0010\u0005\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u000eJ\u001e\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00110\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u0012H\u00a7@\u00a2\u0006\u0002\u0010\u0013J8\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00150\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0002\u0010\u001aJ*\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u001c0\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u001d2\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0002\u0010\u001fJ\u001e\u0010 \u001a\b\u0012\u0004\u0012\u00020!0\u00032\b\b\u0001\u0010\u0005\u001a\u00020\"H\u00a7@\u00a2\u0006\u0002\u0010#J\u0014\u0010$\u001a\b\u0012\u0004\u0012\u00020%0\u0003H\u00a7@\u00a2\u0006\u0002\u0010&J\u001e\u0010\'\u001a\b\u0012\u0004\u0012\u00020(0\u00032\b\b\u0001\u0010\u0005\u001a\u00020)H\u00a7@\u00a2\u0006\u0002\u0010*J(\u0010+\u001a\b\u0012\u0004\u0012\u00020,0\u00032\b\b\u0001\u0010-\u001a\u00020.2\b\b\u0001\u0010/\u001a\u00020.H\u00a7@\u00a2\u0006\u0002\u00100J\u001e\u00101\u001a\b\u0012\u0004\u0012\u0002020\u00032\b\b\u0001\u0010\u0005\u001a\u000203H\u00a7@\u00a2\u0006\u0002\u00104J \u00105\u001a\b\u0012\u0004\u0012\u0002060\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0002\u00107J*\u00108\u001a\b\u0012\u0004\u0012\u0002090\u00032\b\b\u0001\u0010\u0005\u001a\u00020:2\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0002\u0010;J\u00a4\u0001\u0010<\u001a\b\u0012\u0004\u0012\u00020=0\u000320\b\u0001\u0010>\u001a*\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0017\u0012\u0004\u0012\u00020\u00010@0?j\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0017\u0012\u0004\u0012\u00020\u00010@`A20\b\u0001\u0010B\u001a*\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0017\u0012\u0004\u0012\u00020\u00010@0?j\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0017\u0012\u0004\u0012\u00020\u00010@`A2\n\b\u0001\u0010C\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010D\u001a\u0004\u0018\u00010\u00172\b\b\u0001\u0010-\u001a\u00020.2\b\b\u0001\u0010\u0018\u001a\u00020.H\u00a7@\u00a2\u0006\u0002\u0010EJ\u001e\u0010F\u001a\b\u0012\u0004\u0012\u00020G0\u00032\b\b\u0001\u0010H\u001a\u00020.H\u00a7@\u00a2\u0006\u0002\u0010IJ \u0010J\u001a\b\u0012\u0004\u0012\u00020K0\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0002\u00107J\u001e\u0010L\u001a\b\u0012\u0004\u0012\u00020M0\u00032\b\b\u0001\u0010\u0005\u001a\u00020NH\u00a7@\u00a2\u0006\u0002\u0010OJ \u0010P\u001a\b\u0012\u0004\u0012\u00020Q0\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0002\u00107J4\u0010R\u001a\b\u0012\u0004\u0012\u00020S0\u00032\b\b\u0001\u0010-\u001a\u00020.2\b\b\u0001\u0010\u0018\u001a\u00020.2\n\b\u0001\u0010T\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0002\u0010UJ\u001e\u0010V\u001a\b\u0012\u0004\u0012\u00020W0\u00032\b\b\u0001\u0010\u0005\u001a\u00020XH\u00a7@\u00a2\u0006\u0002\u0010YJ(\u0010Z\u001a\b\u0012\u0004\u0012\u00020[0\u00032\b\b\u0001\u0010\u0005\u001a\u00020\\2\b\b\u0001\u0010]\u001a\u00020.H\u00a7@\u00a2\u0006\u0002\u0010^J(\u0010_\u001a\b\u0012\u0004\u0012\u00020`0\u00032\b\b\u0001\u0010a\u001a\u00020.2\b\b\u0001\u0010b\u001a\u00020.H\u00a7@\u00a2\u0006\u0002\u00100J(\u0010c\u001a\b\u0012\u0004\u0012\u00020d0\u00032\b\b\u0001\u0010a\u001a\u00020.2\b\b\u0001\u0010b\u001a\u00020.H\u00a7@\u00a2\u0006\u0002\u00100J\u0014\u0010e\u001a\b\u0012\u0004\u0012\u00020f0\u0003H\u00a7@\u00a2\u0006\u0002\u0010&J\u0014\u0010g\u001a\b\u0012\u0004\u0012\u00020h0\u0003H\u00a7@\u00a2\u0006\u0002\u0010&J\u001e\u0010i\u001a\b\u0012\u0004\u0012\u00020j0\u00032\b\b\u0001\u0010\u0005\u001a\u00020kH\u00a7@\u00a2\u0006\u0002\u0010lJ\u001e\u0010m\u001a\b\u0012\u0004\u0012\u00020n0\u00032\b\b\u0001\u0010\u0005\u001a\u00020oH\u00a7@\u00a2\u0006\u0002\u0010pJ\u001e\u0010q\u001a\b\u0012\u0004\u0012\u00020r0\u00032\b\b\u0001\u0010\u0005\u001a\u00020sH\u00a7@\u00a2\u0006\u0002\u0010tJ\u001e\u0010u\u001a\b\u0012\u0004\u0012\u00020v0\u00032\b\b\u0001\u0010\u0005\u001a\u00020wH\u00a7@\u00a2\u0006\u0002\u0010xJ\u001e\u0010y\u001a\b\u0012\u0004\u0012\u00020z0\u00032\b\b\u0001\u0010\u0005\u001a\u00020{H\u00a7@\u00a2\u0006\u0002\u0010|J*\u0010}\u001a\b\u0012\u0004\u0012\u00020~0\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u007f2\t\b\u0001\u0010\u0080\u0001\u001a\u00020\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0001J\"\u0010\u0082\u0001\u001a\t\u0012\u0005\u0012\u00030\u0083\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u0084\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u0085\u0001J\"\u0010\u0086\u0001\u001a\t\u0012\u0005\u0012\u00030\u0087\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u0088\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u0089\u0001J\"\u0010\u008a\u0001\u001a\t\u0012\u0005\u0012\u00030\u008b\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u008c\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u008d\u0001J\"\u0010\u008e\u0001\u001a\t\u0012\u0005\u0012\u00030\u008f\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u0090\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u0091\u0001J\"\u0010\u0092\u0001\u001a\t\u0012\u0005\u0012\u00030\u0093\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u0094\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u0095\u0001J!\u0010\u0096\u0001\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u0097\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u0098\u0001J\"\u0010\u0099\u0001\u001a\t\u0012\u0005\u0012\u00030\u009a\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u009b\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u009c\u0001J!\u0010\u009d\u0001\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u009e\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u009f\u0001J\"\u0010\u00a0\u0001\u001a\t\u0012\u0005\u0012\u00030\u00a1\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00a2\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00a3\u0001J!\u0010\u00a4\u0001\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00a5\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00a6\u0001J\"\u0010\u00a7\u0001\u001a\t\u0012\u0005\u0012\u00030\u00a8\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00a9\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00aa\u0001J\"\u0010\u00ab\u0001\u001a\t\u0012\u0005\u0012\u00030\u00ac\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00ad\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00ae\u0001J\"\u0010\u00af\u0001\u001a\t\u0012\u0005\u0012\u00030\u00b0\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00b1\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00b2\u0001J\"\u0010\u00b3\u0001\u001a\t\u0012\u0005\u0012\u00030\u00b4\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00b5\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00b6\u0001J\"\u0010\u00b7\u0001\u001a\t\u0012\u0005\u0012\u00030\u00b8\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00b9\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00ba\u0001J\"\u0010\u00bb\u0001\u001a\t\u0012\u0005\u0012\u00030\u00bc\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00bd\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00be\u0001J\"\u0010\u00bf\u0001\u001a\t\u0012\u0005\u0012\u00030\u00c0\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00c1\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00c2\u0001J\"\u0010\u00c3\u0001\u001a\t\u0012\u0005\u0012\u00030\u00c4\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00c5\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00c6\u0001J\"\u0010\u00c7\u0001\u001a\t\u0012\u0005\u0012\u00030\u00c8\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00c9\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00ca\u0001J!\u0010\u00c7\u0001\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00cb\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00cc\u0001J!\u0010\u00cd\u0001\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00ce\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00cf\u0001J!\u0010\u00d0\u0001\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00d1\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00d2\u0001J\"\u0010\u00d3\u0001\u001a\t\u0012\u0005\u0012\u00030\u00d4\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00d5\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00d6\u0001J\"\u0010\u00d7\u0001\u001a\t\u0012\u0005\u0012\u00030\u00d8\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00d9\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00da\u0001J\"\u0010\u00db\u0001\u001a\t\u0012\u0005\u0012\u00030\u00dc\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00dd\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00de\u0001J!\u0010\u00df\u0001\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00e0\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00e1\u0001J\"\u0010\u00e2\u0001\u001a\t\u0012\u0005\u0012\u00030\u00e3\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00e4\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00e5\u0001J\"\u0010\u00e6\u0001\u001a\t\u0012\u0005\u0012\u00030\u00e7\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00e8\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00e9\u0001J\"\u0010\u00ea\u0001\u001a\t\u0012\u0005\u0012\u00030\u00eb\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00ec\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00ed\u0001J\"\u0010\u00ee\u0001\u001a\t\u0012\u0005\u0012\u00030\u00eb\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00ef\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00f0\u0001J\"\u0010\u00f1\u0001\u001a\t\u0012\u0005\u0012\u00030\u00f2\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00f3\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00f4\u0001J\"\u0010\u00f5\u0001\u001a\t\u0012\u0005\u0012\u00030\u00f6\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00f7\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00f8\u0001J\"\u0010\u00f9\u0001\u001a\t\u0012\u0005\u0012\u00030\u00fa\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00fb\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00fc\u0001J\"\u0010\u00fd\u0001\u001a\t\u0012\u0005\u0012\u00030\u00fe\u00010\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00ff\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u0080\u0002J\"\u0010\u0081\u0002\u001a\t\u0012\u0005\u0012\u00030\u0082\u00020\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u0083\u0002H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0002J\"\u0010\u0085\u0002\u001a\t\u0012\u0005\u0012\u00030\u0086\u00020\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u0087\u0002H\u00a7@\u00a2\u0006\u0003\u0010\u0088\u0002J\"\u0010\u0089\u0002\u001a\t\u0012\u0005\u0012\u00030\u008a\u00020\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u008b\u0002H\u00a7@\u00a2\u0006\u0003\u0010\u008c\u0002J\"\u0010\u008d\u0002\u001a\t\u0012\u0005\u0012\u00030\u008e\u00020\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u008f\u0002H\u00a7@\u00a2\u0006\u0003\u0010\u0090\u0002J#\u0010\u0091\u0002\u001a\t\u0012\u0005\u0012\u00030\u0092\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u0094\u0002\u001a\t\u0012\u0005\u0012\u00030\u0095\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u0096\u0002\u001a\t\u0012\u0005\u0012\u00030\u0097\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J\"\u0010\u0098\u0002\u001a\t\u0012\u0005\u0012\u00030\u0099\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0002\u00107J\"\u0010\u009a\u0002\u001a\t\u0012\u0005\u0012\u00030\u009b\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0002\u00107J#\u0010\u009c\u0002\u001a\t\u0012\u0005\u0012\u00030\u009d\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u009e\u0002\u001a\t\u0012\u0005\u0012\u00030\u009f\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00a0\u0002\u001a\t\u0012\u0005\u0012\u00030\u00a1\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00a2\u0002\u001a\t\u0012\u0005\u0012\u00030\u00a3\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00a4\u0002\u001a\t\u0012\u0005\u0012\u00030\u00a5\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00a6\u0002\u001a\t\u0012\u0005\u0012\u00030\u00a7\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00a8\u0002\u001a\t\u0012\u0005\u0012\u00030\u00a9\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00aa\u0002\u001a\t\u0012\u0005\u0012\u00030\u00ab\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00ac\u0002\u001a\t\u0012\u0005\u0012\u00030\u00ad\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J\"\u0010\u00ae\u0002\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J$\u0010\u00af\u0002\u001a\t\u0012\u0005\u0012\u00030\u00b0\u00020\u00032\u000b\b\u0001\u0010\u001e\u001a\u0005\u0018\u00010\u00b1\u0002H\u00a7@\u00a2\u0006\u0003\u0010\u00b2\u0002J#\u0010\u00b3\u0002\u001a\t\u0012\u0005\u0012\u00030\u00b4\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00b5\u0002\u001a\t\u0012\u0005\u0012\u00030\u00b6\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00b7\u0002\u001a\t\u0012\u0005\u0012\u00030\u00b8\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00b9\u0002\u001a\t\u0012\u0005\u0012\u00030\u00ba\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00bb\u0002\u001a\t\u0012\u0005\u0012\u00030\u00bc\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00bd\u0002\u001a\t\u0012\u0005\u0012\u00030\u00be\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J\"\u0010\u00bf\u0002\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00c0\u0002\u001a\t\u0012\u0005\u0012\u00030\u00c1\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J\"\u0010\u00c2\u0002\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00c3\u0002\u001a\t\u0012\u0005\u0012\u00030\u00c4\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00c5\u0002\u001a\t\u0012\u0005\u0012\u00030\u00c6\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00c7\u0002\u001a\t\u0012\u0005\u0012\u00030\u00c8\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00c9\u0002\u001a\t\u0012\u0005\u0012\u00030\u00ca\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00cb\u0002\u001a\t\u0012\u0005\u0012\u00030\u00cc\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00cd\u0002\u001a\t\u0012\u0005\u0012\u00030\u00ce\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00cf\u0002\u001a\t\u0012\u0005\u0012\u00030\u00d0\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00d1\u0002\u001a\t\u0012\u0005\u0012\u00030\u00d2\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00d3\u0002\u001a\t\u0012\u0005\u0012\u00030\u00d4\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J\"\u0010\u00d5\u0002\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00d6\u0002\u001a\t\u0012\u0005\u0012\u00030\u00d7\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00d8\u0002\u001a\t\u0012\u0005\u0012\u00030\u00d9\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00da\u0002\u001a\t\u0012\u0005\u0012\u00030\u00db\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J#\u0010\u00dc\u0002\u001a\t\u0012\u0005\u0012\u00030\u00dd\u00020\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J\"\u0010\u00de\u0002\u001a\t\u0012\u0005\u0012\u00030\u00df\u00020\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00e0\u0002H\u00a7@\u00a2\u0006\u0003\u0010\u00e1\u0002J1\u0010\u00e2\u0002\u001a\t\u0012\u0005\u0012\u00030\u00e3\u00020\u00032\u000b\b\u0001\u0010\u00e4\u0002\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u00e5\u0002\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00e6\u0002J1\u0010\u00e7\u0002\u001a\t\u0012\u0005\u0012\u00030\u00e8\u00020\u00032\u000b\b\u0001\u0010\u00e9\u0002\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u00ea\u0002\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u00eb\u0002J$\u0010\u00ec\u0002\u001a\t\u0012\u0005\u0012\u00030\u00ed\u00020\u00032\u000b\b\u0001\u0010\u00e5\u0002\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J\u0016\u0010\u00ee\u0002\u001a\t\u0012\u0005\u0012\u00030\u00ef\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010&J/\u0010\u00f0\u0002\u001a\t\u0012\u0005\u0012\u00030\u00f1\u00020\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00f2\u00022\u000b\b\u0001\u0010\u001e\u001a\u0005\u0018\u00010\u00b1\u0002H\u00a7@\u00a2\u0006\u0003\u0010\u00f3\u0002J\u0016\u0010\u00f4\u0002\u001a\t\u0012\u0005\u0012\u00030\u00f5\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010&J\u0016\u0010\u00f6\u0002\u001a\t\u0012\u0005\u0012\u00030\u00f7\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010&J\"\u0010\u00f8\u0002\u001a\t\u0012\u0005\u0012\u00030\u00f9\u00020\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00fa\u0002H\u00a7@\u00a2\u0006\u0003\u0010\u00fb\u0002J\"\u0010\u00fc\u0002\u001a\t\u0012\u0005\u0012\u00030\u00fd\u00020\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00fe\u0002H\u00a7@\u00a2\u0006\u0003\u0010\u00ff\u0002JI\u0010\u0080\u0003\u001a\t\u0012\u0005\u0012\u00030\u0081\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u0085\u0003\u001a\t\u0012\u0005\u0012\u00030\u0086\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003J\u0016\u0010\u0087\u0003\u001a\t\u0012\u0005\u0012\u00030\u0088\u00030\u0003H\u00a7@\u00a2\u0006\u0002\u0010&J!\u0010\u0089\u0003\u001a\t\u0012\u0005\u0012\u00030\u008a\u00030\u00032\t\b\u0001\u0010\u0080\u0001\u001a\u00020.H\u00a7@\u00a2\u0006\u0002\u0010IJ,\u0010\u008b\u0003\u001a\t\u0012\u0005\u0012\u00030\u008c\u00030\u00032\b\b\u0001\u0010\u0019\u001a\u00020\u00172\t\b\u0001\u0010\u0083\u0003\u001a\u00020\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u00eb\u0002J#\u0010\u008d\u0003\u001a\t\u0012\u0005\u0012\u00030\u008e\u00030\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J\u0016\u0010\u008f\u0003\u001a\t\u0012\u0005\u0012\u00030\u0090\u00030\u0003H\u00a7@\u00a2\u0006\u0002\u0010&JI\u0010\u0091\u0003\u001a\t\u0012\u0005\u0012\u00030\u0092\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u0093\u0003\u001a\t\u0012\u0005\u0012\u00030\u0094\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003J\u0016\u0010\u0095\u0003\u001a\t\u0012\u0005\u0012\u00030\u0096\u00030\u0003H\u00a7@\u00a2\u0006\u0002\u0010&JI\u0010\u0097\u0003\u001a\t\u0012\u0005\u0012\u00030\u0098\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u0099\u0003\u001a\t\u0012\u0005\u0012\u00030\u009a\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003J6\u0010\u009b\u0003\u001a\t\u0012\u0005\u0012\u00030\u009c\u00030\u00032\b\b\u0001\u0010-\u001a\u00020.2\b\b\u0001\u0010\u0018\u001a\u00020.2\n\b\u0001\u0010T\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0002\u0010UJ\"\u0010\u009d\u0003\u001a\t\u0012\u0005\u0012\u00030\u009e\u00030\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0002\u00107J\"\u0010\u009f\u0003\u001a\t\u0012\u0005\u0012\u00030\u00a0\u00030\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0002\u00107J0\u0010\u00a1\u0003\u001a\t\u0012\u0005\u0012\u00030\u00a2\u00030\u00032\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u00a3\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u00eb\u0002J\"\u0010\u00a4\u0003\u001a\t\u0012\u0005\u0012\u00030\u00a5\u00030\u00032\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0002\u00107J#\u0010\u00a6\u0003\u001a\t\u0012\u0005\u0012\u00030\u00a7\u00030\u00032\u000b\b\u0001\u0010\u00e9\u0002\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0002\u00107JI\u0010\u00a8\u0003\u001a\t\u0012\u0005\u0012\u00030\u00a9\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00aa\u0003\u001a\t\u0012\u0005\u0012\u00030\u00ab\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JI\u0010\u00ac\u0003\u001a\t\u0012\u0005\u0012\u00030\u00ad\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00ae\u0003\u001a\t\u0012\u0005\u0012\u00030\u00af\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003J\"\u0010\u00b0\u0003\u001a\t\u0012\u0005\u0012\u00030\u00b1\u00030\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00b2\u0003H\u00a7@\u00a2\u0006\u0003\u0010\u00b3\u0003JI\u0010\u00b4\u0003\u001a\t\u0012\u0005\u0012\u00030\u00b5\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00b6\u0003\u001a\t\u0012\u0005\u0012\u00030\u00b7\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JI\u0010\u00b8\u0003\u001a\t\u0012\u0005\u0012\u00030\u00b9\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00ba\u0003\u001a\t\u0012\u0005\u0012\u00030\u00bb\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JI\u0010\u00bc\u0003\u001a\t\u0012\u0005\u0012\u00030\u00bd\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00be\u0003\u001a\t\u0012\u0005\u0012\u00030\u00bf\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JI\u0010\u00c0\u0003\u001a\t\u0012\u0005\u0012\u00030\u00c1\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00c2\u0003\u001a\t\u0012\u0005\u0012\u00030\u00c3\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JI\u0010\u00c4\u0003\u001a\t\u0012\u0005\u0012\u00030\u00c5\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00c6\u0003\u001a\t\u0012\u0005\u0012\u00030\u00c7\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003J\"\u0010\u00c8\u0003\u001a\t\u0012\u0005\u0012\u00030\u00c9\u00030\u00032\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0002\u00107JH\u0010\u00ca\u0003\u001a\t\u0012\u0005\u0012\u00030\u00cb\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JI\u0010\u00cc\u0003\u001a\t\u0012\u0005\u0012\u00030\u00cd\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00ce\u0003\u001a\t\u0012\u0005\u0012\u00030\u00cf\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JI\u0010\u00d0\u0003\u001a\t\u0012\u0005\u0012\u00030\u00d1\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00d2\u0003\u001a\t\u0012\u0005\u0012\u00030\u00d3\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JI\u0010\u00d4\u0003\u001a\t\u0012\u0005\u0012\u00030\u00d5\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00d6\u0003\u001a\t\u0012\u0005\u0012\u00030\u00d7\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JI\u0010\u00d8\u0003\u001a\t\u0012\u0005\u0012\u00030\u00d9\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00da\u0003\u001a\t\u0012\u0005\u0012\u00030\u00db\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003J#\u0010\u00dc\u0003\u001a\t\u0012\u0005\u0012\u00030\u00dd\u00030\u00032\u000b\b\u0001\u0010\u00de\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0002\u00107JI\u0010\u00df\u0003\u001a\t\u0012\u0005\u0012\u00030\u00e0\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00e1\u0003\u001a\t\u0012\u0005\u0012\u00030\u00e2\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JI\u0010\u00e3\u0003\u001a\t\u0012\u0005\u0012\u00030\u00e4\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00e5\u0003\u001a\t\u0012\u0005\u0012\u00030\u00e6\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JI\u0010\u00e7\u0003\u001a\t\u0012\u0005\u0012\u00030\u00e8\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00e9\u0003\u001a\t\u0012\u0005\u0012\u00030\u00ea\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003J\"\u0010\u00eb\u0003\u001a\t\u0012\u0005\u0012\u00030\u00ec\u00030\u00032\t\b\u0001\u0010\u00ed\u0003\u001a\u00020\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00ee\u0003JI\u0010\u00ef\u0003\u001a\t\u0012\u0005\u0012\u00030\u00f0\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00f1\u0003\u001a\t\u0012\u0005\u0012\u00030\u00f2\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JI\u0010\u00f3\u0003\u001a\t\u0012\u0005\u0012\u00030\u00f4\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00f5\u0003\u001a\t\u0012\u0005\u0012\u00030\u00f6\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JI\u0010\u00f7\u0003\u001a\t\u0012\u0005\u0012\u00030\u00f8\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00f9\u0003\u001a\t\u0012\u0005\u0012\u00030\u00fa\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JI\u0010\u00fb\u0003\u001a\t\u0012\u0005\u0012\u00030\u00fc\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00fd\u0003\u001a\t\u0012\u0005\u0012\u00030\u00fe\u00030\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003J0\u0010\u00ff\u0003\u001a\t\u0012\u0005\u0012\u00030\u0080\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u0082\u0004\u001a\t\u0012\u0005\u0012\u00030\u0083\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u0084\u0004\u001a\t\u0012\u0005\u0012\u00030\u0085\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u0086\u0004\u001a\t\u0012\u0005\u0012\u00030\u0087\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u0088\u0004\u001a\t\u0012\u0005\u0012\u00030\u0089\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u008a\u0004\u001a\t\u0012\u0005\u0012\u00030\u008b\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u008c\u0004\u001a\t\u0012\u0005\u0012\u00030\u008d\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u008e\u0004\u001a\t\u0012\u0005\u0012\u00030\u008f\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u0090\u0004\u001a\t\u0012\u0005\u0012\u00030\u0091\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u0092\u0004\u001a\t\u0012\u0005\u0012\u00030\u0093\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u0094\u0004\u001a\t\u0012\u0005\u0012\u00030\u0095\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u0096\u0004\u001a\t\u0012\u0005\u0012\u00030\u0097\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u0098\u0004\u001a\t\u0012\u0005\u0012\u00030\u0099\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u009a\u0004\u001a\t\u0012\u0005\u0012\u00030\u009b\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u009c\u0004\u001a\t\u0012\u0005\u0012\u00030\u009d\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u009e\u0004\u001a\t\u0012\u0005\u0012\u00030\u009f\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u00a0\u0004\u001a\t\u0012\u0005\u0012\u00030\u00a1\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u00a2\u0004\u001a\t\u0012\u0005\u0012\u00030\u00a3\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u00a4\u0004\u001a\t\u0012\u0005\u0012\u00030\u00a5\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u00a6\u0004\u001a\t\u0012\u0005\u0012\u00030\u00a7\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u00a8\u0004\u001a\t\u0012\u0005\u0012\u00030\u00a9\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u00aa\u0004\u001a\t\u0012\u0005\u0012\u00030\u00ab\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u00ac\u0004\u001a\t\u0012\u0005\u0012\u00030\u00ad\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u00ae\u0004\u001a\t\u0012\u0005\u0012\u00030\u00af\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u00b0\u0004\u001a\t\u0012\u0005\u0012\u00030\u00b1\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u00b2\u0004\u001a\t\u0012\u0005\u0012\u00030\u00b3\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u00b4\u0004\u001a\t\u0012\u0005\u0012\u00030\u00b5\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u00b6\u0004\u001a\t\u0012\u0005\u0012\u00030\u00b7\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u00b8\u0004\u001a\t\u0012\u0005\u0012\u00030\u00b9\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u00ba\u0004\u001a\t\u0012\u0005\u0012\u00030\u00bb\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u00bc\u0004\u001a\t\u0012\u0005\u0012\u00030\u00bd\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u00be\u0004\u001a\t\u0012\u0005\u0012\u00030\u00bf\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u00c0\u0004\u001a\t\u0012\u0005\u0012\u00030\u00c1\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J0\u0010\u00c2\u0004\u001a\t\u0012\u0005\u0012\u00030\u00c3\u00040\u00032\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.2\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0081\u0004J!\u0010\u00c4\u0004\u001a\t\u0012\u0005\u0012\u00030\u00c5\u00040\u00032\t\b\u0001\u0010\u00e9\u0002\u001a\u00020.H\u00a7@\u00a2\u0006\u0002\u0010IJI\u0010\u00c6\u0004\u001a\t\u0012\u0005\u0012\u00030\u00c7\u00040\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00c8\u0004\u001a\t\u0012\u0005\u0012\u00030\u00c9\u00040\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JI\u0010\u00ca\u0004\u001a\t\u0012\u0005\u0012\u00030\u00cb\u00040\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00cc\u0004\u001a\t\u0012\u0005\u0012\u00030\u00cd\u00040\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003J\u0016\u0010\u00ce\u0004\u001a\t\u0012\u0005\u0012\u00030\u00cf\u00040\u0003H\u00a7@\u00a2\u0006\u0002\u0010&JI\u0010\u00d0\u0004\u001a\t\u0012\u0005\u0012\u00030\u00d1\u00040\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00d2\u0004\u001a\t\u0012\u0005\u0012\u00030\u00d3\u00040\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JI\u0010\u00d4\u0004\u001a\t\u0012\u0005\u0012\u00030\u00d5\u00040\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00d6\u0004\u001a\t\u0012\u0005\u0012\u00030\u00d7\u00040\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JI\u0010\u00d8\u0004\u001a\t\u0012\u0005\u0012\u00030\u00d9\u00040\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00da\u0004\u001a\t\u0012\u0005\u0012\u00030\u00db\u00040\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003J\"\u0010\u00dc\u0004\u001a\t\u0012\u0005\u0012\u00030\u00dd\u00040\u00032\t\b\u0001\u0010\u0080\u0001\u001a\u00020\u0001H\u00a7@\u00a2\u0006\u0003\u0010\u00ee\u0003J\u0016\u0010\u00de\u0004\u001a\t\u0012\u0005\u0012\u00030\u00df\u00040\u0003H\u00a7@\u00a2\u0006\u0002\u0010&J!\u0010\u00e0\u0004\u001a\t\u0012\u0005\u0012\u00030\u00e1\u00040\u00032\t\b\u0001\u0010\u0080\u0001\u001a\u00020.H\u00a7@\u00a2\u0006\u0002\u0010IJ1\u0010\u00e2\u0004\u001a\t\u0012\u0005\u0012\u00030\u00e3\u00040\u00032\u000b\b\u0001\u0010\u00e4\u0004\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u00e5\u0004\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u00eb\u0002JI\u0010\u00e6\u0004\u001a\t\u0012\u0005\u0012\u00030\u00e7\u00040\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00e8\u0004\u001a\t\u0012\u0005\u0012\u00030\u00e9\u00040\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JI\u0010\u00ea\u0004\u001a\t\u0012\u0005\u0012\u00030\u00eb\u00040\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00ec\u0004\u001a\t\u0012\u0005\u0012\u00030\u00ed\u00040\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003J!\u0010\u00ee\u0004\u001a\t\u0012\u0005\u0012\u00030\u00ef\u00040\u00032\t\b\u0001\u0010\u0080\u0001\u001a\u00020.H\u00a7@\u00a2\u0006\u0002\u0010IJ/\u0010\u00f0\u0004\u001a\t\u0012\u0005\u0012\u00030\u00f1\u00040\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u00eb\u0002JI\u0010\u00f2\u0004\u001a\t\u0012\u0005\u0012\u00030\u00f3\u00040\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u00f4\u0004\u001a\t\u0012\u0005\u0012\u00030\u00f5\u00040\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003J\"\u0010\u00f6\u0004\u001a\t\u0012\u0005\u0012\u00030\u00f7\u00040\u00032\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0002\u00107J$\u0010\u00f8\u0004\u001a\t\u0012\u0005\u0012\u00030\u00f9\u00040\u00032\u000b\b\u0001\u0010\u00de\u0003\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0093\u0002J\"\u0010\u00fa\u0004\u001a\t\u0012\u0005\u0012\u00030\u00fb\u00040\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00fc\u0004H\u00a7@\u00a2\u0006\u0003\u0010\u00fd\u0004J \u0010\u00fe\u0004\u001a\t\u0012\u0005\u0012\u00030\u00ff\u00040\u00032\b\b\u0001\u0010]\u001a\u00020.H\u00a7@\u00a2\u0006\u0002\u0010IJI\u0010\u0080\u0005\u001a\t\u0012\u0005\u0012\u00030\u0081\u00050\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u0082\u0005\u001a\t\u0012\u0005\u0012\u00030\u0083\u00050\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JI\u0010\u0084\u0005\u001a\t\u0012\u0005\u0012\u00030\u0085\u00050\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u0086\u0005\u001a\t\u0012\u0005\u0012\u00030\u0087\u00050\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JI\u0010\u0088\u0005\u001a\t\u0012\u0005\u0012\u00030\u0089\u00050\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u008a\u0005\u001a\t\u0012\u0005\u0012\u00030\u008b\u00050\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JI\u0010\u008c\u0005\u001a\t\u0012\u0005\u0012\u00030\u008d\u00050\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0082\u0003\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003JH\u0010\u008e\u0005\u001a\t\u0012\u0005\u0012\u00030\u008f\u00050\u00032\n\b\u0001\u0010\u0016\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0018\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010\u0019\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u0083\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0003J!\u0010\u0090\u0005\u001a\t\u0012\u0005\u0012\u00030\u0091\u00050\u00032\t\b\u0001\u0010\u00e9\u0002\u001a\u00020.H\u00a7@\u00a2\u0006\u0002\u0010IJ\"\u0010\u0092\u0005\u001a\t\u0012\u0005\u0012\u00030\u0093\u00050\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u0094\u0005H\u00a7@\u00a2\u0006\u0003\u0010\u0095\u0005J!\u0010\u0096\u0005\u001a\t\u0012\u0005\u0012\u00030\u0097\u00050\u00032\t\b\u0001\u0010\u0098\u0005\u001a\u00020\u0017H\u00a7@\u00a2\u0006\u0002\u00107J?\u0010\u0099\u0005\u001a\t\u0012\u0005\u0012\u00030\u009a\u00050\u00032\u000b\b\u0001\u0010\u009b\u0005\u001a\u0004\u0018\u00010\u00172\f\b\u0001\u0010\u009c\u0005\u001a\u0005\u0018\u00010\u009d\u00052\u000b\b\u0001\u0010\u009e\u0005\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u009f\u0005J?\u0010\u00a0\u0005\u001a\t\u0012\u0005\u0012\u00030\u00a1\u00050\u00032\u000b\b\u0001\u0010\u009b\u0005\u001a\u0004\u0018\u00010\u00172\u000b\b\u0001\u0010\u00a2\u0005\u001a\u0004\u0018\u00010\u00172\f\b\u0001\u0010\u009c\u0005\u001a\u0005\u0018\u00010\u009d\u0005H\u00a7@\u00a2\u0006\u0003\u0010\u00a3\u0005J \u0010\u00a4\u0005\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\t\b\u0001\u0010\u0080\u0001\u001a\u00020.H\u00a7@\u00a2\u0006\u0002\u0010IJ\u0016\u0010\u00a5\u0005\u001a\t\u0012\u0005\u0012\u00030\u00a6\u00050\u0003H\u00a7@\u00a2\u0006\u0002\u0010&J\"\u0010\u00a7\u0005\u001a\t\u0012\u0005\u0012\u00030\u00a8\u00050\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00a9\u0005H\u00a7@\u00a2\u0006\u0003\u0010\u00aa\u0005J\"\u0010\u00ab\u0005\u001a\t\u0012\u0005\u0012\u00030\u00ac\u00050\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00ad\u0005H\u00a7@\u00a2\u0006\u0003\u0010\u00ae\u0005J\"\u0010\u00af\u0005\u001a\t\u0012\u0005\u0012\u00030\u00b0\u00050\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00b1\u0005H\u00a7@\u00a2\u0006\u0003\u0010\u00b2\u0005J\"\u0010\u00b3\u0005\u001a\t\u0012\u0005\u0012\u00030\u00b4\u00050\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00b5\u0005H\u00a7@\u00a2\u0006\u0003\u0010\u00b6\u0005J\"\u0010\u00b7\u0005\u001a\t\u0012\u0005\u0012\u00030\u00b8\u00050\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00b9\u0005H\u00a7@\u00a2\u0006\u0003\u0010\u00ba\u0005J\u0016\u0010\u00bb\u0005\u001a\t\u0012\u0005\u0012\u00030\u00bc\u00050\u0003H\u00a7@\u00a2\u0006\u0002\u0010&J\"\u0010\u00bd\u0005\u001a\t\u0012\u0005\u0012\u00030\u00be\u00050\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00bf\u0005H\u00a7@\u00a2\u0006\u0003\u0010\u00c0\u0005J\u0016\u0010\u00c1\u0005\u001a\t\u0012\u0005\u0012\u00030\u00c2\u00050\u0003H\u00a7@\u00a2\u0006\u0002\u0010&J\"\u0010\u00c3\u0005\u001a\t\u0012\u0005\u0012\u00030\u00c4\u00050\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00c5\u0005H\u00a7@\u00a2\u0006\u0003\u0010\u00c6\u0005J\"\u0010\u00c7\u0005\u001a\t\u0012\u0005\u0012\u00030\u00c8\u00050\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00c9\u0005H\u00a7@\u00a2\u0006\u0003\u0010\u00ca\u0005J\"\u0010\u00cb\u0005\u001a\t\u0012\u0005\u0012\u00030\u00cc\u00050\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00cd\u0005H\u00a7@\u00a2\u0006\u0003\u0010\u00ce\u0005J\"\u0010\u00cf\u0005\u001a\t\u0012\u0005\u0012\u00030\u00d0\u00050\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00d1\u0005H\u00a7@\u00a2\u0006\u0003\u0010\u00d2\u0005J\"\u0010\u00d3\u0005\u001a\t\u0012\u0005\u0012\u00030\u00d4\u00050\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00d5\u0005H\u00a7@\u00a2\u0006\u0003\u0010\u00d6\u0005J\"\u0010\u00d7\u0005\u001a\t\u0012\u0005\u0012\u00030\u00d8\u00050\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00d9\u0005H\u00a7@\u00a2\u0006\u0003\u0010\u00da\u0005J0\u0010\u00db\u0005\u001a\t\u0012\u0005\u0012\u00030\u00dc\u00050\u00032\u000b\b\u0001\u0010\u00dd\u0005\u001a\u0004\u0018\u00010\u00172\n\b\u0001\u0010-\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00e6\u0002J!\u0010\u00de\u0005\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00df\u0005H\u00a7@\u00a2\u0006\u0003\u0010\u00e0\u0005J\"\u0010\u00e1\u0005\u001a\t\u0012\u0005\u0012\u00030\u00e2\u00050\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00e3\u0005H\u00a7@\u00a2\u0006\u0003\u0010\u00e4\u0005J!\u0010\u00e1\u0005\u001a\t\u0012\u0005\u0012\u00030\u00e2\u00050\u00032\t\b\u0001\u0010\u00e5\u0005\u001a\u00020\u0017H\u00a7@\u00a2\u0006\u0002\u00107J\"\u0010\u00e6\u0005\u001a\t\u0012\u0005\u0012\u00030\u00e7\u00050\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00e8\u0005H\u00a7@\u00a2\u0006\u0003\u0010\u00e9\u0005J!\u0010\u00ea\u0005\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00eb\u0005H\u00a7@\u00a2\u0006\u0003\u0010\u00ec\u0005J/\u0010\u00ed\u0005\u001a\t\u0012\u0005\u0012\u00030\u00ee\u00050\u00032\t\b\u0001\u0010\u0080\u0001\u001a\u00020.2\u000b\b\u0001\u0010\u00ef\u0005\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u00f0\u0005J!\u0010\u00f1\u0005\u001a\t\u0012\u0005\u0012\u00030\u00f2\u00050\u00032\t\b\u0001\u0010\u0080\u0001\u001a\u00020.H\u00a7@\u00a2\u0006\u0002\u0010IJ!\u0010\u00f3\u0005\u001a\t\u0012\u0005\u0012\u00030\u00f4\u00050\u00032\t\b\u0001\u0010\u0080\u0001\u001a\u00020.H\u00a7@\u00a2\u0006\u0002\u0010IJ\"\u0010\u00f5\u0005\u001a\t\u0012\u0005\u0012\u00030\u00f6\u00050\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00f7\u0005H\u00a7@\u00a2\u0006\u0003\u0010\u00f8\u0005J\"\u0010\u00f9\u0005\u001a\t\u0012\u0005\u0012\u00030\u00fa\u00050\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00fb\u0005H\u00a7@\u00a2\u0006\u0003\u0010\u00fc\u0005J\"\u0010\u00fd\u0005\u001a\t\u0012\u0005\u0012\u00030\u00fe\u00050\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00ff\u0005H\u00a7@\u00a2\u0006\u0003\u0010\u0080\u0006J\"\u0010\u0081\u0006\u001a\t\u0012\u0005\u0012\u00030\u0082\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u0083\u0006H\u00a7@\u00a2\u0006\u0003\u0010\u0084\u0006J\"\u0010\u0085\u0006\u001a\t\u0012\u0005\u0012\u00030\u0086\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u0087\u0006H\u00a7@\u00a2\u0006\u0003\u0010\u0088\u0006J\"\u0010\u0089\u0006\u001a\t\u0012\u0005\u0012\u00030\u008a\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u008b\u0006H\u00a7@\u00a2\u0006\u0003\u0010\u008c\u0006J\"\u0010\u008d\u0006\u001a\t\u0012\u0005\u0012\u00030\u008e\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u008f\u0006H\u00a7@\u00a2\u0006\u0003\u0010\u0090\u0006J.\u0010\u0091\u0006\u001a\t\u0012\u0005\u0012\u00030\u0092\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u0093\u00062\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0094\u0006J.\u0010\u0095\u0006\u001a\t\u0012\u0005\u0012\u00030\u0096\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u0097\u00062\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0098\u0006J.\u0010\u0099\u0006\u001a\t\u0012\u0005\u0012\u00030\u009a\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u009b\u00062\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u009c\u0006J\"\u0010\u009d\u0006\u001a\t\u0012\u0005\u0012\u00030\u009e\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u009f\u0006H\u00a7@\u00a2\u0006\u0003\u0010\u00a0\u0006J.\u0010\u00a1\u0006\u001a\t\u0012\u0005\u0012\u00030\u00a2\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00a3\u00062\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0003\u0010\u00a4\u0006J.\u0010\u00a5\u0006\u001a\t\u0012\u0005\u0012\u00030\u00a6\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00a7\u00062\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00a8\u0006J.\u0010\u00a9\u0006\u001a\t\u0012\u0005\u0012\u00030\u00aa\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00ab\u00062\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00ac\u0006J.\u0010\u00ad\u0006\u001a\t\u0012\u0005\u0012\u00030\u00ae\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00af\u00062\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00b0\u0006J!\u0010\u00b1\u0006\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00b2\u0006H\u00a7@\u00a2\u0006\u0003\u0010\u00b3\u0006J.\u0010\u00b4\u0006\u001a\t\u0012\u0005\u0012\u00030\u00b5\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00b6\u00062\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00b7\u0006J.\u0010\u00b8\u0006\u001a\t\u0012\u0005\u0012\u00030\u00b9\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00ba\u00062\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00bb\u0006J-\u0010\u00bc\u0006\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u0097\u00012\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00bd\u0006J.\u0010\u00be\u0006\u001a\t\u0012\u0005\u0012\u00030\u00bf\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00c0\u00062\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00c1\u0006J.\u0010\u00c2\u0006\u001a\t\u0012\u0005\u0012\u00030\u00c3\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00c4\u00062\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00c5\u0006J.\u0010\u00c6\u0006\u001a\t\u0012\u0005\u0012\u00030\u00c7\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00c8\u00062\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00c9\u0006J-\u0010\u00ca\u0006\u001a\t\u0012\u0005\u0012\u00030\u00cb\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00cc\u00062\t\b\u0001\u0010\u0080\u0001\u001a\u00020.H\u00a7@\u00a2\u0006\u0003\u0010\u00cd\u0006J.\u0010\u00ca\u0006\u001a\t\u0012\u0005\u0012\u00030\u00cb\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00cc\u00062\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00ce\u0006J.\u0010\u00cf\u0006\u001a\t\u0012\u0005\u0012\u00030\u00d0\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00d1\u00062\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00d2\u0006J.\u0010\u00d3\u0006\u001a\t\u0012\u0005\u0012\u00030\u00d4\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00d5\u00062\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00d6\u0006J.\u0010\u00d7\u0006\u001a\t\u0012\u0005\u0012\u00030\u00d8\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00d9\u00062\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00da\u0006J.\u0010\u00db\u0006\u001a\t\u0012\u0005\u0012\u00030\u00dc\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00dd\u00062\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00de\u0006J.\u0010\u00df\u0006\u001a\t\u0012\u0005\u0012\u00030\u00e0\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00e1\u00062\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00e2\u0006J,\u0010\u00e3\u0006\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\t\b\u0001\u0010\u00e4\u0006\u001a\u00020.2\t\b\u0001\u0010\u0005\u001a\u00030\u00e5\u0006H\u00a7@\u00a2\u0006\u0003\u0010\u00e6\u0006J.\u0010\u00e7\u0006\u001a\t\u0012\u0005\u0012\u00030\u00e8\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00e9\u00062\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00ea\u0006J.\u0010\u00eb\u0006\u001a\t\u0012\u0005\u0012\u00030\u00ec\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00ed\u00062\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00ee\u0006J.\u0010\u00ef\u0006\u001a\t\u0012\u0005\u0012\u00030\u00f0\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00f1\u00062\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00f2\u0006J,\u0010\u00ef\u0006\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\b\b\u0001\u0010\u0005\u001a\u00020\r2\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00f3\u0006J.\u0010\u00f4\u0006\u001a\t\u0012\u0005\u0012\u00030\u00f5\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00f6\u00062\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00f7\u0006J-\u0010\u00f4\u0006\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00cb\u00012\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00f8\u0006J-\u0010\u00f9\u0006\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00fa\u00062\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00fb\u0006J.\u0010\u00fc\u0006\u001a\t\u0012\u0005\u0012\u00030\u00fd\u00060\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00fe\u00062\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00ff\u0006J.\u0010\u0080\u0007\u001a\t\u0012\u0005\u0012\u00030\u0081\u00070\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u0082\u00072\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0083\u0007J.\u0010\u0084\u0007\u001a\t\u0012\u0005\u0012\u00030\u0085\u00070\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u0086\u00072\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0087\u0007J-\u0010\u0088\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u0089\u00072\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u008a\u0007J.\u0010\u008b\u0007\u001a\t\u0012\u0005\u0012\u00030\u008c\u00070\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u008d\u00072\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u008e\u0007J.\u0010\u008f\u0007\u001a\t\u0012\u0005\u0012\u00030\u0090\u00070\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u0091\u00072\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0092\u0007J.\u0010\u0093\u0007\u001a\t\u0012\u0005\u0012\u00030\u0094\u00070\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u0095\u00072\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u0096\u0007J.\u0010\u0097\u0007\u001a\t\u0012\u0005\u0012\u00030\u0098\u00070\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u0099\u00072\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u009a\u0007J.\u0010\u009b\u0007\u001a\t\u0012\u0005\u0012\u00030\u009c\u00070\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u009d\u00072\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u009e\u0007J,\u0010\u009f\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\b\b\u0001\u0010\u0005\u001a\u00020\r2\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00f3\u0006J.\u0010\u00a0\u0007\u001a\t\u0012\u0005\u0012\u00030\u00a1\u00070\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00a2\u00072\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00a3\u0007J.\u0010\u00a4\u0007\u001a\t\u0012\u0005\u0012\u00030\u00a5\u00070\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00a6\u00072\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00a7\u0007J.\u0010\u00a8\u0007\u001a\t\u0012\u0005\u0012\u00030\u00a9\u00070\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00aa\u00072\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00ab\u0007J.\u0010\u00ac\u0007\u001a\t\u0012\u0005\u0012\u00030\u00ad\u00070\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00ae\u00072\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00af\u0007J.\u0010\u00b0\u0007\u001a\t\u0012\u0005\u0012\u00030\u00b1\u00070\u00032\t\b\u0001\u0010\u0005\u001a\u00030\u00b2\u00072\n\b\u0001\u0010\u001e\u001a\u0004\u0018\u00010.H\u00a7@\u00a2\u0006\u0003\u0010\u00b3\u0007J#\u0010\u00b4\u0007\u001a\t\u0012\u0005\u0012\u00030\u00b5\u00070\u00032\n\b\u0001\u0010\u00b6\u0007\u001a\u00030\u00b7\u0007H\u00a7@\u00a2\u0006\u0003\u0010\u00b8\u0007J#\u0010\u00b9\u0007\u001a\t\u0012\u0005\u0012\u00030\u00ba\u00070\u00032\n\b\u0001\u0010\u00b6\u0007\u001a\u00030\u00b7\u0007H\u00a7@\u00a2\u0006\u0003\u0010\u00b8\u0007J#\u0010\u00bb\u0007\u001a\t\u0012\u0005\u0012\u00030\u00bc\u00070\u00032\u000b\b\u0001\u0010\u00de\u0003\u001a\u0004\u0018\u00010\u0017H\u00a7@\u00a2\u0006\u0002\u00107\u00a8\u0006\u00bd\u0007"}, d2 = {"Lcom/manaknight/app/network/ApiService;", "", "addEcomProductLambda", "Lretrofit2/Response;", "Lcom/manaknight/app/model/remote/AddEcomProductLambdaResponse;", "request", "Lcom/manaknight/app/model/remote/AddEcomProductLambdaRequest;", "(Lcom/manaknight/app/model/remote/AddEcomProductLambdaRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addLineItem", "Lcom/manaknight/app/model/remote/profitPro/CommonResponse;", "Lcom/manaknight/app/model/remote/profitPro/CreateLineItemReqModel;", "(Lcom/manaknight/app/model/remote/profitPro/CreateLineItemReqModel;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addLinealFootCost", "Lcom/manaknight/app/model/remote/profitPro/LinearFootReqModel;", "(Lcom/manaknight/app/model/remote/profitPro/LinearFootReqModel;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addSquareFootCost", "analyticsLog", "Lcom/manaknight/app/model/remote/AnalyticsLogResponse;", "Lcom/manaknight/app/model/remote/AnalyticsLogRequest;", "(Lcom/manaknight/app/model/remote/AnalyticsLogRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "appAlertsList", "Lcom/manaknight/app/model/remote/AppAlertsListResponse;", "order", "", "page", "filter", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "appAlertsUpdate", "Lcom/manaknight/app/model/remote/AppAlertsUpdateResponse;", "Lcom/manaknight/app/model/remote/AppAlertsUpdateRequest;", "id", "(Lcom/manaknight/app/model/remote/AppAlertsUpdateRequest;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "appleAuthCode", "Lcom/manaknight/app/model/remote/AppleAuthCodeResponse;", "Lcom/manaknight/app/model/remote/AppleAuthCodeRequest;", "(Lcom/manaknight/app/model/remote/AppleAuthCodeRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "appleLogin", "Lcom/manaknight/app/model/remote/AppleLoginResponse;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "appleLoginMobileEndpoint", "Lcom/manaknight/app/model/remote/AppleLoginMobileEndpointResponse;", "Lcom/manaknight/app/model/remote/AppleLoginMobileEndpointRequest;", "(Lcom/manaknight/app/model/remote/AppleLoginMobileEndpointRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "blogAll", "Lcom/manaknight/app/model/remote/BlogAllResponse;", "limit", "", "offset", "(IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "blogCreate", "Lcom/manaknight/app/model/remote/BlogCreateResponse;", "Lcom/manaknight/app/model/remote/BlogCreateRequest;", "(Lcom/manaknight/app/model/remote/BlogCreateRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "blogDelete", "Lcom/manaknight/app/model/remote/BlogDeleteResponse;", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "blogEdit", "Lcom/manaknight/app/model/remote/BlogEditResponse;", "Lcom/manaknight/app/model/remote/BlogEditRequest;", "(Lcom/manaknight/app/model/remote/BlogEditRequest;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "blogFilter", "Lcom/manaknight/app/model/remote/BlogFilterResponse;", "categories", "Ljava/util/ArrayList;", "", "Lkotlin/collections/ArrayList;", "tags", "rule", "search", "(Ljava/util/ArrayList;Ljava/util/ArrayList;Ljava/lang/String;Ljava/lang/String;IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "blogSimilar", "Lcom/manaknight/app/model/remote/BlogSimilarResponse;", "top", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "blogSingle", "Lcom/manaknight/app/model/remote/BlogSingleResponse;", "blogTags", "Lcom/manaknight/app/model/remote/BlogTagsResponse;", "Lcom/manaknight/app/model/remote/BlogTagsRequest;", "(Lcom/manaknight/app/model/remote/BlogTagsRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "blogTagsDeleteByID", "Lcom/manaknight/app/model/remote/BlogTagsDeleteByIDResponse;", "blogTagsRetrieve", "Lcom/manaknight/app/model/remote/BlogTagsRetrieveResponse;", "name", "(IILjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "blogTagsUpdate", "Lcom/manaknight/app/model/remote/BlogTagsUpdateResponse;", "Lcom/manaknight/app/model/remote/BlogTagsUpdateRequest;", "(Lcom/manaknight/app/model/remote/BlogTagsUpdateRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "cancelSubscription", "Lcom/manaknight/app/model/remote/CancelSubscriptionResponse;", "Lcom/manaknight/app/model/remote/CancelSubscriptionRequest;", "subscriptionId", "(Lcom/manaknight/app/model/remote/CancelSubscriptionRequest;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "captchaGenerate", "Lcom/manaknight/app/model/remote/CaptchaGenerateResponse;", "width", "height", "captchaTest", "Lcom/manaknight/app/model/remote/CaptchaTestResponse;", "companyDetails", "Lcom/manaknight/app/model/remote/CompanyDetailsResponse;", "companyOverview", "Lcom/manaknight/app/model/remote/CompanyOverviewResponse;", "createAlerts", "Lcom/manaknight/app/model/remote/CreateAlertsResponse;", "Lcom/manaknight/app/model/remote/CreateAlertsRequest;", "(Lcom/manaknight/app/model/remote/CreateAlertsRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createAnalyticLog", "Lcom/manaknight/app/model/remote/CreateAnalyticLogResponse;", "Lcom/manaknight/app/model/remote/CreateAnalyticLogRequest;", "(Lcom/manaknight/app/model/remote/CreateAnalyticLogRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createApiKeys", "Lcom/manaknight/app/model/remote/CreateApiKeysResponse;", "Lcom/manaknight/app/model/remote/CreateApiKeysRequest;", "(Lcom/manaknight/app/model/remote/CreateApiKeysRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createBlogCategory", "Lcom/manaknight/app/model/remote/CreateBlogCategoryResponse;", "Lcom/manaknight/app/model/remote/CreateBlogCategoryRequest;", "(Lcom/manaknight/app/model/remote/CreateBlogCategoryRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createCMSLambda", "Lcom/manaknight/app/model/remote/CreateCMSLambdaResponse;", "Lcom/manaknight/app/model/remote/CreateCMSLambdaRequest;", "(Lcom/manaknight/app/model/remote/CreateCMSLambdaRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createChangeOrder", "Lcom/manaknight/app/model/remote/CreateChangeOrderResponse;", "Lcom/manaknight/app/model/remote/CreateChangeOrderRequest;", "projectId", "(Lcom/manaknight/app/model/remote/CreateChangeOrderRequest;Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createChangeOrderDescription", "Lcom/manaknight/app/model/remote/CreateChangeOrderDescriptionResponse;", "Lcom/manaknight/app/model/remote/CreateChangeOrderDescriptionRequest;", "(Lcom/manaknight/app/model/remote/CreateChangeOrderDescriptionRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createChat", "Lcom/manaknight/app/model/remote/CreateChatResponse;", "Lcom/manaknight/app/model/remote/CreateChatRequest;", "(Lcom/manaknight/app/model/remote/CreateChatRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createCms", "Lcom/manaknight/app/model/remote/CreateCmsResponse;", "Lcom/manaknight/app/model/remote/CreateCmsRequest;", "(Lcom/manaknight/app/model/remote/CreateCmsRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createCompanySettings", "Lcom/manaknight/app/model/remote/CreateCompanySettingsResponse;", "Lcom/manaknight/app/model/remote/CreateCompanySettingsRequest;", "(Lcom/manaknight/app/model/remote/CreateCompanySettingsRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createCost", "Lcom/manaknight/app/model/remote/CreateCostResponse;", "Lcom/manaknight/app/model/remote/CreateCostRequest;", "(Lcom/manaknight/app/model/remote/CreateCostRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createCustomer", "Lcom/manaknight/app/model/remote/profitPro/CustomerModel;", "(Lcom/manaknight/app/model/remote/profitPro/CustomerModel;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createDefaultLinealFootCost", "Lcom/manaknight/app/model/remote/CreateDefaultLinealFootCostResponse;", "Lcom/manaknight/app/model/remote/CreateDefaultLinealFootCostRequest;", "(Lcom/manaknight/app/model/remote/CreateDefaultLinealFootCostRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createDefaultMaterial", "Lcom/manaknight/app/model/remote/profitPro/MaterialReqModel;", "(Lcom/manaknight/app/model/remote/profitPro/MaterialReqModel;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createDefaultSquareFootCost", "Lcom/manaknight/app/model/remote/CreateDefaultSquareFootCostResponse;", "Lcom/manaknight/app/model/remote/CreateDefaultSquareFootCostRequest;", "(Lcom/manaknight/app/model/remote/CreateDefaultSquareFootCostRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createDraw", "Lcom/manaknight/app/model/remote/CreateDrawRequest;", "(Lcom/manaknight/app/model/remote/CreateDrawRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createEmail", "Lcom/manaknight/app/model/remote/CreateEmailResponse;", "Lcom/manaknight/app/model/remote/CreateEmailRequest;", "(Lcom/manaknight/app/model/remote/CreateEmailRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createEmployee", "Lcom/manaknight/app/model/remote/CreateEmployeeResponse;", "Lcom/manaknight/app/model/remote/CreateEmployeeRequest;", "(Lcom/manaknight/app/model/remote/CreateEmployeeRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createInvoice", "Lcom/manaknight/app/model/remote/CreateInvoiceResponse;", "Lcom/manaknight/app/model/remote/CreateInvoiceRequest;", "(Lcom/manaknight/app/model/remote/CreateInvoiceRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createJob", "Lcom/manaknight/app/model/remote/CreateJobResponse;", "Lcom/manaknight/app/model/remote/CreateJobRequest;", "(Lcom/manaknight/app/model/remote/CreateJobRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createLabor", "Lcom/manaknight/app/model/remote/CreateLaborResponse;", "Lcom/manaknight/app/model/remote/CreateLaborRequest;", "(Lcom/manaknight/app/model/remote/CreateLaborRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createLineItemEntry", "Lcom/manaknight/app/model/remote/CreateLineItemEntryResponse;", "Lcom/manaknight/app/model/remote/CreateLineItemEntryRequest;", "(Lcom/manaknight/app/model/remote/CreateLineItemEntryRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createLineItems", "Lcom/manaknight/app/model/remote/CreateLineItemsResponse;", "Lcom/manaknight/app/model/remote/CreateLineItemsRequest;", "(Lcom/manaknight/app/model/remote/CreateLineItemsRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createLinealFootCost", "Lcom/manaknight/app/model/remote/CreateLinealFootCostResponse;", "Lcom/manaknight/app/model/remote/CreateLinealFootCostRequest;", "(Lcom/manaknight/app/model/remote/CreateLinealFootCostRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createMaterial", "Lcom/manaknight/app/model/remote/CreateMaterialResponse;", "Lcom/manaknight/app/model/remote/CreateMaterialRequest;", "(Lcom/manaknight/app/model/remote/CreateMaterialRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Lcom/manaknight/app/model/remote/profitPro/MaterialRequestModel;", "(Lcom/manaknight/app/model/remote/profitPro/MaterialRequestModel;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createNewEstimation", "Lcom/manaknight/app/model/remote/profitPro/ProjectModel;", "(Lcom/manaknight/app/model/remote/profitPro/ProjectModel;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createPercentageDraw", "Lcom/manaknight/app/model/remote/profitPro/CreatePercentageDrawRequest;", "(Lcom/manaknight/app/model/remote/profitPro/CreatePercentageDrawRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createPermission", "Lcom/manaknight/app/model/remote/CreatePermissionResponse;", "Lcom/manaknight/app/model/remote/CreatePermissionRequest;", "(Lcom/manaknight/app/model/remote/CreatePermissionRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createPhoto", "Lcom/manaknight/app/model/remote/CreatePhotoResponse;", "Lcom/manaknight/app/model/remote/CreatePhotoRequest;", "(Lcom/manaknight/app/model/remote/CreatePhotoRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createPosts", "Lcom/manaknight/app/model/remote/CreatePostsResponse;", "Lcom/manaknight/app/model/remote/CreatePostsRequest;", "(Lcom/manaknight/app/model/remote/CreatePostsRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createPriceDraw", "Lcom/manaknight/app/model/remote/profitPro/CreatePriceDrawRequest;", "(Lcom/manaknight/app/model/remote/profitPro/CreatePriceDrawRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createProfile", "Lcom/manaknight/app/model/remote/CreateProfileResponse;", "Lcom/manaknight/app/model/remote/CreateProfileRequest;", "(Lcom/manaknight/app/model/remote/CreateProfileRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createProject", "Lcom/manaknight/app/model/remote/CreateProjectResponse;", "Lcom/manaknight/app/model/remote/CreateProjectRequest;", "(Lcom/manaknight/app/model/remote/CreateProjectRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createRoom", "Lcom/manaknight/app/model/remote/CreateRoomResponse;", "Lcom/manaknight/app/model/remote/CreateRoomRequest;", "(Lcom/manaknight/app/model/remote/CreateRoomRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createRoomRequests", "Lcom/manaknight/app/model/remote/CreateRoomRequests;", "(Lcom/manaknight/app/model/remote/CreateRoomRequests;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createSetting", "Lcom/manaknight/app/model/remote/CreateSettingResponse;", "Lcom/manaknight/app/model/remote/CreateSettingRequest;", "(Lcom/manaknight/app/model/remote/CreateSettingRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createSqftCosts", "Lcom/manaknight/app/model/remote/CreateSqftCostsResponse;", "Lcom/manaknight/app/model/remote/CreateSqftCostsRequest;", "(Lcom/manaknight/app/model/remote/CreateSqftCostsRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createSubscription", "Lcom/manaknight/app/model/remote/CreateSubscriptionResponse;", "Lcom/manaknight/app/model/remote/CreateSubscriptionRequest;", "(Lcom/manaknight/app/model/remote/CreateSubscriptionRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createTeamMember", "Lcom/manaknight/app/model/remote/CreateTeamMemberResponse;", "Lcom/manaknight/app/model/remote/CreateTeamMemberRequest;", "(Lcom/manaknight/app/model/remote/CreateTeamMemberRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createToken", "Lcom/manaknight/app/model/remote/CreateTokenResponse;", "Lcom/manaknight/app/model/remote/CreateTokenRequest;", "(Lcom/manaknight/app/model/remote/CreateTokenRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createTriggerType", "Lcom/manaknight/app/model/remote/CreateTriggerTypeResponse;", "Lcom/manaknight/app/model/remote/CreateTriggerTypeRequest;", "(Lcom/manaknight/app/model/remote/CreateTriggerTypeRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createUser", "Lcom/manaknight/app/model/remote/CreateUserResponse;", "Lcom/manaknight/app/model/remote/CreateUserRequest;", "(Lcom/manaknight/app/model/remote/CreateUserRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createUserSessionsAnalytics", "Lcom/manaknight/app/model/remote/CreateUserSessionsAnalyticsResponse;", "Lcom/manaknight/app/model/remote/CreateUserSessionsAnalyticsRequest;", "(Lcom/manaknight/app/model/remote/CreateUserSessionsAnalyticsRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteAlerts", "Lcom/manaknight/app/model/remote/DeleteAlertsResponse;", "(Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteAnalyticLog", "Lcom/manaknight/app/model/remote/DeleteAnalyticLogResponse;", "deleteApiKeys", "Lcom/manaknight/app/model/remote/DeleteApiKeysResponse;", "deleteBlogCategory", "Lcom/manaknight/app/model/remote/DeleteBlogCategoryResponse;", "deleteCMSLambda", "Lcom/manaknight/app/model/remote/DeleteCMSLambdaResponse;", "deleteChangeOrderDescription", "Lcom/manaknight/app/model/remote/DeleteChangeOrderDescriptionResponse;", "deleteChat", "Lcom/manaknight/app/model/remote/DeleteChatResponse;", "deleteCms", "Lcom/manaknight/app/model/remote/DeleteCmsResponse;", "deleteCompanySettings", "Lcom/manaknight/app/model/remote/DeleteCompanySettingsResponse;", "deleteCost", "Lcom/manaknight/app/model/remote/DeleteCostResponse;", "deleteCustomer", "Lcom/manaknight/app/model/remote/DeleteCustomerResponse;", "deleteDefaultLinealFootCost", "Lcom/manaknight/app/model/remote/DeleteDefaultLinealFootCostResponse;", "deleteDefaultMaterial", "Lcom/manaknight/app/model/remote/DeleteDefaultMaterialResponse;", "deleteDefaultSquareFootCost", "Lcom/manaknight/app/model/remote/DeleteDefaultSquareFootCostResponse;", "deleteDraws", "deleteEcomProductLambda", "Lcom/manaknight/app/model/remote/DeleteEcomProductLambdaResponse;", "", "(Ljava/lang/Number;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteEmail", "Lcom/manaknight/app/model/remote/DeleteEmailResponse;", "deleteEmployee", "Lcom/manaknight/app/model/remote/DeleteEmployeeResponse;", "deleteInvoice", "Lcom/manaknight/app/model/remote/DeleteInvoiceResponse;", "deleteJob", "Lcom/manaknight/app/model/remote/DeleteJobResponse;", "deleteLabor", "Lcom/manaknight/app/model/remote/DeleteLaborResponse;", "deleteLineItemEntry", "Lcom/manaknight/app/model/remote/DeleteLineItemEntryResponse;", "deleteLineItems", "deleteLinealFootCost", "Lcom/manaknight/app/model/remote/DeleteLinealFootCostResponse;", "deleteLinealFootCosts", "deleteMaterial", "Lcom/manaknight/app/model/remote/DeleteMaterialResponse;", "deletePermission", "Lcom/manaknight/app/model/remote/DeletePermissionResponse;", "deletePhoto", "Lcom/manaknight/app/model/remote/DeletePhotoResponse;", "deletePosts", "Lcom/manaknight/app/model/remote/DeletePostsResponse;", "deleteProfile", "Lcom/manaknight/app/model/remote/DeleteProfileResponse;", "deleteProject", "Lcom/manaknight/app/model/remote/DeleteProjectResponse;", "deleteRoom", "Lcom/manaknight/app/model/remote/DeleteRoomResponse;", "deleteSetting", "Lcom/manaknight/app/model/remote/DeleteSettingResponse;", "deleteSqftCosts", "Lcom/manaknight/app/model/remote/DeleteSqftCostsResponse;", "deleteSquareFootCost", "deleteTeamMember", "Lcom/manaknight/app/model/remote/DeleteTeamMemberResponse;", "deleteToken", "Lcom/manaknight/app/model/remote/DeleteTokenResponse;", "deleteTriggerType", "Lcom/manaknight/app/model/remote/DeleteTriggerTypeResponse;", "deleteUser", "Lcom/manaknight/app/model/remote/DeleteUserResponse;", "ecomAddCart", "Lcom/manaknight/app/model/remote/EcomAddCartResponse;", "Lcom/manaknight/app/model/remote/EcomAddCartRequest;", "(Lcom/manaknight/app/model/remote/EcomAddCartRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "ecomAddProductReview", "Lcom/manaknight/app/model/remote/EcomAddProductReviewResponse;", "review", "productid", "(Ljava/lang/String;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "ecomDeleteCartItem", "Lcom/manaknight/app/model/remote/EcomDeleteCartItemResponse;", "userId", "data", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "ecomGetProductReview", "Lcom/manaknight/app/model/remote/EcomGetProductReviewResponse;", "ecomProductByIDDefault", "Lcom/manaknight/app/model/remote/EcomProductByIDDefaultResponse;", "editEcomProductLambda", "Lcom/manaknight/app/model/remote/EditEcomProductLambdaResponse;", "Lcom/manaknight/app/model/remote/EditEcomProductLambdaRequest;", "(Lcom/manaknight/app/model/remote/EditEcomProductLambdaRequest;Ljava/lang/Number;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "finalizeProject", "Lcom/manaknight/app/model/remote/FinalizeProjectResponse;", "finalizingOnboarding", "Lcom/manaknight/app/model/remote/FinalizingOnboardingResponse;", "forgotPassword", "Lcom/manaknight/app/model/remote/ForgotPasswordResponse;", "Lcom/manaknight/app/model/remote/ForgotPasswordRequest;", "(Lcom/manaknight/app/model/remote/ForgotPasswordRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "forgotPasswordMobile", "Lcom/manaknight/app/model/remote/ForgotPasswordMobileResponse;", "Lcom/manaknight/app/model/remote/ForgotPasswordMobileRequest;", "(Lcom/manaknight/app/model/remote/ForgotPasswordMobileRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAlertsList", "Lcom/manaknight/app/model/remote/GetAlertsListResponse;", "size", "join", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAlertsPaginated", "Lcom/manaknight/app/model/remote/GetAlertsPaginatedResponse;", "getAllCMSLambda", "Lcom/manaknight/app/model/remote/GetAllCMSLambdaResponse;", "getAllDraws", "Lcom/manaknight/app/model/remote/profitPro/DrawInfoRespModel;", "getAllProjects", "Lcom/manaknight/app/model/remote/ProjectResponseModel;", "getAllRoom", "Lcom/manaknight/app/model/remote/ChatRoomResponse;", "getAllUser", "Lcom/manaknight/app/model/remote/FriendListResponse;", "getAnalyticLogList", "Lcom/manaknight/app/model/remote/GetAnalyticLogListResponse;", "getAnalyticLogPaginated", "Lcom/manaknight/app/model/remote/GetAnalyticLogPaginatedResponse;", "getAnalytics", "Lcom/manaknight/app/model/remote/GetAnalyticsResponse;", "getApiKeysList", "Lcom/manaknight/app/model/remote/GetApiKeysListResponse;", "getApiKeysPaginated", "Lcom/manaknight/app/model/remote/GetApiKeysPaginatedResponse;", "getBlogCategory", "Lcom/manaknight/app/model/remote/GetBlogCategoryResponse;", "getBlogSubcategory", "Lcom/manaknight/app/model/remote/GetBlogSubcategoryResponse;", "getCMSByIDLambda", "Lcom/manaknight/app/model/remote/GetCMSByIDLambdaResponse;", "getCMSByPageAndKeyLambda", "Lcom/manaknight/app/model/remote/GetCMSByPageAndKeyLambdaResponse;", "key", "getCMSByPageLambda", "Lcom/manaknight/app/model/remote/GetCMSByPageLambdaResponse;", "getCartItems", "Lcom/manaknight/app/model/remote/GetCartItemsResponse;", "getChangeOrderDescriptionList", "Lcom/manaknight/app/model/remote/GetChangeOrderDescriptionListResponse;", "getChangeOrderDescriptionPaginated", "Lcom/manaknight/app/model/remote/GetChangeOrderDescriptionPaginatedResponse;", "getChatList", "Lcom/manaknight/app/model/remote/GetChatListResponse;", "getChatPaginated", "Lcom/manaknight/app/model/remote/GetChatPaginatedResponse;", "getChats", "Lcom/manaknight/app/model/remote/ChatResponse;", "Lcom/manaknight/app/model/remote/ChatRequest;", "(Lcom/manaknight/app/model/remote/ChatRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCmsList", "Lcom/manaknight/app/model/remote/GetCmsListResponse;", "getCmsPaginated", "Lcom/manaknight/app/model/remote/GetCmsPaginatedResponse;", "getCompanySettingsList", "Lcom/manaknight/app/model/remote/GetCompanySettingsListResponse;", "getCompanySettingsPaginated", "Lcom/manaknight/app/model/remote/GetCompanySettingsPaginatedResponse;", "getCostList", "Lcom/manaknight/app/model/remote/GetCostListResponse;", "getCostPaginated", "Lcom/manaknight/app/model/remote/GetCostPaginatedResponse;", "getCustomerList", "Lcom/manaknight/app/model/remote/GetCustomerListResponse;", "getCustomerPaginated", "Lcom/manaknight/app/model/remote/GetCustomerPaginatedResponse;", "getDefaultLinealFootCostList", "Lcom/manaknight/app/model/remote/GetDefaultLinealFootCostListResponse;", "getDefaultLinealFootCostPaginated", "Lcom/manaknight/app/model/remote/GetDefaultLinealFootCostPaginatedResponse;", "getDefaultMaterialList", "Lcom/manaknight/app/model/remote/profitPro/MaterialResponseModel;", "getDefaultMaterialPaginated", "Lcom/manaknight/app/model/remote/GetDefaultMaterialPaginatedResponse;", "getDefaultSquareFootCostList", "Lcom/manaknight/app/model/remote/GetDefaultSquareFootCostListResponse;", "getDefaultSquareFootCostPaginated", "Lcom/manaknight/app/model/remote/GetDefaultSquareFootCostPaginatedResponse;", "getDrawsList", "Lcom/manaknight/app/model/remote/GetDrawsListResponse;", "getDrawsPaginated", "Lcom/manaknight/app/model/remote/GetDrawsPaginatedResponse;", "getEmailList", "Lcom/manaknight/app/model/remote/GetEmailListResponse;", "getEmailPaginated", "Lcom/manaknight/app/model/remote/GetEmailPaginatedResponse;", "getEmployeeList", "Lcom/manaknight/app/model/remote/GetEmployeeListResponse;", "getEmployeePaginated", "Lcom/manaknight/app/model/remote/GetEmployeePaginatedResponse;", "getHeatmapData", "Lcom/manaknight/app/model/remote/GetHeatmapDataResponse;", "customDate", "getInvoiceList", "Lcom/manaknight/app/model/remote/GetInvoiceListResponse;", "getInvoicePaginated", "Lcom/manaknight/app/model/remote/GetInvoicePaginatedResponse;", "getJobList", "Lcom/manaknight/app/model/remote/GetJobListResponse;", "getJobPaginated", "Lcom/manaknight/app/model/remote/GetJobPaginatedResponse;", "getLaborList", "Lcom/manaknight/app/model/remote/GetLaborListResponse;", "getLaborPaginated", "Lcom/manaknight/app/model/remote/GetLaborPaginatedResponse;", "getLineDetails", "Lcom/manaknight/app/model/remote/GetLineDetailsResponse;", "lineId", "(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getLineItemEntryList", "Lcom/manaknight/app/model/remote/GetLineItemEntryListResponse;", "getLineItemEntryPaginated", "Lcom/manaknight/app/model/remote/GetLineItemEntryPaginatedResponse;", "getLineItemsList", "Lcom/manaknight/app/model/remote/GetLineItemsListResponse;", "getLineItemsPaginated", "Lcom/manaknight/app/model/remote/GetLineItemsPaginatedResponse;", "getLinealFootCostList", "Lcom/manaknight/app/model/remote/GetLinealFootCostListResponse;", "getLinealFootCostPaginated", "Lcom/manaknight/app/model/remote/GetLinealFootCostPaginatedResponse;", "getMaterialList", "Lcom/manaknight/app/model/remote/GetMaterialListResponse;", "getMaterialPaginated", "Lcom/manaknight/app/model/remote/GetMaterialPaginatedResponse;", "getOneAlerts", "Lcom/manaknight/app/model/remote/GetOneAlertsResponse;", "(Ljava/lang/Integer;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getOneAnalyticLog", "Lcom/manaknight/app/model/remote/GetOneAnalyticLogResponse;", "getOneApiKeys", "Lcom/manaknight/app/model/remote/GetOneApiKeysResponse;", "getOneChangeOrderDescription", "Lcom/manaknight/app/model/remote/GetOneChangeOrderDescriptionResponse;", "getOneChat", "Lcom/manaknight/app/model/remote/GetOneChatResponse;", "getOneCms", "Lcom/manaknight/app/model/remote/GetOneCmsResponse;", "getOneCompanySettings", "Lcom/manaknight/app/model/remote/GetOneCompanySettingsResponse;", "getOneCost", "Lcom/manaknight/app/model/remote/GetOneCostResponse;", "getOneCustomer", "Lcom/manaknight/app/model/remote/GetOneCustomerResponse;", "getOneDefaultLinealFootCost", "Lcom/manaknight/app/model/remote/GetOneDefaultLinealFootCostResponse;", "getOneDefaultMaterial", "Lcom/manaknight/app/model/remote/GetOneDefaultMaterialResponse;", "getOneDefaultSquareFootCost", "Lcom/manaknight/app/model/remote/GetOneDefaultSquareFootCostResponse;", "getOneDraws", "Lcom/manaknight/app/model/remote/GetOneDrawsResponse;", "getOneEmail", "Lcom/manaknight/app/model/remote/GetOneEmailResponse;", "getOneEmployee", "Lcom/manaknight/app/model/remote/GetOneEmployeeResponse;", "getOneInvoice", "Lcom/manaknight/app/model/remote/GetOneInvoiceResponse;", "getOneJob", "Lcom/manaknight/app/model/remote/GetOneJobResponse;", "getOneLabor", "Lcom/manaknight/app/model/remote/GetOneLaborResponse;", "getOneLineItemEntry", "Lcom/manaknight/app/model/remote/GetOneLineItemEntryResponse;", "getOneLineItems", "Lcom/manaknight/app/model/remote/GetOneLineItemsResponse;", "getOneLinealFootCost", "Lcom/manaknight/app/model/remote/GetOneLinealFootCostResponse;", "getOneMaterial", "Lcom/manaknight/app/model/remote/GetOneMaterialResponse;", "getOnePermission", "Lcom/manaknight/app/model/remote/GetOnePermissionResponse;", "getOnePhoto", "Lcom/manaknight/app/model/remote/GetOnePhotoResponse;", "getOnePosts", "Lcom/manaknight/app/model/remote/GetOnePostsResponse;", "getOneProfile", "Lcom/manaknight/app/model/remote/GetOneProfileResponse;", "getOneProject", "Lcom/manaknight/app/model/remote/GetOneProjectResponse;", "getOneRoom", "Lcom/manaknight/app/model/remote/GetOneRoomResponse;", "getOneSetting", "Lcom/manaknight/app/model/remote/GetOneSettingResponse;", "getOneSqftCosts", "Lcom/manaknight/app/model/remote/GetOneSqftCostsResponse;", "getOneTeamMember", "Lcom/manaknight/app/model/remote/GetOneTeamMemberResponse;", "getOneToken", "Lcom/manaknight/app/model/remote/GetOneTokenResponse;", "getOneTriggerType", "Lcom/manaknight/app/model/remote/GetOneTriggerTypeResponse;", "getOneUser", "Lcom/manaknight/app/model/remote/GetOneUserResponse;", "getPaymentHistory", "Lcom/manaknight/app/model/remote/GetPaymentHistoryResponse;", "getPermissionList", "Lcom/manaknight/app/model/remote/GetPermissionListResponse;", "getPermissionPaginated", "Lcom/manaknight/app/model/remote/GetPermissionPaginatedResponse;", "getPhotoList", "Lcom/manaknight/app/model/remote/GetPhotoListResponse;", "getPhotoPaginated", "Lcom/manaknight/app/model/remote/GetPhotoPaginatedResponse;", "getPlans", "Lcom/manaknight/app/model/remote/GetPlansResponse;", "getPostsList", "Lcom/manaknight/app/model/remote/GetPostsListResponse;", "getPostsPaginated", "Lcom/manaknight/app/model/remote/GetPostsPaginatedResponse;", "getProfileList", "Lcom/manaknight/app/model/remote/GetProfileListResponse;", "getProfilePaginated", "Lcom/manaknight/app/model/remote/GetProfilePaginatedResponse;", "getProjectList", "Lcom/manaknight/app/model/remote/GetProjectListResponse;", "getProjectPaginated", "Lcom/manaknight/app/model/remote/GetProjectPaginatedResponse;", "getProjectReview", "Lcom/manaknight/app/model/remote/GetProjectReviewResponse;", "getProjectStats", "Lcom/manaknight/app/model/remote/GetProjectStatsResponse;", "getProjectTrackingDetails", "Lcom/manaknight/app/model/remote/profitPro/ProjectTrackingResponse;", "getProjects", "Lcom/manaknight/app/model/remote/GetProjectsResponse;", "type", "timePeriod", "getRoomList", "Lcom/manaknight/app/model/remote/GetRoomListResponse;", "getRoomPaginated", "Lcom/manaknight/app/model/remote/GetRoomPaginatedResponse;", "getSettingList", "Lcom/manaknight/app/model/remote/GetSettingListResponse;", "getSettingPaginated", "Lcom/manaknight/app/model/remote/GetSettingPaginatedResponse;", "getSingleProjectDetails", "Lcom/manaknight/app/model/remote/profitPro/AllLineItemsResponseModel;", "getSowTree", "Lcom/manaknight/app/model/remote/GetSowTreeResponse;", "getSqftCostsList", "Lcom/manaknight/app/model/remote/GetSqftCostsListResponse;", "getSqftCostsPaginated", "Lcom/manaknight/app/model/remote/GetSqftCostsPaginatedResponse;", "getSquareFootLinealFootCosts", "Lcom/manaknight/app/model/remote/profitPro/LinearResponseModel;", "getStartPool", "Lcom/manaknight/app/model/remote/SingleChatMessageResponse;", "getStripeData", "Lcom/manaknight/app/model/remote/GetStripeDataResponse;", "Lcom/manaknight/app/model/remote/GetStripeDataRequest;", "(Lcom/manaknight/app/model/remote/GetStripeDataRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getSubscriptionStatus", "Lcom/manaknight/app/model/remote/GetSubscriptionStatusResponse;", "getTeamMemberList", "Lcom/manaknight/app/model/remote/GetTeamMemberListResponse;", "getTeamMemberPaginated", "Lcom/manaknight/app/model/remote/GetTeamMemberPaginatedResponse;", "getTokenList", "Lcom/manaknight/app/model/remote/GetTokenListResponse;", "getTokenPaginated", "Lcom/manaknight/app/model/remote/GetTokenPaginatedResponse;", "getTriggerTypeList", "Lcom/manaknight/app/model/remote/GetTriggerTypeListResponse;", "getTriggerTypePaginated", "Lcom/manaknight/app/model/remote/GetTriggerTypePaginatedResponse;", "getUserList", "Lcom/manaknight/app/model/remote/GetUserListResponse;", "getUserPaginated", "Lcom/manaknight/app/model/remote/GetUserPaginatedResponse;", "getUserSubscriptions", "Lcom/manaknight/app/model/remote/GetUserSubscriptionsResponse;", "googleCaptchaVerify", "Lcom/manaknight/app/model/remote/GoogleCaptchaVerifyResponse;", "Lcom/manaknight/app/model/remote/GoogleCaptchaVerifyRequest;", "(Lcom/manaknight/app/model/remote/GoogleCaptchaVerifyRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "googleCode", "Lcom/manaknight/app/model/remote/GoogleCodeResponse;", "state", "googleCodeMobile", "Lcom/manaknight/app/model/remote/GoogleCodeMobileResponse;", "role", "isRefresh", "", "code", "(Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "googleLogin", "Lcom/manaknight/app/model/remote/GoogleLoginResponse;", "companyId", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Boolean;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "initializeDraws", "initializeUser", "Lcom/manaknight/app/model/remote/InitializeUserResponse;", "lambdaCheck", "Lcom/manaknight/app/model/remote/LambdaCheckResponse;", "Lcom/manaknight/app/model/remote/LambdaCheckRequest;", "(Lcom/manaknight/app/model/remote/LambdaCheckRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "logHeatmapAnalytics", "Lcom/manaknight/app/model/remote/LogHeatmapAnalyticsResponse;", "Lcom/manaknight/app/model/remote/LogHeatmapAnalyticsRequest;", "(Lcom/manaknight/app/model/remote/LogHeatmapAnalyticsRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "loginLambda", "Lcom/manaknight/app/model/remote/LoginLambdaResponse;", "Lcom/manaknight/app/model/remote/LoginLambdaRequest;", "(Lcom/manaknight/app/model/remote/LoginLambdaRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "marketingLoginLambda", "Lcom/manaknight/app/model/remote/MarketingLoginLambdaResponse;", "Lcom/manaknight/app/model/remote/MarketingLoginLambdaRequest;", "(Lcom/manaknight/app/model/remote/MarketingLoginLambdaRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "onboarding", "Lcom/manaknight/app/model/remote/OnboardingResponse;", "Lcom/manaknight/app/model/remote/OnboardingRequest;", "(Lcom/manaknight/app/model/remote/OnboardingRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "preferenceFetch", "Lcom/manaknight/app/model/remote/PreferenceFetchResponse;", "preferenceUpdate", "Lcom/manaknight/app/model/remote/PreferenceUpdateResponse;", "Lcom/manaknight/app/model/remote/PreferenceUpdateRequest;", "(Lcom/manaknight/app/model/remote/PreferenceUpdateRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "profile", "Lcom/manaknight/app/model/remote/ProfileResponse;", "profileUpdate", "Lcom/manaknight/app/model/remote/ProfileUpdateResponse;", "Lcom/manaknight/app/model/remote/ProfileUpdateRequest;", "(Lcom/manaknight/app/model/remote/ProfileUpdateRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "registerLambda", "Lcom/manaknight/app/model/remote/RegisterLambdaResponse;", "Lcom/manaknight/app/model/remote/RegisterLambdaRequest;", "(Lcom/manaknight/app/model/remote/RegisterLambdaRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "resetPassword", "Lcom/manaknight/app/model/remote/ResetPasswordResponse;", "Lcom/manaknight/app/model/remote/ResetPasswordRequest;", "(Lcom/manaknight/app/model/remote/ResetPasswordRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "resetPasswordMobile", "Lcom/manaknight/app/model/remote/ResetPasswordMobileResponse;", "Lcom/manaknight/app/model/remote/ResetPasswordMobileRequest;", "(Lcom/manaknight/app/model/remote/ResetPasswordMobileRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "retrieveProductDefault", "Lcom/manaknight/app/model/remote/RetrieveProductDefaultResponse;", "Lcom/manaknight/app/model/remote/RetrieveProductDefaultRequest;", "(Lcom/manaknight/app/model/remote/RetrieveProductDefaultRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveDefaultsOnbording", "Lcom/manaknight/app/model/remote/SaveDefaultsOnbordingResponse;", "Lcom/manaknight/app/model/remote/SaveDefaultsOnbordingRequest;", "(Lcom/manaknight/app/model/remote/SaveDefaultsOnbordingRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchCustomers", "Lcom/manaknight/app/model/remote/profitPro/CustomerResponseModel;", "searchText", "sendInvoice", "Lcom/manaknight/app/model/remote/profitPro/SendInvoiceRequest;", "(Lcom/manaknight/app/model/remote/profitPro/SendInvoiceRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "sendMessageToBot", "Lcom/manaknight/app/model/remote/ChatBotTextResponse;", "Lcom/manaknight/app/model/remote/ChatBotRequest;", "(Lcom/manaknight/app/model/remote/ChatBotRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "json", "sendTextMessage", "Lcom/manaknight/app/model/remote/ChatTextResponse;", "Lcom/manaknight/app/model/remote/ChatTextRequest;", "(Lcom/manaknight/app/model/remote/ChatTextRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "signupCompanySeup", "Lcom/manaknight/app/model/remote/profitPro/CompanyRequest;", "(Lcom/manaknight/app/model/remote/profitPro/CompanyRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "trackingDraws", "Lcom/manaknight/app/model/remote/TrackingDrawsResponse;", "status", "(ILjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "trackingLabour", "Lcom/manaknight/app/model/remote/TrackingLabourResponse;", "trackingMaterial", "Lcom/manaknight/app/model/remote/TrackingMaterialResponse;", "twoFAAuth", "Lcom/manaknight/app/model/remote/TwoFAAuthResponse;", "Lcom/manaknight/app/model/remote/TwoFAAuthRequest;", "(Lcom/manaknight/app/model/remote/TwoFAAuthRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "twoFAAuthorize", "Lcom/manaknight/app/model/remote/TwoFAAuthorizeResponse;", "Lcom/manaknight/app/model/remote/TwoFAAuthorizeRequest;", "(Lcom/manaknight/app/model/remote/TwoFAAuthorizeRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "twoFADisable", "Lcom/manaknight/app/model/remote/TwoFADisableResponse;", "Lcom/manaknight/app/model/remote/TwoFADisableRequest;", "(Lcom/manaknight/app/model/remote/TwoFADisableRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "twoFAEnable", "Lcom/manaknight/app/model/remote/TwoFAEnableResponse;", "Lcom/manaknight/app/model/remote/TwoFAEnableRequest;", "(Lcom/manaknight/app/model/remote/TwoFAEnableRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "twoFALogin", "Lcom/manaknight/app/model/remote/TwoFALoginResponse;", "Lcom/manaknight/app/model/remote/TwoFALoginRequest;", "(Lcom/manaknight/app/model/remote/TwoFALoginRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "twoFASignin", "Lcom/manaknight/app/model/remote/TwoFASigninResponse;", "Lcom/manaknight/app/model/remote/TwoFASigninRequest;", "(Lcom/manaknight/app/model/remote/TwoFASigninRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "twoFAVerify", "Lcom/manaknight/app/model/remote/TwoFAVerifyResponse;", "Lcom/manaknight/app/model/remote/TwoFAVerifyRequest;", "(Lcom/manaknight/app/model/remote/TwoFAVerifyRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateAlerts", "Lcom/manaknight/app/model/remote/UpdateAlertsResponse;", "Lcom/manaknight/app/model/remote/UpdateAlertsRequest;", "(Lcom/manaknight/app/model/remote/UpdateAlertsRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateAnalyticLog", "Lcom/manaknight/app/model/remote/UpdateAnalyticLogResponse;", "Lcom/manaknight/app/model/remote/UpdateAnalyticLogRequest;", "(Lcom/manaknight/app/model/remote/UpdateAnalyticLogRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateApiKeys", "Lcom/manaknight/app/model/remote/UpdateApiKeysResponse;", "Lcom/manaknight/app/model/remote/UpdateApiKeysRequest;", "(Lcom/manaknight/app/model/remote/UpdateApiKeysRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateBlogCategory", "Lcom/manaknight/app/model/remote/UpdateBlogCategoryResponse;", "Lcom/manaknight/app/model/remote/UpdateBlogCategoryRequest;", "(Lcom/manaknight/app/model/remote/UpdateBlogCategoryRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateCMSLambda", "Lcom/manaknight/app/model/remote/UpdateCMSLambdaResponse;", "Lcom/manaknight/app/model/remote/UpdateCMSLambdaRequest;", "(Lcom/manaknight/app/model/remote/UpdateCMSLambdaRequest;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateChangeOrderDescription", "Lcom/manaknight/app/model/remote/UpdateChangeOrderDescriptionResponse;", "Lcom/manaknight/app/model/remote/UpdateChangeOrderDescriptionRequest;", "(Lcom/manaknight/app/model/remote/UpdateChangeOrderDescriptionRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateChat", "Lcom/manaknight/app/model/remote/UpdateChatResponse;", "Lcom/manaknight/app/model/remote/UpdateChatRequest;", "(Lcom/manaknight/app/model/remote/UpdateChatRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateCms", "Lcom/manaknight/app/model/remote/UpdateCmsResponse;", "Lcom/manaknight/app/model/remote/UpdateCmsRequest;", "(Lcom/manaknight/app/model/remote/UpdateCmsRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateCompanyDefault", "Lcom/manaknight/app/model/remote/profitPro/DefaultModel;", "(Lcom/manaknight/app/model/remote/profitPro/DefaultModel;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateCompanySettings", "Lcom/manaknight/app/model/remote/UpdateCompanySettingsResponse;", "Lcom/manaknight/app/model/remote/UpdateCompanySettingsRequest;", "(Lcom/manaknight/app/model/remote/UpdateCompanySettingsRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateCost", "Lcom/manaknight/app/model/remote/UpdateCostResponse;", "Lcom/manaknight/app/model/remote/UpdateCostRequest;", "(Lcom/manaknight/app/model/remote/UpdateCostRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateCustomer", "(Lcom/manaknight/app/model/remote/profitPro/CustomerModel;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateDefaultLinealFootCost", "Lcom/manaknight/app/model/remote/UpdateDefaultLinealFootCostResponse;", "Lcom/manaknight/app/model/remote/UpdateDefaultLinealFootCostRequest;", "(Lcom/manaknight/app/model/remote/UpdateDefaultLinealFootCostRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateDefaultMaterial", "Lcom/manaknight/app/model/remote/UpdateDefaultMaterialResponse;", "Lcom/manaknight/app/model/remote/UpdateDefaultMaterialRequest;", "(Lcom/manaknight/app/model/remote/UpdateDefaultMaterialRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateDefaultSquareFootCost", "Lcom/manaknight/app/model/remote/UpdateDefaultSquareFootCostResponse;", "Lcom/manaknight/app/model/remote/UpdateDefaultSquareFootCostRequest;", "(Lcom/manaknight/app/model/remote/UpdateDefaultSquareFootCostRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateDraws", "Lcom/manaknight/app/model/remote/UpdateDrawsResponse;", "Lcom/manaknight/app/model/remote/UpdateDrawsRequest;", "(Lcom/manaknight/app/model/remote/UpdateDrawsRequest;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "(Lcom/manaknight/app/model/remote/UpdateDrawsRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateEmail", "Lcom/manaknight/app/model/remote/UpdateEmailResponse;", "Lcom/manaknight/app/model/remote/UpdateEmailRequest;", "(Lcom/manaknight/app/model/remote/UpdateEmailRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateEmployee", "Lcom/manaknight/app/model/remote/UpdateEmployeeResponse;", "Lcom/manaknight/app/model/remote/UpdateEmployeeRequest;", "(Lcom/manaknight/app/model/remote/UpdateEmployeeRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateInvoice", "Lcom/manaknight/app/model/remote/UpdateInvoiceResponse;", "Lcom/manaknight/app/model/remote/UpdateInvoiceRequest;", "(Lcom/manaknight/app/model/remote/UpdateInvoiceRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateJob", "Lcom/manaknight/app/model/remote/UpdateJobResponse;", "Lcom/manaknight/app/model/remote/UpdateJobRequest;", "(Lcom/manaknight/app/model/remote/UpdateJobRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateLabor", "Lcom/manaknight/app/model/remote/UpdateLaborResponse;", "Lcom/manaknight/app/model/remote/UpdateLaborRequest;", "(Lcom/manaknight/app/model/remote/UpdateLaborRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateLineItem", "itemID", "Lcom/manaknight/app/model/remote/profitPro/UpdateLineItemReqModel;", "(ILcom/manaknight/app/model/remote/profitPro/UpdateLineItemReqModel;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateLineItemEntry", "Lcom/manaknight/app/model/remote/UpdateLineItemEntryResponse;", "Lcom/manaknight/app/model/remote/UpdateLineItemEntryRequest;", "(Lcom/manaknight/app/model/remote/UpdateLineItemEntryRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateLineItems", "Lcom/manaknight/app/model/remote/UpdateLineItemsResponse;", "Lcom/manaknight/app/model/remote/UpdateLineItemsRequest;", "(Lcom/manaknight/app/model/remote/UpdateLineItemsRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateLinealFootCost", "Lcom/manaknight/app/model/remote/UpdateLinealFootCostResponse;", "Lcom/manaknight/app/model/remote/UpdateLinealFootCostRequest;", "(Lcom/manaknight/app/model/remote/UpdateLinealFootCostRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "(Lcom/manaknight/app/model/remote/profitPro/LinearFootReqModel;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateMaterial", "Lcom/manaknight/app/model/remote/UpdateMaterialResponse;", "Lcom/manaknight/app/model/remote/UpdateMaterialRequest;", "(Lcom/manaknight/app/model/remote/UpdateMaterialRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "(Lcom/manaknight/app/model/remote/profitPro/MaterialRequestModel;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updatePercentageDraw", "Lcom/manaknight/app/model/remote/profitPro/UpdateDrawRequest2;", "(Lcom/manaknight/app/model/remote/profitPro/UpdateDrawRequest2;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updatePermission", "Lcom/manaknight/app/model/remote/UpdatePermissionResponse;", "Lcom/manaknight/app/model/remote/UpdatePermissionRequest;", "(Lcom/manaknight/app/model/remote/UpdatePermissionRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updatePhoto", "Lcom/manaknight/app/model/remote/UpdatePhotoResponse;", "Lcom/manaknight/app/model/remote/UpdatePhotoRequest;", "(Lcom/manaknight/app/model/remote/UpdatePhotoRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updatePosts", "Lcom/manaknight/app/model/remote/UpdatePostsResponse;", "Lcom/manaknight/app/model/remote/UpdatePostsRequest;", "(Lcom/manaknight/app/model/remote/UpdatePostsRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updatePriceDraw", "Lcom/manaknight/app/model/remote/profitPro/UpdateDrawRequest;", "(Lcom/manaknight/app/model/remote/profitPro/UpdateDrawRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateProfile", "Lcom/manaknight/app/model/remote/UpdateProfileResponse;", "Lcom/manaknight/app/model/remote/UpdateProfileRequest;", "(Lcom/manaknight/app/model/remote/UpdateProfileRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateProject", "Lcom/manaknight/app/model/remote/UpdateProjectResponse;", "Lcom/manaknight/app/model/remote/UpdateProjectRequest;", "(Lcom/manaknight/app/model/remote/UpdateProjectRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateRoom", "Lcom/manaknight/app/model/remote/UpdateRoomResponse;", "Lcom/manaknight/app/model/remote/UpdateRoomRequest;", "(Lcom/manaknight/app/model/remote/UpdateRoomRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateSetting", "Lcom/manaknight/app/model/remote/UpdateSettingResponse;", "Lcom/manaknight/app/model/remote/UpdateSettingRequest;", "(Lcom/manaknight/app/model/remote/UpdateSettingRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateSqftCosts", "Lcom/manaknight/app/model/remote/UpdateSqftCostsResponse;", "Lcom/manaknight/app/model/remote/UpdateSqftCostsRequest;", "(Lcom/manaknight/app/model/remote/UpdateSqftCostsRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateSquareFootCost", "updateTeamMember", "Lcom/manaknight/app/model/remote/UpdateTeamMemberResponse;", "Lcom/manaknight/app/model/remote/UpdateTeamMemberRequest;", "(Lcom/manaknight/app/model/remote/UpdateTeamMemberRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateTeamMemberHours", "Lcom/manaknight/app/model/remote/UpdateTeamMemberHoursResponse;", "Lcom/manaknight/app/model/remote/UpdateTeamMemberHoursRequest;", "(Lcom/manaknight/app/model/remote/UpdateTeamMemberHoursRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateToken", "Lcom/manaknight/app/model/remote/UpdateTokenResponse;", "Lcom/manaknight/app/model/remote/UpdateTokenRequest;", "(Lcom/manaknight/app/model/remote/UpdateTokenRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateTriggerType", "Lcom/manaknight/app/model/remote/UpdateTriggerTypeResponse;", "Lcom/manaknight/app/model/remote/UpdateTriggerTypeRequest;", "(Lcom/manaknight/app/model/remote/UpdateTriggerTypeRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateUser", "Lcom/manaknight/app/model/remote/UpdateUserResponse;", "Lcom/manaknight/app/model/remote/UpdateUserRequest;", "(Lcom/manaknight/app/model/remote/UpdateUserRequest;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "uploadImageLocalDefault", "Lcom/manaknight/app/model/remote/UploadImageLocalDefaultResponse;", "file", "Lokhttp3/MultipartBody$Part;", "(Lokhttp3/MultipartBody$Part;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "uploadimages3", "Lcom/manaknight/app/model/remote/UploadImageS3Response;", "userSessionsData", "Lcom/manaknight/app/model/remote/UserSessionsDataResponse;", "app_debug"})
public abstract interface ApiService {
    
    @retrofit2.http.POST(value = "/v3/api/custom/profitpro/company/initialize-user")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object signupCompanySeup(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.CompanyRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CommonResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v3/api/custom/profitpro/company/onboarding-save-defaults")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateCompanyDefault(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.DefaultModel request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CommonResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v3/api/custom/profitpro/company/search-customer")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object searchCustomers(@retrofit2.http.Query(value = "prefix")
    @org.jetbrains.annotations.Nullable()
    java.lang.String searchText, @retrofit2.http.Query(value = "limit")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CustomerResponseModel>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/default_material")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createMaterial(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.MaterialRequestModel request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CommonResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/default_material/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateMaterial(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.MaterialRequestModel request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CommonResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/customer")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createCustomer(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.CustomerModel request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CommonResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/customer/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateCustomer(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.CustomerModel request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CommonResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/customer")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCustomerList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetCustomerListResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v3/api/custom/profitpro/company/create-estimation")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createNewEstimation(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.ProjectModel request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CommonResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/default_material")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getDefaultMaterialList(@retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.MaterialResponseModel>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/default_material")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createDefaultMaterial(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.MaterialReqModel request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CommonResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v3/api/custom/profitpro/company/add-line-item")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object addLineItem(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.CreateLineItemReqModel request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CommonResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v3/api/custom/profitpro/company/edit-line-item/{item_id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateLineItem(@retrofit2.http.Path(value = "item_id")
    int itemID, @retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.UpdateLineItemReqModel request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CommonResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v3/api/custom/profitpro/company/get-costs")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getSquareFootLinealFootCosts(@retrofit2.http.Query(value = "type")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.LinearResponseModel>> $completion);
    
    @retrofit2.http.POST(value = "/v3/api/custom/profitpro/company/add-lineal-foot-cost")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object addLinealFootCost(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.LinearFootReqModel request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CommonResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v3/api/custom/profitpro/company/add-square-foot-cost")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object addSquareFootCost(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.LinearFootReqModel request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CommonResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v3/api/custom/profitpro/company/get-project-details/{project_id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getSingleProjectDetails(@retrofit2.http.Path(value = "project_id")
    int projectId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/project")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllProjects(@retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.NotNull()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.NotNull()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.ProjectResponseModel>> $completion);
    
    @retrofit2.http.GET(value = "/v3/api/custom/profitpro/company/initialize-draws/{project_id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object initializeDraws(@retrofit2.http.Path(value = "project_id")
    int projectId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CommonResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v3/api/custom/profitpro/company/get-draws-totals/{project_id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllDraws(@retrofit2.http.Path(value = "project_id")
    int projectId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.DrawInfoRespModel>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/draws/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteDraws(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CommonResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v3/api/custom/profitpro/company/create-draws")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createPriceDraw(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.CreatePriceDrawRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CommonResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v3/api/custom/profitpro/company/create-draws")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createPercentageDraw(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.CreatePercentageDrawRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CommonResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v3/api/custom/profitpro/send-invoice")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object sendInvoice(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.SendInvoiceRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CommonResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v3/api/custom/profitpro/company/update-draws/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updatePriceDraw(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.UpdateDrawRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CommonResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v3/api/custom/profitpro/company/update-draws/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updatePercentageDraw(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.UpdateDrawRequest2 request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CommonResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/line_items/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteLineItems(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CommonResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v1/api/rest/user/GETALL")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllUser(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.FriendListResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v3/api/lambda/realtime/room")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createRoomRequests(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateRoomRequests request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateRoomResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v3/api/lambda/realtime/chat")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getChats(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.ChatRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.ChatResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v3/api/lambda/realtime/room/poll")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getStartPool(@retrofit2.http.Query(value = "user_id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer customDate, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.SingleChatMessageResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/room")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllRoom(@retrofit2.http.Query(value = "user_id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.ChatRoomResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v5/api/deployments/wireframe/ask-gpt-custom")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object sendMessageToBot(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    java.lang.String json, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.ChatBotTextResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v5/api/deployments/wireframe/ask-gpt-custom")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object sendMessageToBot(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.ChatBotRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.ChatBotTextResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v3/api/lambda/realtime/send")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object sendTextMessage(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.ChatTextRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.ChatTextResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v3/api/custom/profitpro/company/change-order")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createChangeOrder(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateChangeOrderRequest request, @retrofit2.http.Path(value = "project_id")
    @org.jetbrains.annotations.NotNull()
    java.lang.Object projectId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateChangeOrderResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v3/api/custom/profitpro/company/finalize-project")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object finalizeProject(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.FinalizeProjectResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v3/api/custom/profitpro/company/project-review")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getProjectReview(@retrofit2.http.Path(value = "project_id")
    @org.jetbrains.annotations.NotNull()
    java.lang.Object projectId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetProjectReviewResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v3/api/custom/profitpro/company/update-draws")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateDraws(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateDrawsRequest request, @retrofit2.http.Path(value = "project_id")
    int projectId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateDrawsResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v3/api/custom/profitpro/company/get-material-details")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object trackingMaterial(@retrofit2.http.Path(value = "project_id")
    int projectId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.TrackingMaterialResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v3/api/custom/profitpro/company/get-tracking/{project_id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getProjectTrackingDetails(@retrofit2.http.Path(value = "project_id")
    int projectId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.ProjectTrackingResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v3/api/custom/profitpro/company/get-labor-details")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object trackingLabour(@retrofit2.http.Path(value = "project_id")
    int projectId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.TrackingLabourResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v3/api/custom/profitpro/company/get-draws")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object trackingDraws(@retrofit2.http.Path(value = "project_id")
    int projectId, @retrofit2.http.Query(value = "status")
    @org.jetbrains.annotations.Nullable()
    java.lang.String status, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.TrackingDrawsResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v3/api/custom/profitpro/company/line-details")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLineDetails(@retrofit2.http.Path(value = "line_id")
    @org.jetbrains.annotations.NotNull()
    java.lang.Object lineId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetLineDetailsResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v3/api/custom/profitpro/company/finlize-account")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object finalizingOnboarding(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.FinalizingOnboardingResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v3/api/custom/profitpro/company/initialize-user")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object initializeUser(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.InitializeUserResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v3/api/custom/profitpro/company/onboarding-save-defaults")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object saveDefaultsOnbording(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.SaveDefaultsOnbordingRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.SaveDefaultsOnbordingResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v3/api/custom/profitpro/company/get-projects")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getProjects(@retrofit2.http.Query(value = "type")
    @org.jetbrains.annotations.Nullable()
    java.lang.String type, @retrofit2.http.Query(value = "time_period")
    @org.jetbrains.annotations.Nullable()
    java.lang.String timePeriod, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetProjectsResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v3/api/custom/profitpro/company/onboarding")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object onboarding(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.OnboardingRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.OnboardingResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v3/api/custom/profitpro/company/overview")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object companyOverview(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CompanyOverviewResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v3/api/custom/profitpro/company/details")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object companyDetails(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CompanyDetailsResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v3/api/profitpro/dashboard/project-stats")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getProjectStats(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetProjectStatsResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/check")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object lambdaCheck(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.LambdaCheckRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.LambdaCheckResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/2fa/login")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object twoFALogin(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.TwoFALoginRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.TwoFALoginResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/2fa/signin")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object twoFASignin(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.TwoFASigninRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.TwoFASigninResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/2fa/authorize")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object twoFAAuthorize(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.TwoFAAuthorizeRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.TwoFAAuthorizeResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/2fa/enable")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object twoFAEnable(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.TwoFAEnableRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.TwoFAEnableResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/2fa/disable")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object twoFADisable(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.TwoFADisableRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.TwoFADisableResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/2fa/verify")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object twoFAVerify(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.TwoFAVerifyRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.TwoFAVerifyResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/2fa/auth")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object twoFAAuth(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.TwoFAAuthRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.TwoFAAuthResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/analytics/")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object analyticsLog(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.AnalyticsLogRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.AnalyticsLogResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v2/api/lambda/analytics/data")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAnalytics(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetAnalyticsResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/heatmap")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object logHeatmapAnalytics(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.LogHeatmapAnalyticsRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.LogHeatmapAnalyticsResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v2/api/lambda/heatmap/data")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getHeatmapData(@retrofit2.http.Query(value = "custom_date")
    @org.jetbrains.annotations.Nullable()
    java.lang.String customDate, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetHeatmapDataResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v2/api/lambda/user-sessions/data")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object userSessionsData(@retrofit2.http.Query(value = "custom_date")
    @org.jetbrains.annotations.Nullable()
    java.lang.String customDate, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UserSessionsDataResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/analytics/user-sessions/")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createUserSessionsAnalytics(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateUserSessionsAnalyticsRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateUserSessionsAnalyticsResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/apple/login/mobile")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object appleLoginMobileEndpoint(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.AppleLoginMobileEndpointRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.AppleLoginMobileEndpointResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v2/api/lambda/apple/login")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object appleLogin(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.AppleLoginResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/apple/code")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object appleAuthCode(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.AppleAuthCodeRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.AppleAuthCodeResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v2/api/lambda/google/code")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object googleCode(@retrofit2.http.Query(value = "state")
    @org.jetbrains.annotations.NotNull()
    java.lang.String state, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GoogleCodeResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v2/api/lambda/google/code/mobile")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object googleCodeMobile(@retrofit2.http.Query(value = "role")
    @org.jetbrains.annotations.Nullable()
    java.lang.String role, @retrofit2.http.Query(value = "is_refresh")
    @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isRefresh, @retrofit2.http.Query(value = "code")
    @org.jetbrains.annotations.Nullable()
    java.lang.String code, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GoogleCodeMobileResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v2/api/lambda/google/login")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object googleLogin(@retrofit2.http.Query(value = "role")
    @org.jetbrains.annotations.Nullable()
    java.lang.String role, @retrofit2.http.Query(value = "company_id")
    @org.jetbrains.annotations.Nullable()
    java.lang.String companyId, @retrofit2.http.Query(value = "is_refresh")
    @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isRefresh, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GoogleLoginResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v2/api/lambda/blog/all")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object blogAll(@retrofit2.http.Query(value = "limit")
    int limit, @retrofit2.http.Query(value = "offset")
    int offset, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.BlogAllResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v2/api/lambda/blog/similar/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object blogSimilar(@retrofit2.http.Query(value = "top")
    int top, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.BlogSimilarResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v2/api/lambda/blog/filter")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object blogFilter(@retrofit2.http.Query(value = "categories")
    @org.jetbrains.annotations.NotNull()
    java.util.ArrayList<java.util.Map<java.lang.String, java.lang.Object>> categories, @retrofit2.http.Query(value = "tags")
    @org.jetbrains.annotations.NotNull()
    java.util.ArrayList<java.util.Map<java.lang.String, java.lang.Object>> tags, @retrofit2.http.Query(value = "rule")
    @org.jetbrains.annotations.Nullable()
    java.lang.String rule, @retrofit2.http.Query(value = "search")
    @org.jetbrains.annotations.Nullable()
    java.lang.String search, @retrofit2.http.Query(value = "limit")
    int limit, @retrofit2.http.Query(value = "page")
    int page, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.BlogFilterResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/blog/create")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object blogCreate(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.BlogCreateRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.BlogCreateResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/blog/edit/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object blogEdit(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.BlogEditRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.BlogEditResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v2/api/lambda/blog/delete/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object blogDelete(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.BlogDeleteResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v2/api/lambda/blog/single/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object blogSingle(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.BlogSingleResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/blog/tags")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object blogTags(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.BlogTagsRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.BlogTagsResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/blog/tags/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object blogTagsUpdate(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.BlogTagsUpdateRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.BlogTagsUpdateResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v2/api/lambda/blog/tags")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object blogTagsRetrieve(@retrofit2.http.Query(value = "limit")
    int limit, @retrofit2.http.Query(value = "page")
    int page, @retrofit2.http.Query(value = "name")
    @org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.BlogTagsRetrieveResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v2/api/lambda/blog/tags/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object blogTagsDeleteByID(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.BlogTagsDeleteByIDResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/blog/category")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createBlogCategory(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateBlogCategoryRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateBlogCategoryResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/blog/category/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateBlogCategory(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateBlogCategoryRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateBlogCategoryResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v2/api/lambda/blog/category")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getBlogCategory(@retrofit2.http.Query(value = "limit")
    int limit, @retrofit2.http.Query(value = "page")
    int page, @retrofit2.http.Query(value = "name")
    @org.jetbrains.annotations.Nullable()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetBlogCategoryResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v2/api/lambda/blog/subcategory/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getBlogSubcategory(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetBlogSubcategoryResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v2/api/lambda/blog/category/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteBlogCategory(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteBlogCategoryResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v2/api/lambda/test/{width?}/{height?}/")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object captchaTest(@retrofit2.http.Path(value = "width")
    int width, @retrofit2.http.Path(value = "height")
    int height, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CaptchaTestResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v2/api/lambda/captcha/{width?}/{height?}/")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object captchaGenerate(@retrofit2.http.Path(value = "width")
    int width, @retrofit2.http.Path(value = "height")
    int height, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CaptchaGenerateResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/google-captcha/")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object googleCaptchaVerify(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.GoogleCaptchaVerifyRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GoogleCaptchaVerifyResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/cms")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createCMSLambda(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateCMSLambdaRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateCMSLambdaResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v2/api/lambda/cms/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateCMSLambda(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateCMSLambdaRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateCMSLambdaResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v2/api/lambda/cms/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteCMSLambda(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteCMSLambdaResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v2/api/lambda/cms/id/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCMSByIDLambda(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetCMSByIDLambdaResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v2/api/lambda/cms/page/{page}/{key}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCMSByPageAndKeyLambda(@retrofit2.http.Path(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Path(value = "key")
    @org.jetbrains.annotations.Nullable()
    java.lang.String key, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetCMSByPageAndKeyLambdaResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v2/api/lambda/cms/page/{page}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCMSByPageLambda(@retrofit2.http.Path(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetCMSByPageLambdaResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v2/api/lambda/cms/all")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllCMSLambda(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetAllCMSLambdaResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/register")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object registerLambda(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.RegisterLambdaRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.RegisterLambdaResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/login")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object loginLambda(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.LoginLambdaRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.LoginLambdaResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/marketing-login")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object marketingLoginLambda(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.MarketingLoginLambdaRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.MarketingLoginLambdaResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v2/api/lambda/profile")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object profile(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.ProfileResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/profile")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object profileUpdate(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.ProfileUpdateRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.ProfileUpdateResponse>> $completion);
    
    @retrofit2.http.Multipart()
    @retrofit2.http.POST(value = "/v2/api/lambda/upload")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object uploadImageLocalDefault(@retrofit2.http.Part()
    @org.jetbrains.annotations.NotNull()
    okhttp3.MultipartBody.Part file, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UploadImageLocalDefaultResponse>> $completion);
    
    @retrofit2.http.Multipart()
    @retrofit2.http.POST(value = "/v2/api/lambda/s3/upload")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object uploadimages3(@retrofit2.http.Part()
    @org.jetbrains.annotations.NotNull()
    okhttp3.MultipartBody.Part file, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UploadImageS3Response>> $completion);
    
    @retrofit2.http.GET(value = "/v2/api/lambda/preference")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object preferenceFetch(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.PreferenceFetchResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/preference")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object preferenceUpdate(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.PreferenceUpdateRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.PreferenceUpdateResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/sow")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getSowTree(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetSowTreeResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/alerts")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object appAlertsList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.AppAlertsListResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/alerts/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object appAlertsUpdate(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.AppAlertsUpdateRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.AppAlertsUpdateResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/ecom/product/")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object retrieveProductDefault(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.RetrieveProductDefaultRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.RetrieveProductDefaultResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v2/api/lambda/ecom/product/{product_identifier}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object ecomProductByIDDefault(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.EcomProductByIDDefaultResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v3/api/custom/lambda/ecom/product/add")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object addEcomProductLambda(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.AddEcomProductLambdaRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.AddEcomProductLambdaResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v2/api/lambda/ecom/product/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object editEcomProductLambda(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.EditEcomProductLambdaRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Number id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.EditEcomProductLambdaResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v2/api/lambda/ecom/product/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteEcomProductLambda(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Number id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteEcomProductLambdaResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v2/api/lambda/ecom/cart")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCartItems(@retrofit2.http.Query(value = "user_id")
    @org.jetbrains.annotations.Nullable()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetCartItemsResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/ecom/cart/item")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object ecomAddCart(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.EcomAddCartRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.EcomAddCartResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/ecom/cart/update")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object ecomDeleteCartItem(@retrofit2.http.Query(value = "user_id")
    @org.jetbrains.annotations.Nullable()
    java.lang.String userId, @retrofit2.http.Query(value = "data")
    @org.jetbrains.annotations.Nullable()
    java.lang.String data, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.EcomDeleteCartItemResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/ecom/product/review")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object ecomGetProductReview(@retrofit2.http.Query(value = "productId")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer productid, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.EcomGetProductReviewResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/ecom/product/review/add")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object ecomAddProductReview(@retrofit2.http.Query(value = "review")
    @org.jetbrains.annotations.Nullable()
    java.lang.String review, @retrofit2.http.Query(value = "productId")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer productid, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.EcomAddProductReviewResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/forgot")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object forgotPassword(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.ForgotPasswordRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.ForgotPasswordResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/mobile/forgot")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object forgotPasswordMobile(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.ForgotPasswordMobileRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.ForgotPasswordMobileResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/reset")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object resetPassword(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.ResetPasswordRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.ResetPasswordResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/mobile/reset")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object resetPasswordMobile(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.ResetPasswordMobileRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.ResetPasswordMobileResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v2/api/lambda/stripe/mobile/intent/")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getStripeData(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.GetStripeDataRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetStripeDataResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/default_square_foot_cost/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneDefaultSquareFootCost(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneDefaultSquareFootCostResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/setting/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneSetting(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneSettingResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/cost/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneCost(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneCostResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/room/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneRoom(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneRoomResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/labor/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneLabor(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneLaborResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/line_item_entry/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneLineItemEntry(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneLineItemEntryResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/company_settings/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneCompanySettings(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneCompanySettingsResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/cms/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneCms(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneCmsResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/team_member/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneTeamMember(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneTeamMemberResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/default_material/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneDefaultMaterial(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneDefaultMaterialResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/project/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneProject(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneProjectResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/user/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneUser(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneUserResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/profile/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneProfile(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneProfileResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/lineal_foot_cost/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneLinealFootCost(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneLinealFootCostResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/customer/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneCustomer(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneCustomerResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/permission/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOnePermission(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOnePermissionResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/token/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneToken(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneTokenResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/sqft_costs/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneSqftCosts(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneSqftCostsResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/email/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneEmail(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneEmailResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/alerts/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneAlerts(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneAlertsResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/draws/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneDraws(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneDrawsResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/chat/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneChat(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneChatResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/material/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneMaterial(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneMaterialResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/invoice/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneInvoice(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneInvoiceResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/default_lineal_foot_cost/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneDefaultLinealFootCost(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneDefaultLinealFootCostResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/trigger_type/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneTriggerType(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneTriggerTypeResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/job/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneJob(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneJobResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/line_items/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneLineItems(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneLineItemsResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/photo/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOnePhoto(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOnePhotoResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/api_keys/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneApiKeys(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneApiKeysResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/change_order_description/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneChangeOrderDescription(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneChangeOrderDescriptionResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/analytic_log/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneAnalyticLog(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneAnalyticLogResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/posts/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOnePosts(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOnePostsResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/employee/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneEmployee(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetOneEmployeeResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/default_square_foot_cost")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getDefaultSquareFootCostList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetDefaultSquareFootCostListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/setting")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getSettingList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetSettingListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/cost")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCostList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetCostListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/room")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRoomList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetRoomListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/labor")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLaborList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetLaborListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/line_item_entry")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLineItemEntryList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetLineItemEntryListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/company_settings")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCompanySettingsList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetCompanySettingsListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/cms")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCmsList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetCmsListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/team_member")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTeamMemberList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetTeamMemberListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/project")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getProjectList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetProjectListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/user")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getUserList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetUserListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/profile")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getProfileList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetProfileListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/lineal_foot_cost")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLinealFootCostList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetLinealFootCostListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/permission")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPermissionList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetPermissionListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/token")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTokenList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetTokenListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/sqft_costs")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getSqftCostsList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetSqftCostsListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/email")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getEmailList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetEmailListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/alerts")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAlertsList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetAlertsListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/draws")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getDrawsList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetDrawsListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/chat")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getChatList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetChatListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/material")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getMaterialList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetMaterialListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/invoice")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getInvoiceList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetInvoiceListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/default_lineal_foot_cost")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getDefaultLinealFootCostList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetDefaultLinealFootCostListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/trigger_type")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTriggerTypeList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetTriggerTypeListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/job")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getJobList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetJobListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/line_items")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLineItemsList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetLineItemsListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/photo")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPhotoList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetPhotoListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/api_keys")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getApiKeysList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetApiKeysListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/change_order_description")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getChangeOrderDescriptionList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetChangeOrderDescriptionListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/analytic_log")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAnalyticLogList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetAnalyticLogListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/posts")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPostsList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetPostsListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/employee")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getEmployeeList(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "size")
    @org.jetbrains.annotations.Nullable()
    java.lang.String size, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetEmployeeListResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/default_square_foot_cost")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getDefaultSquareFootCostPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetDefaultSquareFootCostPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/setting")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getSettingPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetSettingPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/cost")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCostPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetCostPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/room")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRoomPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetRoomPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/labor")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLaborPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetLaborPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/line_item_entry")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLineItemEntryPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetLineItemEntryPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/company_settings")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCompanySettingsPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetCompanySettingsPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/cms")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCmsPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetCmsPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/team_member")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTeamMemberPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetTeamMemberPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/default_material")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getDefaultMaterialPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetDefaultMaterialPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/project")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getProjectPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetProjectPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/user")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getUserPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetUserPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/profile")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getProfilePaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetProfilePaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/lineal_foot_cost")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLinealFootCostPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetLinealFootCostPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/customer")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCustomerPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetCustomerPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/permission")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPermissionPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetPermissionPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/token")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTokenPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetTokenPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/sqft_costs")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getSqftCostsPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetSqftCostsPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/email")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getEmailPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetEmailPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/alerts")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAlertsPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetAlertsPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/draws")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getDrawsPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetDrawsPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/chat")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getChatPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetChatPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/material")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getMaterialPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetMaterialPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/invoice")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getInvoicePaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetInvoicePaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/default_lineal_foot_cost")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getDefaultLinealFootCostPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetDefaultLinealFootCostPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/trigger_type")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTriggerTypePaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetTriggerTypePaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/job")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getJobPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetJobPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/line_items")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLineItemsPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetLineItemsPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/photo")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPhotoPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetPhotoPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/api_keys")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getApiKeysPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetApiKeysPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/change_order_description")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getChangeOrderDescriptionPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetChangeOrderDescriptionPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/analytic_log")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAnalyticLogPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetAnalyticLogPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/posts")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPostsPaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetPostsPaginatedResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/employee")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getEmployeePaginated(@retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.Nullable()
    java.lang.String order, @retrofit2.http.Query(value = "page")
    @org.jetbrains.annotations.Nullable()
    java.lang.String page, @retrofit2.http.Query(value = "filter")
    @org.jetbrains.annotations.Nullable()
    java.lang.String filter, @retrofit2.http.Query(value = "join")
    @org.jetbrains.annotations.Nullable()
    java.lang.String join, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetEmployeePaginatedResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/default_square_foot_cost")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createDefaultSquareFootCost(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateDefaultSquareFootCostRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateDefaultSquareFootCostResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/setting")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createSetting(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateSettingRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateSettingResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/cost")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createCost(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateCostRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateCostResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/room")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createRoom(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateRoomRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateRoomResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/labor")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createLabor(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateLaborRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateLaborResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/line_item_entry")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createLineItemEntry(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateLineItemEntryRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateLineItemEntryResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/company_settings")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createCompanySettings(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateCompanySettingsRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateCompanySettingsResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/cms")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createCms(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateCmsRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateCmsResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/team_member")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createTeamMember(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateTeamMemberRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateTeamMemberResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/draws")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createDraw(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateDrawRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CommonResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/project")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createProject(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateProjectRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateProjectResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/user")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createUser(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateUserRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateUserResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/profile")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createProfile(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateProfileRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateProfileResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/lineal_foot_cost")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createLinealFootCost(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateLinealFootCostRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateLinealFootCostResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/permission")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createPermission(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreatePermissionRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreatePermissionResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/token")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createToken(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateTokenRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateTokenResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/sqft_costs")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createSqftCosts(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateSqftCostsRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateSqftCostsResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/email")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createEmail(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateEmailRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateEmailResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/alerts")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createAlerts(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateAlertsRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateAlertsResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/chat")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createChat(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateChatRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateChatResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/material")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createMaterial(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateMaterialRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateMaterialResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/invoice")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createInvoice(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateInvoiceRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateInvoiceResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/default_lineal_foot_cost")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createDefaultLinealFootCost(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateDefaultLinealFootCostRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateDefaultLinealFootCostResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/trigger_type")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createTriggerType(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateTriggerTypeRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateTriggerTypeResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/job")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createJob(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateJobRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateJobResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/line_items")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createLineItems(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateLineItemsRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateLineItemsResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/photo")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createPhoto(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreatePhotoRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreatePhotoResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/api_keys")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createApiKeys(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateApiKeysRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateApiKeysResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/change_order_description")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createChangeOrderDescription(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateChangeOrderDescriptionRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateChangeOrderDescriptionResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/analytic_log")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createAnalyticLog(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateAnalyticLogRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateAnalyticLogResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/posts")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createPosts(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreatePostsRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreatePostsResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/employee")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createEmployee(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateEmployeeRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateEmployeeResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/default_square_foot_cost/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateDefaultSquareFootCost(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateDefaultSquareFootCostRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateDefaultSquareFootCostResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/setting/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateSetting(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateSettingRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateSettingResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/cost/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateCost(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateCostRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateCostResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/room/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateRoom(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateRoomRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateRoomResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/labor/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateLabor(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateLaborRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateLaborResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/line_item_entry/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateLineItemEntry(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateLineItemEntryRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateLineItemEntryResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/company_settings/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateCompanySettings(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateCompanySettingsRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateCompanySettingsResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/cms/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateCms(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateCmsRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateCmsResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/team_member/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateTeamMember(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateTeamMemberRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateTeamMemberResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/team_member/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateTeamMemberHours(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateTeamMemberHoursRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateTeamMemberHoursResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/default_material/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateDefaultMaterial(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateDefaultMaterialRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateDefaultMaterialResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v3/api/custom/profitpro/company/update-lineal-foot-cost/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateLinealFootCost(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.LinearFootReqModel request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CommonResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v3/api/custom/profitpro/company/update-square-foot-cost/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateSquareFootCost(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.LinearFootReqModel request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CommonResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/project/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateProject(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateProjectRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateProjectResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/user/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateUser(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateUserRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateUserResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/profile/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateProfile(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateProfileRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateProfileResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/lineal_foot_cost/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateLinealFootCost(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateLinealFootCostRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateLinealFootCostResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/permission/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updatePermission(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdatePermissionRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdatePermissionResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/token/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateToken(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateTokenRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateTokenResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/sqft_costs/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateSqftCosts(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateSqftCostsRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateSqftCostsResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/email/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateEmail(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateEmailRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateEmailResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/alerts/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateAlerts(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateAlertsRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateAlertsResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/draws/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateDraws(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateDrawsRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateDrawsResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/chat/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateChat(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateChatRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateChatResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/material/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateMaterial(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateMaterialRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateMaterialResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/invoice/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateInvoice(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateInvoiceRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateInvoiceResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/default_lineal_foot_cost/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateDefaultLinealFootCost(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateDefaultLinealFootCostRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateDefaultLinealFootCostResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/trigger_type/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateTriggerType(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateTriggerTypeRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateTriggerTypeResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/job/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateJob(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateJobRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateJobResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/line_items/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateLineItems(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateLineItemsRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateLineItemsResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/photo/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updatePhoto(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdatePhotoRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdatePhotoResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/api_keys/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateApiKeys(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateApiKeysRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateApiKeysResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/change_order_description/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateChangeOrderDescription(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateChangeOrderDescriptionRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateChangeOrderDescriptionResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/analytic_log/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateAnalyticLog(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateAnalyticLogRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateAnalyticLogResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/posts/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updatePosts(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdatePostsRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdatePostsResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/employee/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateEmployee(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.UpdateEmployeeRequest request, @retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.UpdateEmployeeResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/default_square_foot_cost/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteDefaultSquareFootCost(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteDefaultSquareFootCostResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/setting/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteSetting(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteSettingResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/cost/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteCost(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteCostResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/room/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteRoom(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteRoomResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/labor/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteLabor(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteLaborResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/line_item_entry/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteLineItemEntry(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteLineItemEntryResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/company_settings/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteCompanySettings(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteCompanySettingsResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/cms/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteCms(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteCmsResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/team_member/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteTeamMember(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteTeamMemberResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/default_material/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteDefaultMaterial(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteDefaultMaterialResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v3/api/custom/profitpro/company/delete-lineal-foot-cost/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteLinealFootCosts(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CommonResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v3/api/custom/profitpro/company/delete-square-foot-cost/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteSquareFootCost(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.profitPro.CommonResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/project/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteProject(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteProjectResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/user/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteUser(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteUserResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/profile/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteProfile(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteProfileResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/lineal_foot_cost/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteLinealFootCost(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteLinealFootCostResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/customer/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteCustomer(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteCustomerResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/permission/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deletePermission(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeletePermissionResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/token/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteToken(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteTokenResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/sqft_costs/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteSqftCosts(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteSqftCostsResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/email/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteEmail(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteEmailResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/alerts/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAlerts(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteAlertsResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/chat/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteChat(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteChatResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/material/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteMaterial(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteMaterialResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/invoice/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteInvoice(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteInvoiceResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/default_lineal_foot_cost/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteDefaultLinealFootCost(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteDefaultLinealFootCostResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/trigger_type/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteTriggerType(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteTriggerTypeResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/job/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteJob(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteJobResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/photo/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deletePhoto(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeletePhotoResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/api_keys/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteApiKeys(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteApiKeysResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/change_order_description/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteChangeOrderDescription(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteChangeOrderDescriptionResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/analytic_log/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAnalyticLog(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteAnalyticLogResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/posts/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deletePosts(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeletePostsResponse>> $completion);
    
    @retrofit2.http.DELETE(value = "/v4/api/records/employee/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteEmployee(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.Nullable()
    java.lang.Integer id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.DeleteEmployeeResponse>> $completion);
    
    @retrofit2.http.POST(value = "/v4/api/records/subscription")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createSubscription(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CreateSubscriptionRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CreateSubscriptionResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/plan")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPlans(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetPlansResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/subscription")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getUserSubscriptions(@retrofit2.http.Query(value = "user_id")
    int userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetUserSubscriptionsResponse>> $completion);
    
    @retrofit2.http.PUT(value = "/v4/api/records/subscription/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object cancelSubscription(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.CancelSubscriptionRequest request, @retrofit2.http.Path(value = "id")
    int subscriptionId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.CancelSubscriptionResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/payment_history")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPaymentHistory(@retrofit2.http.Query(value = "user_id")
    int userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetPaymentHistoryResponse>> $completion);
    
    @retrofit2.http.GET(value = "/v4/api/records/subscription/{id}/status")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getSubscriptionStatus(@retrofit2.http.Path(value = "id")
    int subscriptionId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.manaknight.app.model.remote.GetSubscriptionStatusResponse>> $completion);
}