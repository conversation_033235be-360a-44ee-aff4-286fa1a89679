// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemDrawBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnAddDraw;

  @NonNull
  public final ImageView btnDelete;

  @NonNull
  public final ImageView btnEdit;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   */
  @Nullable
  public final MaterialButton btnReviews;

  @NonNull
  public final LinearLayout line1;

  @NonNull
  public final TextView name;

  @NonNull
  public final TextView txtAmount;

  @NonNull
  public final TextView txtDescription;

  @NonNull
  public final TextView txtPercentage;

  @NonNull
  public final TextView txtRemaing;

  private ItemDrawBinding(@NonNull ConstraintLayout rootView, @NonNull MaterialButton btnAddDraw,
      @NonNull ImageView btnDelete, @NonNull ImageView btnEdit, @Nullable MaterialButton btnReviews,
      @NonNull LinearLayout line1, @NonNull TextView name, @NonNull TextView txtAmount,
      @NonNull TextView txtDescription, @NonNull TextView txtPercentage,
      @NonNull TextView txtRemaing) {
    this.rootView = rootView;
    this.btnAddDraw = btnAddDraw;
    this.btnDelete = btnDelete;
    this.btnEdit = btnEdit;
    this.btnReviews = btnReviews;
    this.line1 = line1;
    this.name = name;
    this.txtAmount = txtAmount;
    this.txtDescription = txtDescription;
    this.txtPercentage = txtPercentage;
    this.txtRemaing = txtRemaing;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemDrawBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemDrawBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_draw, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemDrawBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnAddDraw;
      MaterialButton btnAddDraw = ViewBindings.findChildViewById(rootView, id);
      if (btnAddDraw == null) {
        break missingId;
      }

      id = R.id.btnDelete;
      ImageView btnDelete = ViewBindings.findChildViewById(rootView, id);
      if (btnDelete == null) {
        break missingId;
      }

      id = R.id.btnEdit;
      ImageView btnEdit = ViewBindings.findChildViewById(rootView, id);
      if (btnEdit == null) {
        break missingId;
      }

      id = R.id.btnReviews;
      MaterialButton btnReviews = ViewBindings.findChildViewById(rootView, id);

      id = R.id.line1;
      LinearLayout line1 = ViewBindings.findChildViewById(rootView, id);
      if (line1 == null) {
        break missingId;
      }

      id = R.id.name;
      TextView name = ViewBindings.findChildViewById(rootView, id);
      if (name == null) {
        break missingId;
      }

      id = R.id.txtAmount;
      TextView txtAmount = ViewBindings.findChildViewById(rootView, id);
      if (txtAmount == null) {
        break missingId;
      }

      id = R.id.txtDescription;
      TextView txtDescription = ViewBindings.findChildViewById(rootView, id);
      if (txtDescription == null) {
        break missingId;
      }

      id = R.id.txtPercentage;
      TextView txtPercentage = ViewBindings.findChildViewById(rootView, id);
      if (txtPercentage == null) {
        break missingId;
      }

      id = R.id.txtRemaing;
      TextView txtRemaing = ViewBindings.findChildViewById(rootView, id);
      if (txtRemaing == null) {
        break missingId;
      }

      return new ItemDrawBinding((ConstraintLayout) rootView, btnAddDraw, btnDelete, btnEdit,
          btnReviews, line1, name, txtAmount, txtDescription, txtPercentage, txtRemaing);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
