// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentProfileEditBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton buttonSave;

  @NonNull
  public final EditText editTextFirstName;

  @NonNull
  public final EditText editTextLastName;

  @NonNull
  public final ImageView ivEdit;

  @NonNull
  public final ImageView ivUser;

  @NonNull
  public final CardView materialCardView2;

  private FragmentProfileEditBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton buttonSave, @NonNull EditText editTextFirstName,
      @NonNull EditText editTextLastName, @NonNull ImageView ivEdit, @NonNull ImageView ivUser,
      @NonNull CardView materialCardView2) {
    this.rootView = rootView;
    this.buttonSave = buttonSave;
    this.editTextFirstName = editTextFirstName;
    this.editTextLastName = editTextLastName;
    this.ivEdit = ivEdit;
    this.ivUser = ivUser;
    this.materialCardView2 = materialCardView2;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentProfileEditBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentProfileEditBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_profile_edit, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentProfileEditBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonSave;
      MaterialButton buttonSave = ViewBindings.findChildViewById(rootView, id);
      if (buttonSave == null) {
        break missingId;
      }

      id = R.id.editTextFirstName;
      EditText editTextFirstName = ViewBindings.findChildViewById(rootView, id);
      if (editTextFirstName == null) {
        break missingId;
      }

      id = R.id.editTextLastName;
      EditText editTextLastName = ViewBindings.findChildViewById(rootView, id);
      if (editTextLastName == null) {
        break missingId;
      }

      id = R.id.ivEdit;
      ImageView ivEdit = ViewBindings.findChildViewById(rootView, id);
      if (ivEdit == null) {
        break missingId;
      }

      id = R.id.ivUser;
      ImageView ivUser = ViewBindings.findChildViewById(rootView, id);
      if (ivUser == null) {
        break missingId;
      }

      id = R.id.materialCardView2;
      CardView materialCardView2 = ViewBindings.findChildViewById(rootView, id);
      if (materialCardView2 == null) {
        break missingId;
      }

      return new FragmentProfileEditBinding((ConstraintLayout) rootView, buttonSave,
          editTextFirstName, editTextLastName, ivEdit, ivUser, materialCardView2);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
