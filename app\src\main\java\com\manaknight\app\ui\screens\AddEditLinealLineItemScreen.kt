package com.manaknight.app.ui.screens

import Manaknight.R
import android.widget.Toast
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Save
import androidx.compose.material3.OutlinedTextField
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.manaknight.app.model.remote.profitPro.CreateLineEntriesReqModel
import com.manaknight.app.model.remote.profitPro.CreateLineItemReqModel
import com.manaknight.app.model.remote.profitPro.LinearRespListModel
import com.manaknight.app.model.remote.profitPro.MaterialRespListModel2

import com.manaknight.app.viewmodels.BaasViewModel

import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState


import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.material3.*
import androidx.compose.ui.unit.sp
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.MoreVert
import com.manaknight.app.viewmodels.*

import androidx.compose.foundation.background

import androidx.compose.ui.*
import androidx.compose.ui.Alignment.Companion.CenterVertically
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.*
import com.manaknight.app.model.remote.ProjectResponseModel
import com.manaknight.app.model.remote.list
import com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel
import com.manaknight.app.ui.fragments.LineItemsComposeFragmentDirections
//import androidx.compose.ui.graphics.BorderStroke
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import android.os.Bundle
import android.util.Log
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.*
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.unit.dp
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.material3.*
import androidx.compose.ui.unit.sp
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.MoreVert
import com.manaknight.app.viewmodels.*
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import com.manaknight.app.utils.CustomUtils.formatDate
import androidx.compose.ui.window.Dialog
import androidx.navigation.fragment.findNavController
import com.manaknight.app.extensions.snackBarForDialog
import com.manaknight.app.ui.fragments.AddLineItemComposeFragmentDirections
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.foundation.layout.PaddingValues // Make sure this is imported
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.verticalScroll
import com.manaknight.app.ui.components.CustomCheckbox
import com.manaknight.app.ui.components.LineItemMasterDetailLayout
import com.manaknight.app.ui.components.LineItemsMasterPanel
import com.manaknight.app.ui.utils.isTabletLayout
import com.manaknight.app.ui.components.ResponsiveSheetContainer

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddEditLinealLineItemScreen(
    projectID: Int,
    isEditable: Int,
    lineItemNumber: Int,
    lineDescription: String,
    lineEstimateType: String,
    labourHours: String?,
    materialItem: MaterialRespListModel2?, // Renamed for consistency
    itemLineID: Int?,
    customerName: String,
    navController: NavController,
    baasViewModel: BaasViewModel
) {
    val linealListState = remember { mutableStateOf<List<LinearRespListModel>>(emptyList()) }
    val selectedLinearItemId = remember { mutableStateOf<Int?>(null) }
    val unitsState = remember { mutableStateOf(materialItem?.list?.firstOrNull()?.quantity?.toString() ?: "") }
    var showAddNewCostDialog by remember { mutableStateOf(false) }
    val context = LocalContext.current
    val sheetState = rememberModalBottomSheetState(
        skipPartiallyExpanded = true // Ensures it fully expands instead of partially
    )
    var showAddNewCostSheet by remember { mutableStateOf(false) }
//    val defaultHorizontalPadding = OutlinedTextFieldDefaults.contentPaddingWithLabel().calculateStartPadding(LayoutDirection.Ltr)




    LaunchedEffect(lineEstimateType) {
        baasViewModel.fetchSquareFootLinealFootCosts(lineEstimateType)
    }

    val linealCostsResource by baasViewModel.linealCostsResource.observeAsState()

    LaunchedEffect(linealCostsResource) {
        if (linealCostsResource?.status == com.manaknight.app.network.Status.SUCCESS) {
            linealListState.value = linealCostsResource!!.data?.list ?: emptyList()
            if (isEditable == 1 && materialItem?.list?.isNotEmpty() == true) {
                val matchingItem = materialItem.list.firstOrNull()
                linealListState.value.forEach { item ->
                    if (item.name == matchingItem?.name) {
                        selectedLinearItemId.value = item.id
                        unitsState.value = matchingItem?.quantity?.toString() ?: ""
                    }
                }
            }
        } else if (linealCostsResource?.status == com.manaknight.app.network.Status.ERROR) {
            Toast.makeText(context, "Error loading costs: ${linealCostsResource!!.message}", Toast.LENGTH_SHORT).show()
        }
    }

    if (isTabletLayout()) {
        // Tablet layout with full-width top bar
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // Full-width top bar for tablet
            AddEditLinealLineItemTopBar(
                customerName = customerName,
                isEditable = isEditable,
                navController = navController
            )

            // Master-detail layout below the top bar
            LineItemMasterDetailLayout(
                masterContent = {
                    LineItemsMasterPanel(
                        projectID = projectID,
                        customerName = customerName,
                        baasViewModel = baasViewModel,
                        navController = navController,
                        onAddLineItem = {
                            // Navigate to add line item screen
                            navController.popBackStack()
                        },
                        onDeleteItem = { lineItemId ->
                            // Handle delete if needed
                        }
                    )
                },
                detailContent = {
                    AddEditLinealLineItemDetailContent(
                        projectID = projectID,
                        isEditable = isEditable,
                        lineItemNumber = lineItemNumber,
                        lineDescription = lineDescription,
                        lineEstimateType = lineEstimateType,
                        labourHours = labourHours,
                        materialItem = materialItem,
                        itemLineID = itemLineID,
                        navController = navController,
                        baasViewModel = baasViewModel,
                        linealListState = linealListState,
                        selectedLinearItemId = selectedLinearItemId,
                        unitsState = unitsState,
                        showAddNewCostSheet = showAddNewCostSheet,
                        onShowAddNewCostSheetChange = { showAddNewCostSheet = it },
                        showTopBar = false, // Hide the detail's top bar on tablet
                        showDetailHeader = true // Show the detail panel header with buttons
                    )
                }
            )
        }
    } else {
        // Mobile layout - keep existing implementation
        LineItemMasterDetailLayout(
            masterContent = {
                // No master content on mobile
            },
            detailContent = {
                AddEditLinealLineItemDetailContent(
                    projectID = projectID,
                    isEditable = isEditable,
                    lineItemNumber = lineItemNumber,
                    lineDescription = lineDescription,
                    lineEstimateType = lineEstimateType,
                    labourHours = labourHours,
                    materialItem = materialItem,
                    itemLineID = itemLineID,
                    navController = navController,
                    baasViewModel = baasViewModel,
                    linealListState = linealListState,
                    selectedLinearItemId = selectedLinearItemId,
                    unitsState = unitsState,
                    showAddNewCostSheet = showAddNewCostSheet,
                    onShowAddNewCostSheetChange = { showAddNewCostSheet = it },
                    showTopBar = true, // Show the detail's top bar on mobile
                    showDetailHeader = false // Don't show detail header on mobile
                )
            }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AddEditLinealLineItemTopBar(
    customerName: String,
    isEditable: Int,
    navController: NavController
) {
    CenterAlignedTopAppBar(
        title = {
            Text(
                text = customerName,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = colorResource(R.color.profit_black)
            )
        },
        navigationIcon = {
            IconButton(onClick = { navController.popBackStack() }) {
                Icon(Icons.Filled.ArrowBack, contentDescription = "Back")
            }
        },
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
            containerColor = Color.White
        ),
        modifier = Modifier.fillMaxWidth()
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AddEditLinealLineItemDetailContent(
    projectID: Int,
    isEditable: Int,
    lineItemNumber: Int,
    lineDescription: String,
    lineEstimateType: String,
    labourHours: String?,
    materialItem: MaterialRespListModel2?,
    itemLineID: Int?,
    navController: NavController,
    baasViewModel: BaasViewModel,
    linealListState: MutableState<List<LinearRespListModel>>,
    selectedLinearItemId: MutableState<Int?>,
    unitsState: MutableState<String>,
    showAddNewCostSheet: Boolean,
    onShowAddNewCostSheetChange: (Boolean) -> Unit,
    showTopBar: Boolean = true,
    showDetailHeader: Boolean = false
) {
    val context = LocalContext.current
    val sheetState = rememberModalBottomSheetState(
        skipPartiallyExpanded = true
    )

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Detail header for tablet (with Save and Cancel buttons)
        if (showDetailHeader) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = if (isEditable == 1) "Update Line Item" else "Add Line Item",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    color = colorResource(R.color.profit_black)
                )

                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Button(
                        shape = RoundedCornerShape(4.dp),
                        colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.profit_blue)),
                        contentPadding = PaddingValues(horizontal = 12.dp, vertical = 6.dp),
                        onClick = {
                            selectedLinearItemId.value?.let { selectedId ->
                                val selectedItem =
                                    linealListState.value.find { it.id == selectedId }
                                selectedItem?.let { item ->
                                    saveLinealLineItem(
                                        isEditable = isEditable,
                                        projectID = projectID,
                                        lineDescription = lineDescription,
                                        lineEstimateType = lineEstimateType,
                                        labourHours = labourHours?.toIntOrNull() ?: 0,
                                        selectedItem = item,
                                        units = unitsState.value.toIntOrNull() ?: 0,
                                        itemLineID = itemLineID,
                                        baasViewModel = baasViewModel,
                                        navController = navController
                                    )
                                } ?: run {
                                    Toast.makeText(
                                        context,
                                        "Please select a cost item",
                                        Toast.LENGTH_SHORT
                                    ).show()
                                }
                            } ?: run {
                                Toast.makeText(
                                    context,
                                    "Please select a cost item",
                                    Toast.LENGTH_SHORT
                                ).show()
                            }
                        },
                        enabled = selectedLinearItemId.value != null
                    ) {
                        Text(
                            text = "Save",
                            fontSize = 16.sp,
                            color = Color.White
                        )
                    }

                    Button(
                        shape = RoundedCornerShape(4.dp),
                        colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
                        border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
                        contentPadding = PaddingValues(horizontal = 12.dp, vertical = 6.dp),
                        onClick = { navController.popBackStack() }
                    ) {
                        Text(
                            text = "Cancel",
                            fontSize = 16.sp,
                            color = colorResource(R.color.profit_blue)
                        )
                    }
                }
            }
        }

        // Main content with optional top bar
        Surface(color = Color.White) {
            Scaffold(
                containerColor = Color.White,
                topBar = {
                    if (showTopBar) {
                        CenterAlignedTopAppBar(
                            title = {
                                Text(
                                    if (isEditable == 1) "Update Line Item"
                                    else "New Line Item #${lineItemNumber + 1}",
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.Medium,
                                    color = colorResource(R.color.profit_black)
                                )
                            },
                            navigationIcon = {
                                IconButton(onClick = { navController.popBackStack() }) {
                                    Icon(Icons.Filled.ArrowBack, contentDescription = "Back")
                                }
                            },
                            actions = {
                                Button(
                                    onClick = {
                                        selectedLinearItemId.value?.let { selectedId ->
                                            val selectedItem =
                                                linealListState.value.find { it.id == selectedId }
                                            selectedItem?.let { item ->
                                                saveLinealLineItem(
                                                    isEditable = isEditable,
                                                    projectID = projectID,
                                                    lineDescription = lineDescription,
                                                    lineEstimateType = lineEstimateType,
                                                    labourHours = labourHours?.toIntOrNull() ?: 0,
                                                    selectedItem = item,
                                                    units = unitsState.value.toIntOrNull() ?: 0,
                                                    itemLineID = itemLineID,
                                                    baasViewModel = baasViewModel,
                                                    navController = navController
                                                )
                                            } ?: run {
                                                Toast.makeText(
                                                    context,
                                                    "Please select a cost item",
                                                    Toast.LENGTH_SHORT
                                                ).show()
                                            }
                                        } ?: run {
                                            Toast.makeText(
                                                context,
                                                "Please select a cost item",
                                                Toast.LENGTH_SHORT
                                            ).show()
                                        }
                                    },
                                    shape = RoundedCornerShape(4.dp),
                                    colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
                                    border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
                                    contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp),
                                    modifier = Modifier
                                        .height(28.dp) // Adjust height as needed
                                ) {
                                    Text(
                                        "Save", fontSize = 16.sp,
                                        color = colorResource(R.color.profit_blue)
                                    )
                                }

                            },
                            colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                                containerColor = Color.White
                            )

                        )
                    }
                },
            content = { paddingValues ->
                Column(
                    modifier = Modifier
                        .padding(paddingValues)
                        .padding(16.dp)
                        .fillMaxSize()
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = if (lineEstimateType == "square_foot") "Square Foot Costs" else "Linear Foot Costs",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            color = colorResource(id = R.color.gray)
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(
                            onClick = { onShowAddNewCostSheetChange(true) },
                            shape = RoundedCornerShape(4.dp),
                            colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
                            border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
                            contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp),
                            modifier = Modifier
                                .height(28.dp) // Adjust height as needed)
                        ) {
                            Text(
                                "Add New Cost", fontSize = 16.sp,
                                color = colorResource(R.color.profit_blue)
                            )
                        }
                    }
                    Spacer(modifier = Modifier.height(16.dp))
                    LazyColumn {
                        items(linealListState.value) { item ->
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 4.dp)
                                    .border(
                                        width = 1.dp,
                                        color = colorResource(R.color.gray),
                                        shape = RoundedCornerShape(8.dp)
                                    )
                            ) {
                                RadioButton(
                                    selected = selectedLinearItemId.value == item.id,
                                    onClick = { selectedLinearItemId.value = item.id },
                                    colors = RadioButtonDefaults.colors(
                                        selectedColor = colorResource(R.color.brand_green)
                                    )
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(text = item.name ?: "No Name", modifier = Modifier.weight(1f))
                            }
                            if (selectedLinearItemId.value == item.id) {
                                Column(
                                    modifier = Modifier.padding(start = 24.dp)
                                ) {
                                    Spacer(modifier = Modifier.height(4.dp))
                                    Text(
                                        text = buildAnnotatedString {
                                            // Define the base text
                                            val baseText = if (lineEstimateType == "square_foot") "Square Foot" else "Linear Foot"

                                            // Append the asterisk with red color
                                            withStyle(style = SpanStyle(color = Color.Red)) { // Using Color.Red directly
                                                append("*")
                                            }

                                            // Append the base text with black color
                                            withStyle(style = SpanStyle(color = colorResource(R.color.black))) {
                                                append(baseText)
                                            }
                                        },
                                        fontSize = 16.sp,

                                    )

                                    Spacer(modifier = Modifier.height(4.dp))
                                    BasicTextField(
                                        value = unitsState.value,
                                        onValueChange = { unitsState.value = it },
                                        singleLine = true,
                                        textStyle = LocalTextStyle.current.copy(color = Color.Black),
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .defaultMinSize(minHeight = 40.dp)
                                            .border(1.dp, Color.Gray, shape = RoundedCornerShape(8.dp))
                                            .padding(horizontal = 12.dp, vertical = 10.dp),
                                        decorationBox = { innerTextField ->
                                            if (unitsState.value.isEmpty()) {
                                                Text(
                                                    "Units",
                                                    color = Color.Gray,
                                                    style = MaterialTheme.typography.bodyMedium
                                                )
                                            }
                                            innerTextField()
                                        }
                                    )






                                }
                            }
                        }
                    }
                }
                },
                bottomBar = {
                    // No bottom bar in your provided UI
                }
            )
        }
    }

    if (showAddNewCostSheet) {
        ResponsiveSheetContainer(
            showSheet = showAddNewCostSheet,
            onDismiss = { onShowAddNewCostSheetChange(false) },
            sheetState = sheetState,
            headerContent = {
                AddNewLinearOrSquareCostDialogHeader(
                    lineEstimateType = lineEstimateType,
                    onDismiss = { onShowAddNewCostSheetChange(false) }
                )
            },
            content = {
                AddNewLinearOrSquareCostDialogContent(
                    onDismissRequest = { onShowAddNewCostSheetChange(false) },
                    onCostAdded = {
                        // Refresh the list after a new cost is added
                        baasViewModel.fetchSquareFootLinealFootCosts(lineEstimateType)
                    },
                    lineEstimateType = lineEstimateType,
                    baasViewModel = baasViewModel
                )
            },
            height = 600.dp
        )
    }
}

@Composable
fun AddNewLinearOrSquareCostDialog(
    onDismissRequest: () -> Unit,
    onCostAdded: () -> Unit, // Callback to refresh the list
    lineEstimateType: String,
    baasViewModel: BaasViewModel
) {
    var costName by remember { mutableStateOf("") }
    var costValue by remember { mutableStateOf("") }
    var laborCost by remember { mutableStateOf("") }
    var profitOverhead by remember { mutableStateOf("") }
    var materialCost by remember { mutableStateOf("") }
    val context = LocalContext.current
    // State to hold whether the checkbox is checked
    var isChecked by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxHeight( 0.6f) // Fill whatever space the bottom sheet gives it
            .fillMaxWidth()
            .verticalScroll(rememberScrollState()) // Allow scroll if content is too tall
            .background(colorResource(id = R.color.white), shape = RoundedCornerShape(8.dp))
            .padding(16.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onDismissRequest) {
                Icon(Icons.Filled.Close, contentDescription = "Close")
            }
            Text(
                text = if (lineEstimateType == "square_foot") "New Square Foot Cost" else "New Linear Foot Cost",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = colorResource(id = R.color.profit_black)
            )

            Button (
                shape = RoundedCornerShape(4.dp),
                colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
                border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
                contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp),
                modifier = Modifier
                    .padding(2.dp)
                    .height(28.dp)
                    .wrapContentWidth()
                    .defaultMinSize(minWidth = 0.dp),
                onClick = {
                    if (costName.isNotBlank() && costValue.isNotBlank() && laborCost.isNotBlank()) {
                        val cost = costValue.toDoubleOrNull() ?: 0.0
                        val labor = laborCost.toDoubleOrNull() ?: 0.0
                        if (lineEstimateType == "square_foot") {
                            baasViewModel.addSquareFootCost(costName, cost.toInt(), labor.toInt(), 0)
                                .observeForever { resource ->
                                    handleCostAdditionResponse(resource, onCostAdded, onDismissRequest, context)
                                }
                        } else {
                            baasViewModel.addLinealFootCost(costName, cost.toInt(), labor.toInt(), 0)
                                .observeForever { resource ->
                                    handleCostAdditionResponse(resource, onCostAdded, onDismissRequest, context)
                                }
                        }
                    } else {
                        Toast.makeText(context, "Please fill in all fields", Toast.LENGTH_SHORT).show()
                    }
                },
            ) {
                Text(
                    text = "Save",
                    fontSize = 16.sp,
                    color = colorResource(R.color.profit_blue)
                )
            }

        }
        Spacer(modifier = Modifier.height(16.dp))

//        Name
        Text(
            text = if (lineEstimateType == "square_foot") "Name" else "Name",
            color = colorResource(R.color.profit_black),
            fontWeight = FontWeight.Medium,
            fontSize = 14.sp,
            modifier = Modifier.padding(bottom = 4.dp)
        )
        BasicTextField(
            value = costName,
            onValueChange = { costName = it },
            singleLine = true,
            textStyle = LocalTextStyle.current.copy(
                fontSize = 16.sp,
                color = Color.Black // Change to match your theme
            ),
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White, shape = RoundedCornerShape(10.dp))
                .border(1.dp, Color.Gray, shape = RoundedCornerShape(10.dp))
                .padding(horizontal = 12.dp, vertical = 8.dp),
            decorationBox = { innerTextField ->
                Column {

                    innerTextField()
                }
            }
        )

//        foot cost

        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = if (lineEstimateType == "square_foot") "Square Foot Cost" else "Linear Foot Cost",
            color = colorResource(R.color.profit_black),
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 4.dp)
        )

        BasicTextField(
            value = costValue,
            onValueChange = { costValue = it },
            singleLine = true,
            textStyle = LocalTextStyle.current.copy(
                fontSize = 16.sp,
                color = Color.Black // adjust if needed
            ),
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White, shape = RoundedCornerShape(10.dp))
                .border(1.dp, Color.Gray, shape = RoundedCornerShape(10.dp))
                .padding(horizontal = 12.dp, vertical = 8.dp), // controls spacing
            decorationBox = { innerTextField ->
                Column {

                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            "$",
                            style = MaterialTheme.typography.bodyLarge,
                            modifier = Modifier.padding(end = 4.dp)
                        )
                        innerTextField()
                    }
                }
            }
        )

//        profit overhead

        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = "Profit overhead",
            color = colorResource(R.color.profit_black),
            fontWeight = FontWeight.Medium,
            fontSize = 14.sp,
            modifier = Modifier.padding(bottom = 4.dp)
        )
        BasicTextField(
            value = profitOverhead,
            onValueChange = { profitOverhead = it },
            singleLine = true,
            textStyle = LocalTextStyle.current.copy(fontSize = 16.sp),
            decorationBox = { innerTextField ->
                Row(
                    modifier = Modifier
                        .background(Color.Transparent)
                        .border(1.dp, Color.Gray, RoundedCornerShape(10.dp))
                        .padding(horizontal = 12.dp, vertical = 10.dp)
                        .fillMaxWidth()
                ) {
                    Text(
                        "$",
                        style = MaterialTheme.typography.bodyLarge,
                        modifier = Modifier.padding(end = 4.dp)
                    )
                    innerTextField()
                }
            }
        )

        Text(
            text = "$ Remaining",
            style = MaterialTheme.typography.labelSmall,
            color = Color.Gray,
            modifier = Modifier.padding(bottom = 4.dp)
        )

//        cost labor

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = "Cost labor",
            color = colorResource(R.color.profit_black),
            fontWeight = FontWeight.Medium,
            fontSize = 14.sp,
            modifier = Modifier.padding(bottom = 4.dp)
        )
        BasicTextField(
            value = laborCost,
            onValueChange = { laborCost = it },
            singleLine = true,
            textStyle = LocalTextStyle.current.copy(fontSize = 16.sp),
            decorationBox = { innerTextField ->
                Row(
                    modifier = Modifier
                        .background(Color.Transparent)
                        .border(1.dp, Color.Gray, RoundedCornerShape(10.dp))
                        .padding(horizontal = 12.dp, vertical = 10.dp)
                        .fillMaxWidth()
                ) {
                    Text(
                        "$",
                        style = MaterialTheme.typography.bodyLarge,
                        modifier = Modifier.padding(end = 4.dp)
                    )
                    innerTextField()
                }
            }
        )
//        Material cost

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = "Material cost",
            color = colorResource(R.color.profit_black),
            fontWeight = FontWeight.Medium,
            fontSize = 14.sp,
            modifier = Modifier.padding(bottom = 4.dp)

        )
        BasicTextField(
            value = materialCost,
            onValueChange = { materialCost = it },
            singleLine = true,
            textStyle = LocalTextStyle.current.copy(fontSize = 16.sp),
            decorationBox = { innerTextField ->
                Row(
                    modifier = Modifier
                        .background(Color.Transparent)
                        .border(1.dp, Color.Gray, RoundedCornerShape(10.dp))
                        .padding(horizontal = 12.dp, vertical = 10.dp)
                        .fillMaxWidth()
                ) {
                    Text(
                        "$",
                        style = MaterialTheme.typography.bodyLarge,
                        modifier = Modifier.padding(end = 4.dp)
                    )
                    innerTextField()
                }
            }
        )
        CustomCheckboxRow(
            isChecked = isChecked,
            onCheckedChange = { isChecked = it }
        )

        Spacer(modifier = Modifier.height(16.dp))




    }
}

// Header and Content components for AddNewLinearOrSquareCostDialog ResponsiveSheetContainer
@Composable
fun AddNewLinearOrSquareCostDialogHeader(
    lineEstimateType: String,
    onDismiss: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(onClick = onDismiss) {
            Icon(Icons.Filled.Close, contentDescription = "Close")
        }
        Text(
            text = if (lineEstimateType == "square_foot") "New Square Foot Cost" else "New Linear Foot Cost",
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            color = colorResource(id = R.color.profit_black)
        )
        // Empty space to balance the layout
        Spacer(modifier = Modifier.width(48.dp))
    }
}

@Composable
fun AddNewLinearOrSquareCostDialogContent(
    onDismissRequest: () -> Unit,
    onCostAdded: () -> Unit,
    lineEstimateType: String,
    baasViewModel: BaasViewModel
) {
    var costName by remember { mutableStateOf("") }
    var costValue by remember { mutableStateOf("") }
    var laborCost by remember { mutableStateOf("") }
    var profitOverhead by remember { mutableStateOf("") }
    var materialCost by remember { mutableStateOf("") }
    val context = LocalContext.current
    var isChecked by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .verticalScroll(rememberScrollState())
            .padding(horizontal = 16.dp, vertical = 8.dp)
    ) {
        // Name field
        Text(
            text = "Name",
            color = colorResource(R.color.profit_black),
            fontWeight = FontWeight.Medium,
            fontSize = 14.sp,
            modifier = Modifier.padding(bottom = 4.dp)
        )
        BasicTextField(
            value = costName,
            onValueChange = { costName = it },
            singleLine = true,
            textStyle = LocalTextStyle.current.copy(
                fontSize = 16.sp,
                color = Color.Black
            ),
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White, shape = RoundedCornerShape(10.dp))
                .border(1.dp, Color.Gray, shape = RoundedCornerShape(10.dp))
                .padding(horizontal = 12.dp, vertical = 8.dp),
            decorationBox = { innerTextField ->
                Column {
                    innerTextField()
                }
            }
        )

        Spacer(modifier = Modifier.height(8.dp))

        // Cost field
        Text(
            text = if (lineEstimateType == "square_foot") "Square Foot Cost" else "Linear Foot Cost",
            color = colorResource(R.color.profit_black),
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 4.dp)
        )
        BasicTextField(
            value = costValue,
            onValueChange = { costValue = it },
            singleLine = true,
            textStyle = LocalTextStyle.current.copy(
                fontSize = 16.sp,
                color = Color.Black
            ),
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White, shape = RoundedCornerShape(10.dp))
                .border(1.dp, Color.Gray, shape = RoundedCornerShape(10.dp))
                .padding(horizontal = 12.dp, vertical = 8.dp),
            decorationBox = { innerTextField ->
                Column {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            "$",
                            style = MaterialTheme.typography.bodyLarge,
                            modifier = Modifier.padding(end = 4.dp)
                        )
                        innerTextField()
                    }
                }
            }
        )

        Spacer(modifier = Modifier.height(8.dp))

        // Profit overhead field
        Text(
            text = "Profit overhead",
            color = colorResource(R.color.profit_black),
            fontWeight = FontWeight.Medium,
            fontSize = 14.sp,
            modifier = Modifier.padding(bottom = 4.dp)
        )
        BasicTextField(
            value = profitOverhead,
            onValueChange = { profitOverhead = it },
            singleLine = true,
            textStyle = LocalTextStyle.current.copy(fontSize = 16.sp),
            decorationBox = { innerTextField ->
                Row(
                    modifier = Modifier
                        .background(Color.Transparent)
                        .border(1.dp, Color.Gray, RoundedCornerShape(10.dp))
                        .padding(horizontal = 12.dp, vertical = 10.dp)
                        .fillMaxWidth()
                ) {
                    Text(
                        "$",
                        style = MaterialTheme.typography.bodyLarge,
                        modifier = Modifier.padding(end = 4.dp)
                    )
                    innerTextField()
                }
            }
        )

        Spacer(modifier = Modifier.height(8.dp))

        // Labor cost field
        Text(
            text = "Cost labor",
            color = colorResource(R.color.profit_black),
            fontWeight = FontWeight.Medium,
            fontSize = 14.sp,
            modifier = Modifier.padding(bottom = 4.dp)
        )
        BasicTextField(
            value = laborCost,
            onValueChange = { laborCost = it },
            singleLine = true,
            textStyle = LocalTextStyle.current.copy(fontSize = 16.sp),
            decorationBox = { innerTextField ->
                Row(
                    modifier = Modifier
                        .background(Color.Transparent)
                        .border(1.dp, Color.Gray, RoundedCornerShape(10.dp))
                        .padding(horizontal = 12.dp, vertical = 10.dp)
                        .fillMaxWidth()
                ) {
                    Text(
                        "$",
                        style = MaterialTheme.typography.bodyLarge,
                        modifier = Modifier.padding(end = 4.dp)
                    )
                    innerTextField()
                }
            }
        )

        Spacer(modifier = Modifier.height(8.dp))

        // Material cost field
        Text(
            text = "Material cost",
            color = colorResource(R.color.profit_black),
            fontWeight = FontWeight.Medium,
            fontSize = 14.sp,
            modifier = Modifier.padding(bottom = 4.dp)
        )
        BasicTextField(
            value = materialCost,
            onValueChange = { materialCost = it },
            singleLine = true,
            textStyle = LocalTextStyle.current.copy(fontSize = 16.sp),
            decorationBox = { innerTextField ->
                Row(
                    modifier = Modifier
                        .background(Color.Transparent)
                        .border(1.dp, Color.Gray, RoundedCornerShape(10.dp))
                        .padding(horizontal = 12.dp, vertical = 10.dp)
                        .fillMaxWidth()
                ) {
                    Text(
                        "$",
                        style = MaterialTheme.typography.bodyLarge,
                        modifier = Modifier.padding(end = 4.dp)
                    )
                    innerTextField()
                }
            }
        )

        CustomCheckboxRow(
            isChecked = isChecked,
            onCheckedChange = { isChecked = it }
        )

        Spacer(modifier = Modifier.height(24.dp))

        // Save button
        Button(
            shape = RoundedCornerShape(8.dp),
            colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.profit_blue)),
            modifier = Modifier
                .fillMaxWidth()
                .height(48.dp),
            onClick = {
                if (costName.isNotBlank() && costValue.isNotBlank() && laborCost.isNotBlank()) {
                    val cost = costValue.toDoubleOrNull() ?: 0.0
                    val labor = laborCost.toDoubleOrNull() ?: 0.0
                    if (lineEstimateType == "square_foot") {
                        baasViewModel.addSquareFootCost(costName, cost.toInt(), labor.toInt(), 0)
                            .observeForever { resource ->
                                handleCostAdditionResponse(resource, onCostAdded, onDismissRequest, context)
                            }
                    } else {
                        baasViewModel.addLinealFootCost(costName, cost.toInt(), labor.toInt(), 0)
                            .observeForever { resource ->
                                handleCostAdditionResponse(resource, onCostAdded, onDismissRequest, context)
                            }
                    }
                } else {
                    Toast.makeText(context, "Please fill in all fields", Toast.LENGTH_SHORT).show()
                }
            }
        ) {
            Text(
                text = "Save",
                fontSize = 16.sp,
                color = Color.White
            )
        }
    }
}

@Composable
fun CustomCheckboxRow(
    isChecked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable {
                onCheckedChange(!isChecked) // Toggle on row click
            }
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        CustomCheckbox(
            checked = isChecked,
            onCheckedChange = { newCheckedState ->
                onCheckedChange(newCheckedState) // Update on checkbox click
            }
        )
        Text(
            text = "Add to default material list", // Replace with your actual label
            fontWeight = FontWeight.Medium,
            fontSize = 14.sp,
            modifier = Modifier.padding(start = 4.dp)
        )
    }
}


private fun handleCostAdditionResponse(
    response: com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse>,
    onCostAdded: () -> Unit,
    onDismissRequest: () -> Unit,
    context: android.content.Context
) {
    when (response.status) {
        com.manaknight.app.network.Status.SUCCESS -> {
            onCostAdded()
            onDismissRequest()
        }
        com.manaknight.app.network.Status.ERROR -> {
            Toast.makeText(context, response.message ?: "Error adding cost", Toast.LENGTH_SHORT).show()
        }
        com.manaknight.app.network.Status.LOADING -> {
            // TODO: Show loading indicator if needed
        }
    }
}

fun saveLinealLineItem(
    isEditable: Int,
    projectID: Int,
    lineDescription: String,
    lineEstimateType: String,
    labourHours: Int,
    selectedItem: LinearRespListModel,
    units: Int,
    itemLineID: Int?,
    baasViewModel: BaasViewModel,
    navController: NavController
) {
    if (isEditable == 1 && itemLineID != null) {
        val updateLineEntries = listOf(
            com.manaknight.app.model.remote.profitPro.UpdateLineEntriesReqModel(
                quantity = units,
                item_id = selectedItem.id ?: 0,
                id = selectedItem.selectedID,
                name = selectedItem.name ?: ""
            )
        )
        baasViewModel.updateLineItem(
            itemLineID,
            com.manaknight.app.model.remote.profitPro.UpdateLineItemReqModel(
                lineDescription,
                lineEstimateType,
                projectID,
                labourHours,
                updateLineEntries
            )
        ).observeForever { resource ->
            if (resource.status == com.manaknight.app.network.Status.SUCCESS) {
                // Update project status to draft
                updateProjectStatusToDraft(projectID, baasViewModel)
                navController.popBackStack()
                navController.popBackStack()
            } else if (resource.status == com.manaknight.app.network.Status.ERROR) {
                Toast.makeText(navController.context, resource.message, Toast.LENGTH_SHORT).show()
            }
        }
    } else {
        val createLineEntries = listOf(
            com.manaknight.app.model.remote.profitPro.CreateLineEntriesReqModel(
                quantity = units,
                item_id = selectedItem.id ?: 0
            )
        )
        baasViewModel.addLineItem(
            com.manaknight.app.model.remote.profitPro.CreateLineItemReqModel(
                lineDescription,
                lineEstimateType,
                projectID,
                labourHours,
                createLineEntries
            )
        ).observeForever { resource ->
            if (resource.status == com.manaknight.app.network.Status.SUCCESS) {
                // Update project status to draft
                updateProjectStatusToDraft(projectID, baasViewModel)
                navController.popBackStack()
                navController.popBackStack()
            } else if (resource.status == com.manaknight.app.network.Status.ERROR) {
                Toast.makeText(navController.context, resource.message, Toast.LENGTH_SHORT).show()
            }
        }
    }
}

private fun updateProjectStatusToDraft(projectID: Int, baasViewModel: BaasViewModel) {
    // Get project details to retrieve necessary data for updateProject
    baasViewModel.getSingleProjectDetails(projectID).observeForever { projectResource ->
        if (projectResource.status == com.manaknight.app.network.Status.SUCCESS && projectResource.data?.error == false) {
            val clientDetails = projectResource.data?.client_details
            val totals = projectResource.data?.totals
            val changeCount = 0
            val customerId = clientDetails?.customer_id ?: 0
            val userId = clientDetails?.user_id ?: 0
            val profitOverhead = totals?.total_profit_overhead?.toString() ?: "0"
            val hourlyRate = totals?.labour_budget?.toString() ?: "0"

            baasViewModel.updateProject(
                changeCount = changeCount,
                customerId = customerId,
                userId = userId,
                status = 3, // draft
                profitOverhead = profitOverhead,
                hourlyRate = hourlyRate,
                id = projectID
            ).observeForever { updateResource ->
                when (updateResource.status) {
                    com.manaknight.app.network.Status.LOADING -> {
                        // Show loading if needed
                    }
                    com.manaknight.app.network.Status.SUCCESS -> {
                        if (updateResource.data?.error == false) {
                            Log.d("AddEditLinealLineItemScreen", "Project status updated to Draft (3)")
                        }
                    }
                    com.manaknight.app.network.Status.ERROR -> {
                        Log.e("AddEditLinealLineItemScreen", "Failed to update project status: ${updateResource.message}")
                    }
                }
            }
        }
    }
}