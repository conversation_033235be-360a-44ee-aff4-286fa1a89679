// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class BottomAddLinealBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView addHeading;

  @NonNull
  public final ImageView backButton;

  @NonNull
  public final MaterialButton btnSave;

  @NonNull
  public final CheckBox checkBox1;

  @NonNull
  public final EditText edTxtLabourCost;

  @NonNull
  public final EditText edTxtLinealCost;

  @NonNull
  public final EditText edTxtLinealName;

  @NonNull
  public final EditText edTxtMaterialCost;

  @NonNull
  public final EditText edTxtProfitOverHead;

  @NonNull
  public final LinearLayout header;

  @NonNull
  public final LinearLayout line1;

  @NonNull
  public final LinearLayout linearDefaultHourlyRate2;

  @NonNull
  public final ScrollView mainLayout;

  @NonNull
  public final TextView remaingCost;

  @NonNull
  public final TextView tvFullName;

  @NonNull
  public final TextView tvFullNameError;

  @NonNull
  public final TextView tvMaterialCostError;

  private BottomAddLinealBinding(@NonNull LinearLayout rootView, @NonNull TextView addHeading,
      @NonNull ImageView backButton, @NonNull MaterialButton btnSave, @NonNull CheckBox checkBox1,
      @NonNull EditText edTxtLabourCost, @NonNull EditText edTxtLinealCost,
      @NonNull EditText edTxtLinealName, @NonNull EditText edTxtMaterialCost,
      @NonNull EditText edTxtProfitOverHead, @NonNull LinearLayout header,
      @NonNull LinearLayout line1, @NonNull LinearLayout linearDefaultHourlyRate2,
      @NonNull ScrollView mainLayout, @NonNull TextView remaingCost, @NonNull TextView tvFullName,
      @NonNull TextView tvFullNameError, @NonNull TextView tvMaterialCostError) {
    this.rootView = rootView;
    this.addHeading = addHeading;
    this.backButton = backButton;
    this.btnSave = btnSave;
    this.checkBox1 = checkBox1;
    this.edTxtLabourCost = edTxtLabourCost;
    this.edTxtLinealCost = edTxtLinealCost;
    this.edTxtLinealName = edTxtLinealName;
    this.edTxtMaterialCost = edTxtMaterialCost;
    this.edTxtProfitOverHead = edTxtProfitOverHead;
    this.header = header;
    this.line1 = line1;
    this.linearDefaultHourlyRate2 = linearDefaultHourlyRate2;
    this.mainLayout = mainLayout;
    this.remaingCost = remaingCost;
    this.tvFullName = tvFullName;
    this.tvFullNameError = tvFullNameError;
    this.tvMaterialCostError = tvMaterialCostError;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static BottomAddLinealBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static BottomAddLinealBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.bottom_add_lineal, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static BottomAddLinealBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addHeading;
      TextView addHeading = ViewBindings.findChildViewById(rootView, id);
      if (addHeading == null) {
        break missingId;
      }

      id = R.id.backButton;
      ImageView backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.btnSave;
      MaterialButton btnSave = ViewBindings.findChildViewById(rootView, id);
      if (btnSave == null) {
        break missingId;
      }

      id = R.id.checkBox1;
      CheckBox checkBox1 = ViewBindings.findChildViewById(rootView, id);
      if (checkBox1 == null) {
        break missingId;
      }

      id = R.id.edTxtLabourCost;
      EditText edTxtLabourCost = ViewBindings.findChildViewById(rootView, id);
      if (edTxtLabourCost == null) {
        break missingId;
      }

      id = R.id.edTxtLinealCost;
      EditText edTxtLinealCost = ViewBindings.findChildViewById(rootView, id);
      if (edTxtLinealCost == null) {
        break missingId;
      }

      id = R.id.edTxtLinealName;
      EditText edTxtLinealName = ViewBindings.findChildViewById(rootView, id);
      if (edTxtLinealName == null) {
        break missingId;
      }

      id = R.id.edTxtMaterialCost;
      EditText edTxtMaterialCost = ViewBindings.findChildViewById(rootView, id);
      if (edTxtMaterialCost == null) {
        break missingId;
      }

      id = R.id.edTxtProfitOverHead;
      EditText edTxtProfitOverHead = ViewBindings.findChildViewById(rootView, id);
      if (edTxtProfitOverHead == null) {
        break missingId;
      }

      id = R.id.header;
      LinearLayout header = ViewBindings.findChildViewById(rootView, id);
      if (header == null) {
        break missingId;
      }

      id = R.id.line1;
      LinearLayout line1 = ViewBindings.findChildViewById(rootView, id);
      if (line1 == null) {
        break missingId;
      }

      id = R.id.linearDefaultHourlyRate2;
      LinearLayout linearDefaultHourlyRate2 = ViewBindings.findChildViewById(rootView, id);
      if (linearDefaultHourlyRate2 == null) {
        break missingId;
      }

      id = R.id.mainLayout;
      ScrollView mainLayout = ViewBindings.findChildViewById(rootView, id);
      if (mainLayout == null) {
        break missingId;
      }

      id = R.id.remaingCost;
      TextView remaingCost = ViewBindings.findChildViewById(rootView, id);
      if (remaingCost == null) {
        break missingId;
      }

      id = R.id.tvFullName;
      TextView tvFullName = ViewBindings.findChildViewById(rootView, id);
      if (tvFullName == null) {
        break missingId;
      }

      id = R.id.tvFullNameError;
      TextView tvFullNameError = ViewBindings.findChildViewById(rootView, id);
      if (tvFullNameError == null) {
        break missingId;
      }

      id = R.id.tvMaterialCostError;
      TextView tvMaterialCostError = ViewBindings.findChildViewById(rootView, id);
      if (tvMaterialCostError == null) {
        break missingId;
      }

      return new BottomAddLinealBinding((LinearLayout) rootView, addHeading, backButton, btnSave,
          checkBox1, edTxtLabourCost, edTxtLinealCost, edTxtLinealName, edTxtMaterialCost,
          edTxtProfitOverHead, header, line1, linearDefaultHourlyRate2, mainLayout, remaingCost,
          tvFullName, tvFullNameError, tvMaterialCostError);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
