[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_tab_team.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\tab_team.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_new_unchecked.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\new_unchecked.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_google.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\google.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_bottom_add_employee.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\bottom_add_employee.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_chevron_bottom.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\chevron_bottom.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_tab_cost_unselected.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\tab_cost_unselected.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_create_customer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_create_customer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_send_text_message_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\send_text_message_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_rounded_textview_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\rounded_textview_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_green_check.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\green_check.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_check.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\check.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_bottom_add_employee.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\bottom_add_employee.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_custom_checkbox_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\custom_checkbox_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_bg_round_corners_2.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\bg_round_corners_2.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_costview.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_costview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_bottom_sheet_multi_select_status_filter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\bottom_sheet_multi_select_status_filter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_ic_info_custom_fill.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\ic_info_custom_fill.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_ic_baseline_add_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\ic_baseline_add_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\color_bottom_nav_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\color\\bottom_nav_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_chat_input_shape.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\chat_input_shape.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_rounded_edittext2.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\rounded_edittext2.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_checked.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\checked.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_bottom_select_customer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\bottom_select_customer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\mipmap-xxxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_labortrackingview.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_labortrackingview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_fragment_materialsetup.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\fragment_materialsetup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_tab_project.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\tab_project.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_item_line.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\item_line.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\font_inter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\font\\inter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_draws.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_draws.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\font_medium.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\font\\medium.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_send_button_shape.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\send_button_shape.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_fragment_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\fragment_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_bottom_sheet_month_filter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\bottom_sheet_month_filter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_ic_options_bold.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\ic_options_bold.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\font_sfpro.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\font\\sfpro.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_logout_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\logout_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_splash.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_splash.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_item_line_lineal.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\item_line_lineal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_materialsetup.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_materialsetup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_bottom_add_draw.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\bottom_add_draw.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_subscription_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\subscription_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_bottom_sheet_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\bottom_sheet_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_linear_line_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_linear_line_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_select_camera_shape.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\select_camera_shape.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_ic_arrow_up_right.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\ic_arrow_up_right.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_dialog_forgetpassword.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\dialog_forgetpassword.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_dialog_add_lineal.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\dialog_add_lineal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_tab_home_unselected.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\tab_home_unselected.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_add_line_items.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_add_line_items.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_fragment_material_line_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\fragment_material_line_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_edit.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\edit.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\mipmap-xxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\font_inter_18pt_bold.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\font\\inter_18pt_bold.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_profile.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_dialog_add_square.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\dialog_add_square.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_fragment_profileview.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\fragment_profileview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_profit_overhead.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\profit_overhead.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_ic_baseline_play_circle_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\ic_baseline_play_circle_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_apple.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\apple.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_ic_delete_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\ic_delete_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_ic_close.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\ic_close.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_ic_baseline_image_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\ic_baseline_image_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_dialog_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\dialog_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\font_inter_18pt_semibold.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\font\\inter_18pt_semibold.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_item_filter_option.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\item_filter_option.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_item_draw.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\item_draw.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_select_image_shape.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\select_image_shape.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_add_shape.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\add_shape.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_image_shape.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\image_shape.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_progress_bar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\progress_bar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_bottom_add_material.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\bottom_add_material.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_tab_project_unselected.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\tab_project_unselected.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_dialog_resetpassword.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\dialog_resetpassword.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_info.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\info.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_item_lineal.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\item_lineal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_ic_cart.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\ic_cart.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_dialog_add_square.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\dialog_add_square.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_item_customer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\item_customer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_projectview.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_projectview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_checkbox_checked.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\checkbox_checked.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_tab_account.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\tab_account.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_dialog_add_lineal.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\dialog_add_lineal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\font_sf_pro.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\font\\sf_pro.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_trackingview.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_trackingview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_alerts.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_alerts.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_fragment_companysetup.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\fragment_companysetup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_item_line.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\item_line.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_user_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\user_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\mipmap-mdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_fragment_squresetup.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\fragment_squresetup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_ic_baseline_video_library_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\ic_baseline_video_library_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_ic_add.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\ic_add.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_header.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_item_material.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\item_material.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_item_employee.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\item_employee.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_bottom_add_draw.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\bottom_add_draw.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_ic_baseline_send_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\ic_baseline_send_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_ic_default.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\ic_default.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_room_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_room_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\font_semibold.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\font\\semibold.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_rounded_edittext_none_editable.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\rounded_edittext_none_editable.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_bg_stat_card.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\bg_stat_card.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_bottom_sheet_rounded_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\bottom_sheet_rounded_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_dialog_alert_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\dialog_alert_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_rounded_textview.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\rounded_textview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_bottom_add_lineal.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\bottom_add_lineal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\raw_loader.json.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\raw\\loader.json"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_bottom_edit_profile.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\bottom_edit_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_companysetup.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_companysetup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_fragment_create_customer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\fragment_create_customer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_receive_message_shape.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\receive_message_shape.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_ic_edit_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\ic_edit_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_tab_team_selected.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\tab_team_selected.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_tab_account_selected.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\tab_account_selected.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_tab_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\tab_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_room_data.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\room_data.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_bottom_sheet_status_filter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\bottom_sheet_status_filter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_profile_edit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_profile_edit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_linealsetup.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_linealsetup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_user_data.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\user_data.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_fragment_sign_up.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\fragment_sign_up.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_accountview.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_accountview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_fragment_completesetup.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\fragment_completesetup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_dialog_add_material.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\dialog_add_material.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_item_material.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\item_material.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_item_line_lineal.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\item_line_lineal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_filter_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\filter_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_progress_dialog.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\progress_dialog.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_item_draw.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\item_draw.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_tab_account_unselected.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\tab_account_unselected.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_dialog_add_material.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\dialog_add_material.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_ic_baseline_close_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\ic_baseline_close_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\mipmap-hdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_simple_chat_view_widget.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\simple_chat_view_widget.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_header.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_profit.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\profit.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_fragment_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\fragment_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_receive_video_message_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\receive_video_message_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_checkbox_color_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\checkbox_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_item_lineal.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\item_lineal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_receive_image_message_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\receive_image_message_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_fragment_create_estimation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\fragment_create_estimation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_unchecked.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\unchecked.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\font_inter_18pt_medium.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\font\\inter_18pt_medium.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_bottom_sheet_status_filter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\bottom_sheet_status_filter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_ic_baseline_camera_alt_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\ic_baseline_camera_alt_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_reset_password.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_reset_password.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_tab_cost.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\tab_cost.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_squresetup.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_squresetup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_fragment_linealsetup.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\fragment_linealsetup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_bottom_sheet_month_filter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\bottom_sheet_month_filter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_item_employee.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\item_employee.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_bottom_add_lineal.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\bottom_add_lineal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_tab_project_selected.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\tab_project_selected.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_cross.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\cross.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_receive_text_message_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\receive_text_message_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_item_multi_select_filter_option.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\item_multi_select_filter_option.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_item_line_material.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\item_line_material.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_ic_profile.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\ic_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_line_items.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_line_items.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_item_dashboard_project.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\item_dashboard_project.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_icon_left.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\icon_left.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_workerview.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_workerview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_fragment_forget_password.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\fragment_forget_password.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_dialog_resetpassword.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\dialog_resetpassword.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_send_video_message_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\send_video_message_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_new_checked.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\new_checked.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\mipmap-xhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_rounded_edittext.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\rounded_edittext.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_ic_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\ic_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\navigation_mobile_navigation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\navigation\\mobile_navigation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_completesetup.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_completesetup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_checkbox_unchecked.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\checkbox_unchecked.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\menu_bottom_nav_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\menu\\bottom_nav_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_fragment_accountview.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\fragment_accountview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_bottom_add_material.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\bottom_add_material.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_bottom_edit_profile.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\bottom_edit_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_fragment_reset_password.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\fragment_reset_password.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_delete.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\delete.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_create_estimation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_create_estimation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_plus_button.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\plus_button.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_item_app_alert.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\item_app_alert.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_item_line_material.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\item_line_material.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_chevron_up.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\chevron_up.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_ic_arrow_right.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\ic_arrow_right.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_fragment_line_items.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\fragment_line_items.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_fragment_dashboardview.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\fragment_dashboardview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_fragment_linear_line_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\fragment_linear_line_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_tab_cost_selected.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\tab_cost_selected.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\font_sf_pro_italic.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\font\\sf_pro_italic.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_tab_team_unselected.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\tab_team_unselected.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\font_inter_18pt_regular.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\font\\inter_18pt_regular.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_item_line_total.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\item_line_total.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_fragment_add_line_items.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\fragment_add_line_items.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\anim_scanner_animation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\anim\\scanner_animation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_green_check_mark.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\green_check_mark.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_item_customer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\item_customer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_subscription.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_subscription.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_ic_option_light.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\ic_option_light.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_item_line_total.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\item_line_total.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_tab_home_selected.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\tab_home_selected.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_dashboardview.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_dashboardview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_bg_round_white.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\bg_round_white.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_ic_search.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\ic_search.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_setting_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\setting_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_bg_round_corners.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\bg_round_corners.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_sign_up.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_sign_up.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_dialog_forgetpassword.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\dialog_forgetpassword.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_item_broadcast_video.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\item_broadcast_video.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_custom_checkbox.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\custom_checkbox.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_fragment_draws.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\fragment_draws.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_bottom_update_profile.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\bottom_update_profile.xml"}, {"merged": "Manaknight.app-debug-107:/layout_bottom_add_draw.xml.flat", "source": "Manaknight.app-main-109:/layout/bottom_add_draw.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_bottom_update_password.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\bottom_update_password.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_bottom_update_profile.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\bottom_update_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_send_image_message_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\send_image_message_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_ic_dropdown.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\ic_dropdown.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_select_video_shape.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\select_video_shape.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_compney_health.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\compney_health.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_rounded_edittext2_none_editable.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\rounded_edittext2_none_editable.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_send_message_shape.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\send_message_shape.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_bottom_select_customer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\bottom_select_customer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_bottom_sheet_multi_select_status_filter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\bottom_sheet_multi_select_status_filter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_forget_password.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_forget_password.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_material_line_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_material_line_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout-sw600dp_fragment_subscription.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout-sw600dp\\fragment_subscription.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\xml_file_provider.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\xml\\file_provider.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\drawable_ic_loc_active.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\drawable\\ic_loc_active.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_profileview.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_profileview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_fragment_friend_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\fragment_friend_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-debug-107:\\layout_bottom_update_password.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\Manaknight.app-main-109:\\layout\\bottom_update_password.xml"}]