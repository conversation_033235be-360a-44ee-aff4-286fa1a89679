package com.manaknight.app.extensions;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u001c\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u001a.\u0010\u0000\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0001\"\b\b\u0000\u0010\u0002*\u00020\u0003*\u00020\u00042\u0012\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u0002H\u00020\u0006\u00a8\u0006\b"}, d2 = {"viewBinding", "Lcom/manaknight/app/extensions/FragmentDelegate;", "T", "Landroidx/viewbinding/ViewBinding;", "Landroidx/fragment/app/Fragment;", "viewBindingFactory", "Lkotlin/Function1;", "Landroid/view/View;", "app_debug"})
public final class FragmentDelegateKt {
    
    @org.jetbrains.annotations.NotNull()
    public static final <T extends androidx.viewbinding.ViewBinding>com.manaknight.app.extensions.FragmentDelegate<T> viewBinding(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$viewBinding, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super android.view.View, ? extends T> viewBindingFactory) {
        return null;
    }
}