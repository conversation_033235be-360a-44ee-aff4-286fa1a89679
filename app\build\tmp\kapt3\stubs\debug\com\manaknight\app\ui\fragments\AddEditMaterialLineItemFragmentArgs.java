package com.manaknight.app.ui.fragments;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0019\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\b\u0018\u0000 +2\u00020\u0001:\u0001+BS\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u0012\b\b\u0002\u0010\t\u001a\u00020\b\u0012\b\b\u0002\u0010\n\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u000b\u001a\u00020\b\u0012\b\b\u0002\u0010\f\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\rJ\t\u0010\u0018\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\bH\u00c6\u0003J\t\u0010\u001c\u001a\u00020\bH\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\bH\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0005H\u00c6\u0003JY\u0010 \u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\b2\b\b\u0002\u0010\n\u001a\u00020\u00052\b\b\u0002\u0010\u000b\u001a\u00020\b2\b\b\u0002\u0010\f\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010!\u001a\u00020\"2\b\u0010#\u001a\u0004\u0018\u00010$H\u00d6\u0003J\t\u0010%\u001a\u00020\u0005H\u00d6\u0001J\u0006\u0010&\u001a\u00020\'J\u0006\u0010(\u001a\u00020)J\t\u0010*\u001a\u00020\bH\u00d6\u0001R\u0011\u0010\n\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000eR\u0011\u0010\f\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000eR\u0011\u0010\u000b\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0011R\u0011\u0010\t\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0011R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u000eR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u000e\u00a8\u0006,"}, d2 = {"Lcom/manaknight/app/ui/fragments/AddEditMaterialLineItemFragmentArgs;", "Landroidx/navigation/NavArgs;", "materialItem", "Lcom/manaknight/app/model/remote/profitPro/MaterialRespListModel2;", "projectID", "", "lineItem", "lineDescrition", "", "lineEstimateType", "isEditable", "labourHours", "itemLineID", "(Lcom/manaknight/app/model/remote/profitPro/MaterialRespListModel2;IILjava/lang/String;Ljava/lang/String;ILjava/lang/String;I)V", "()I", "getItemLineID", "getLabourHours", "()Ljava/lang/String;", "getLineDescrition", "getLineEstimateType", "getLineItem", "getMaterialItem", "()Lcom/manaknight/app/model/remote/profitPro/MaterialRespListModel2;", "getProjectID", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "equals", "", "other", "", "hashCode", "toBundle", "Landroid/os/Bundle;", "toSavedStateHandle", "Landroidx/lifecycle/SavedStateHandle;", "toString", "Companion", "app_debug"})
public final class AddEditMaterialLineItemFragmentArgs implements androidx.navigation.NavArgs {
    @org.jetbrains.annotations.NotNull()
    private final com.manaknight.app.model.remote.profitPro.MaterialRespListModel2 materialItem = null;
    private final int projectID = 0;
    private final int lineItem = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String lineDescrition = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String lineEstimateType = null;
    private final int isEditable = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String labourHours = null;
    private final int itemLineID = 0;
    @org.jetbrains.annotations.NotNull()
    public static final com.manaknight.app.ui.fragments.AddEditMaterialLineItemFragmentArgs.Companion Companion = null;
    
    public AddEditMaterialLineItemFragmentArgs(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.MaterialRespListModel2 materialItem, int projectID, int lineItem, @org.jetbrains.annotations.NotNull()
    java.lang.String lineDescrition, @org.jetbrains.annotations.NotNull()
    java.lang.String lineEstimateType, int isEditable, @org.jetbrains.annotations.NotNull()
    java.lang.String labourHours, int itemLineID) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.manaknight.app.model.remote.profitPro.MaterialRespListModel2 getMaterialItem() {
        return null;
    }
    
    public final int getProjectID() {
        return 0;
    }
    
    public final int getLineItem() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getLineDescrition() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getLineEstimateType() {
        return null;
    }
    
    public final int isEditable() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getLabourHours() {
        return null;
    }
    
    public final int getItemLineID() {
        return 0;
    }
    
    @kotlin.Suppress(names = {"CAST_NEVER_SUCCEEDS"})
    @org.jetbrains.annotations.NotNull()
    public final android.os.Bundle toBundle() {
        return null;
    }
    
    @kotlin.Suppress(names = {"CAST_NEVER_SUCCEEDS"})
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.SavedStateHandle toSavedStateHandle() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.manaknight.app.model.remote.profitPro.MaterialRespListModel2 component1() {
        return null;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final int component3() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    public final int component6() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component7() {
        return null;
    }
    
    public final int component8() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.manaknight.app.ui.fragments.AddEditMaterialLineItemFragmentArgs copy(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.MaterialRespListModel2 materialItem, int projectID, int lineItem, @org.jetbrains.annotations.NotNull()
    java.lang.String lineDescrition, @org.jetbrains.annotations.NotNull()
    java.lang.String lineEstimateType, int isEditable, @org.jetbrains.annotations.NotNull()
    java.lang.String labourHours, int itemLineID) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @kotlin.jvm.JvmStatic()
    @kotlin.Suppress(names = {"DEPRECATION"})
    @org.jetbrains.annotations.NotNull()
    public static final com.manaknight.app.ui.fragments.AddEditMaterialLineItemFragmentArgs fromBundle(@org.jetbrains.annotations.NotNull()
    android.os.Bundle bundle) {
        return null;
    }
    
    @kotlin.jvm.JvmStatic()
    @org.jetbrains.annotations.NotNull()
    public static final com.manaknight.app.ui.fragments.AddEditMaterialLineItemFragmentArgs fromSavedStateHandle(@org.jetbrains.annotations.NotNull()
    androidx.lifecycle.SavedStateHandle savedStateHandle) {
        return null;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0007\u001a\u00020\u00042\u0006\u0010\b\u001a\u00020\tH\u0007\u00a8\u0006\n"}, d2 = {"Lcom/manaknight/app/ui/fragments/AddEditMaterialLineItemFragmentArgs$Companion;", "", "()V", "fromBundle", "Lcom/manaknight/app/ui/fragments/AddEditMaterialLineItemFragmentArgs;", "bundle", "Landroid/os/Bundle;", "fromSavedStateHandle", "savedStateHandle", "Landroidx/lifecycle/SavedStateHandle;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @kotlin.jvm.JvmStatic()
        @kotlin.Suppress(names = {"DEPRECATION"})
        @org.jetbrains.annotations.NotNull()
        public final com.manaknight.app.ui.fragments.AddEditMaterialLineItemFragmentArgs fromBundle(@org.jetbrains.annotations.NotNull()
        android.os.Bundle bundle) {
            return null;
        }
        
        @kotlin.jvm.JvmStatic()
        @org.jetbrains.annotations.NotNull()
        public final com.manaknight.app.ui.fragments.AddEditMaterialLineItemFragmentArgs fromSavedStateHandle(@org.jetbrains.annotations.NotNull()
        androidx.lifecycle.SavedStateHandle savedStateHandle) {
            return null;
        }
    }
}