// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentAlertsBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final LinearLayout llNoAlerts;

  @NonNull
  public final RecyclerView rcvNewAlerts;

  @NonNull
  public final TextView textView20;

  @NonNull
  public final TextView textView21;

  @NonNull
  public final TextView tvTagNew;

  private FragmentAlertsBinding(@NonNull ConstraintLayout rootView,
      @NonNull LinearLayout llNoAlerts, @NonNull RecyclerView rcvNewAlerts,
      @NonNull TextView textView20, @NonNull TextView textView21, @NonNull TextView tvTagNew) {
    this.rootView = rootView;
    this.llNoAlerts = llNoAlerts;
    this.rcvNewAlerts = rcvNewAlerts;
    this.textView20 = textView20;
    this.textView21 = textView21;
    this.tvTagNew = tvTagNew;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentAlertsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentAlertsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_alerts, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentAlertsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.llNoAlerts;
      LinearLayout llNoAlerts = ViewBindings.findChildViewById(rootView, id);
      if (llNoAlerts == null) {
        break missingId;
      }

      id = R.id.rcvNewAlerts;
      RecyclerView rcvNewAlerts = ViewBindings.findChildViewById(rootView, id);
      if (rcvNewAlerts == null) {
        break missingId;
      }

      id = R.id.textView20;
      TextView textView20 = ViewBindings.findChildViewById(rootView, id);
      if (textView20 == null) {
        break missingId;
      }

      id = R.id.textView21;
      TextView textView21 = ViewBindings.findChildViewById(rootView, id);
      if (textView21 == null) {
        break missingId;
      }

      id = R.id.tvTagNew;
      TextView tvTagNew = ViewBindings.findChildViewById(rootView, id);
      if (tvTagNew == null) {
        break missingId;
      }

      return new FragmentAlertsBinding((ConstraintLayout) rootView, llNoAlerts, rcvNewAlerts,
          textView20, textView21, tvTagNew);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
