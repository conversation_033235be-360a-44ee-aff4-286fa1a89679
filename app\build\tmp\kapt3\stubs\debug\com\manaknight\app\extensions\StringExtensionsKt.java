package com.manaknight.app.extensions;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000T\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a\u000e\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003\u001a\n\u0010\u0004\u001a\u00020\u0001*\u00020\u0001\u001a\n\u0010\u0005\u001a\u00020\u0006*\u00020\u0007\u001a\n\u0010\u0005\u001a\u00020\u0006*\u00020\u0001\u001a\f\u0010\b\u001a\u0004\u0018\u00010\u0003*\u00020\u0001\u001a\u001c\u0010\t\u001a\u00020\n*\u00020\u000b2\u0006\u0010\f\u001a\u00020\u00012\b\b\u0002\u0010\r\u001a\u00020\u0006\u001a\n\u0010\u000e\u001a\u00020\u0001*\u00020\u000f\u001a\u0014\u0010\u0010\u001a\u00020\u0001*\u00020\u000f2\b\b\u0002\u0010\u0011\u001a\u00020\u0012\u001a\f\u0010\u0013\u001a\u0004\u0018\u00010\u0001*\u00020\u0001\u001a\f\u0010\u0014\u001a\u0004\u0018\u00010\u0015*\u00020\u0001\u001a\u0012\u0010\u0016\u001a\u00020\u0017*\u00020\u00152\u0006\u0010\u0018\u001a\u00020\u0019\u001a\n\u0010\u001a\u001a\u00020\u0001*\u00020\u0001\u001a\n\u0010\u001b\u001a\u00020\u0006*\u00020\u0007\u001a\n\u0010\u001c\u001a\u00020\n*\u00020\u001d\u001a\n\u0010\u001e\u001a\u00020\u0001*\u00020\u0007\u001a\n\u0010\u001f\u001a\u00020\u0006*\u00020\u0007\u00a8\u0006 "}, d2 = {"getDate", "", "date", "Ljava/util/Date;", "appendHttpsIfMissing", "checkIsEmpty", "", "Landroid/widget/EditText;", "convertToLocalTime", "copyText", "", "Landroidx/fragment/app/Fragment;", "text", "toast", "currentFormat", "", "formatDecimal", "places", "", "formatNumber", "getBitmapFromUrl", "Landroid/graphics/Bitmap;", "getImageUri", "Landroid/net/Uri;", "context", "Landroid/content/Context;", "getTimeInAgo", "isEmailValid", "showSoftKeyboard", "Landroid/view/View;", "textToString", "validPassword", "app_debug"})
public final class StringExtensionsKt {
    
    public static final boolean checkIsEmpty(@org.jetbrains.annotations.NotNull()
    java.lang.String $this$checkIsEmpty) {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getDate(@org.jetbrains.annotations.NotNull()
    java.util.Date date) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String textToString(@org.jetbrains.annotations.NotNull()
    android.widget.EditText $this$textToString) {
        return null;
    }
    
    public static final boolean checkIsEmpty(@org.jetbrains.annotations.NotNull()
    android.widget.EditText $this$checkIsEmpty) {
        return false;
    }
    
    public static final boolean isEmailValid(@org.jetbrains.annotations.NotNull()
    android.widget.EditText $this$isEmailValid) {
        return false;
    }
    
    public static final boolean validPassword(@org.jetbrains.annotations.NotNull()
    android.widget.EditText $this$validPassword) {
        return false;
    }
    
    public static final void showSoftKeyboard(@org.jetbrains.annotations.NotNull()
    android.view.View $this$showSoftKeyboard) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public static final java.lang.String formatNumber(@org.jetbrains.annotations.NotNull()
    java.lang.String $this$formatNumber) {
        return null;
    }
    
    public static final void copyText(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$copyText, @org.jetbrains.annotations.NotNull()
    java.lang.String text, boolean toast) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getTimeInAgo(@org.jetbrains.annotations.NotNull()
    java.lang.String $this$getTimeInAgo) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String currentFormat(double $this$currentFormat) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String formatDecimal(double $this$formatDecimal, int places) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String appendHttpsIfMissing(@org.jetbrains.annotations.NotNull()
    java.lang.String $this$appendHttpsIfMissing) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final android.net.Uri getImageUri(@org.jetbrains.annotations.NotNull()
    android.graphics.Bitmap $this$getImageUri, @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public static final android.graphics.Bitmap getBitmapFromUrl(@org.jetbrains.annotations.NotNull()
    java.lang.String $this$getBitmapFromUrl) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public static final java.util.Date convertToLocalTime(@org.jetbrains.annotations.NotNull()
    java.lang.String $this$convertToLocalTime) {
        return null;
    }
}