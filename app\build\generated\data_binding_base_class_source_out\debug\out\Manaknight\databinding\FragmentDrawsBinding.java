// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentDrawsBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final RelativeLayout container;

  @NonNull
  public final ConstraintLayout coss;

  @NonNull
  public final HeaderBinding headerInclude;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final ConstraintLayout innerConstraintLayout;

  @NonNull
  public final LinearLayout line1;

  @NonNull
  public final RecyclerView materialRecylerView;

  @NonNull
  public final TextView txtSalePrice;

  @NonNull
  public final TextView txtUnaccounted;

  private FragmentDrawsBinding(@NonNull ConstraintLayout rootView,
      @Nullable RelativeLayout container, @NonNull ConstraintLayout coss,
      @NonNull HeaderBinding headerInclude, @Nullable ConstraintLayout innerConstraintLayout,
      @NonNull LinearLayout line1, @NonNull RecyclerView materialRecylerView,
      @NonNull TextView txtSalePrice, @NonNull TextView txtUnaccounted) {
    this.rootView = rootView;
    this.container = container;
    this.coss = coss;
    this.headerInclude = headerInclude;
    this.innerConstraintLayout = innerConstraintLayout;
    this.line1 = line1;
    this.materialRecylerView = materialRecylerView;
    this.txtSalePrice = txtSalePrice;
    this.txtUnaccounted = txtUnaccounted;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentDrawsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentDrawsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_draws, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentDrawsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.container;
      RelativeLayout container = ViewBindings.findChildViewById(rootView, id);

      ConstraintLayout coss = (ConstraintLayout) rootView;

      id = R.id.headerInclude;
      View headerInclude = ViewBindings.findChildViewById(rootView, id);
      if (headerInclude == null) {
        break missingId;
      }
      HeaderBinding binding_headerInclude = HeaderBinding.bind(headerInclude);

      id = R.id.innerConstraintLayout;
      ConstraintLayout innerConstraintLayout = ViewBindings.findChildViewById(rootView, id);

      id = R.id.line1;
      LinearLayout line1 = ViewBindings.findChildViewById(rootView, id);
      if (line1 == null) {
        break missingId;
      }

      id = R.id.materialRecylerView;
      RecyclerView materialRecylerView = ViewBindings.findChildViewById(rootView, id);
      if (materialRecylerView == null) {
        break missingId;
      }

      id = R.id.txtSalePrice;
      TextView txtSalePrice = ViewBindings.findChildViewById(rootView, id);
      if (txtSalePrice == null) {
        break missingId;
      }

      id = R.id.txtUnaccounted;
      TextView txtUnaccounted = ViewBindings.findChildViewById(rootView, id);
      if (txtUnaccounted == null) {
        break missingId;
      }

      return new FragmentDrawsBinding((ConstraintLayout) rootView, container, coss,
          binding_headerInclude, innerConstraintLayout, line1, materialRecylerView, txtSalePrice,
          txtUnaccounted);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
