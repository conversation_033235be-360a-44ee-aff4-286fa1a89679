package com.manaknight.app.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\b\u0002\u0018\u0000 \u00172\u00020\u0001:\u0001\u0017B9\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0012\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\f0\u000b\u00a2\u0006\u0002\u0010\rJ\u0018\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\tH\u0002J\u0010\u0010\u0013\u001a\u00020\f2\u0006\u0010\u0010\u001a\u00020\u0011H\u0002J\u0014\u0010\u0014\u001a\u00020\f2\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00110\u0016R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\f0\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0018"}, d2 = {"Lcom/manaknight/app/utils/DynamicLineItemManager;", "", "context", "Landroid/content/Context;", "container", "Landroid/widget/LinearLayout;", "navController", "Landroidx/navigation/NavController;", "projectID", "", "onDeleteItem", "Lkotlin/Function1;", "", "(Landroid/content/Context;Landroid/widget/LinearLayout;Landroidx/navigation/NavController;ILkotlin/jvm/functions/Function1;)V", "createLineItemView", "Landroid/view/View;", "lineItem", "Lcom/manaknight/app/model/remote/profitPro/JobDetailsRespModel;", "position", "navigateToEditScreen", "updateLineItems", "lineItems", "", "Companion", "app_debug"})
public final class DynamicLineItemManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final android.widget.LinearLayout container = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.navigation.NavController navController = null;
    private final int projectID = 0;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<java.lang.Integer, kotlin.Unit> onDeleteItem = null;
    private static final int BASE_LINE_ITEM_ID = 1000;
    private static final int BASE_DESCRIPTION_ID = 2000;
    private static final int BASE_SALE_PRICE_ID = 3000;
    private static final int BASE_PROFIT_OVERHEAD_ID = 4000;
    private static final int BASE_LABOR_BUDGET_ID = 5000;
    private static final int BASE_MATERIAL_BUDGET_ID = 6000;
    private static final int BASE_TYPE_LABEL_ID = 7000;
    private static final int BASE_DELETE_BTN_ID = 8000;
    private static final int BASE_EDIT_BTN_ID = 9000;
    @org.jetbrains.annotations.NotNull()
    public static final com.manaknight.app.utils.DynamicLineItemManager.Companion Companion = null;
    
    public DynamicLineItemManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    android.widget.LinearLayout container, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, int projectID, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onDeleteItem) {
        super();
    }
    
    public final void updateLineItems(@org.jetbrains.annotations.NotNull()
    java.util.List<com.manaknight.app.model.remote.profitPro.JobDetailsRespModel> lineItems) {
    }
    
    private final android.view.View createLineItemView(com.manaknight.app.model.remote.profitPro.JobDetailsRespModel lineItem, int position) {
        return null;
    }
    
    private final void navigateToEditScreen(com.manaknight.app.model.remote.profitPro.JobDetailsRespModel lineItem) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\t\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/manaknight/app/utils/DynamicLineItemManager$Companion;", "", "()V", "BASE_DELETE_BTN_ID", "", "BASE_DESCRIPTION_ID", "BASE_EDIT_BTN_ID", "BASE_LABOR_BUDGET_ID", "BASE_LINE_ITEM_ID", "BASE_MATERIAL_BUDGET_ID", "BASE_PROFIT_OVERHEAD_ID", "BASE_SALE_PRICE_ID", "BASE_TYPE_LABEL_ID", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}