package com.manaknight.app.fcm;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\fH\u0002J\u0010\u0010\u000e\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000fH\u0017J\u0010\u0010\u0010\u001a\u00020\n2\u0006\u0010\u0011\u001a\u00020\fH\u0016R\u001b\u0010\u0003\u001a\u00020\u00048BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0007\u0010\b\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0012"}, d2 = {"Lcom/manaknight/app/fcm/MyFirebasePushNotifications;", "Lcom/google/firebase/messaging/FirebaseMessagingService;", "()V", "pref", "Lcom/manaknight/app/data/local/AppPreferences;", "getPref", "()Lcom/manaknight/app/data/local/AppPreferences;", "pref$delegate", "Lkotlin/Lazy;", "generatePushNotifications", "", "title", "", "message", "onMessageReceived", "Lcom/google/firebase/messaging/RemoteMessage;", "onNewToken", "token", "app_debug"})
public final class MyFirebasePushNotifications extends com.google.firebase.messaging.FirebaseMessagingService {
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy pref$delegate = null;
    
    public MyFirebasePushNotifications() {
        super();
    }
    
    private final com.manaknight.app.data.local.AppPreferences getPref() {
        return null;
    }
    
    @java.lang.Override()
    @android.annotation.SuppressLint(value = {"NewApi"})
    public void onMessageReceived(@org.jetbrains.annotations.NotNull()
    com.google.firebase.messaging.RemoteMessage message) {
    }
    
    private final void generatePushNotifications(java.lang.String title, java.lang.String message) {
    }
    
    @java.lang.Override()
    public void onNewToken(@org.jetbrains.annotations.NotNull()
    java.lang.String token) {
    }
}