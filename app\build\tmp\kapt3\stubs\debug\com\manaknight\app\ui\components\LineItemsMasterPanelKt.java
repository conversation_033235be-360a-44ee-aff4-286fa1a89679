package com.manaknight.app.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001aT\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\u000b2\u0012\u0010\f\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\r2\b\b\u0002\u0010\u000e\u001a\u00020\u000fH\u0007\u00a8\u0006\u0010"}, d2 = {"LineItemsMasterPanel", "", "projectID", "", "customerName", "", "baasViewModel", "Lcom/manaknight/app/viewmodels/BaasViewModel;", "navController", "Landroidx/navigation/NavController;", "onAddLineItem", "Lkotlin/Function0;", "onDeleteItem", "Lkotlin/Function1;", "modifier", "Landroidx/compose/ui/Modifier;", "app_debug"})
public final class LineItemsMasterPanelKt {
    
    /**
     * Master panel component that displays the list of line items
     * Uses the exact same implementation as LineItemsScreen for consistency
     *
     * @param projectID The project ID to fetch line items for
     * @param customerName The customer name to display in the header
     * @param baasViewModel The view model for data operations
     * @param navController Navigation controller for handling navigation
     * @param onAddLineItem Callback when add line item button is clicked
     * @param onDeleteItem Callback when a line item is deleted
     * @param modifier Modifier to be applied to the panel
     */
    @androidx.compose.runtime.Composable()
    public static final void LineItemsMasterPanel(int projectID, @org.jetbrains.annotations.NotNull()
    java.lang.String customerName, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.viewmodels.BaasViewModel baasViewModel, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onAddLineItem, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onDeleteItem, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
}