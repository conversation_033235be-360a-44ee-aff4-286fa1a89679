package com.manaknight.app.extensions;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000Z\n\u0000\n\u0002\u0010\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\r\u001a\u0012\u0010\u0000\u001a\u00020\u0001*\u00020\u00022\u0006\u0010\u0003\u001a\u00020\u0004\u001a\n\u0010\u0005\u001a\u00020\u0001*\u00020\u0006\u001a\u001a\u0010\u0007\u001a\u00020\u0001*\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\u0004\u001a\u0014\u0010\f\u001a\u00020\u0001*\u00020\u00022\b\b\u0002\u0010\r\u001a\u00020\u0004\u001a\u0014\u0010\u000e\u001a\u00020\u0001*\u00020\u00022\b\b\u0002\u0010\r\u001a\u00020\u0004\u001a*\u0010\u000f\u001a\u00020\u0004*\u00020\u00102\b\b\u0001\u0010\u0011\u001a\u00020\u00042\b\b\u0002\u0010\u0012\u001a\u00020\u00132\b\b\u0002\u0010\u0014\u001a\u00020\u0015H\u0007\u001a\n\u0010\u0016\u001a\u00020\u0001*\u00020\u0002\u001a\n\u0010\u0017\u001a\u00020\u0001*\u00020\u0010\u001a\n\u0010\u0018\u001a\u00020\u0001*\u00020\u0019\u001a\n\u0010\u001a\u001a\u00020\u0001*\u00020\u0002\u001a\n\u0010\u001b\u001a\u00020\u0015*\u00020\u0010\u001a\u001e\u0010\u001c\u001a\u00020\u0001*\u00020\b2\b\u0010\u001d\u001a\u0004\u0018\u00010\u001e2\b\b\u0002\u0010\u000b\u001a\u00020\u0004\u001a\u0014\u0010\u001f\u001a\u00020\u0001*\u00020 2\b\b\u0002\u0010!\u001a\u00020\u0004\u001a\u0014\u0010\"\u001a\u00020\u0001*\u00020 2\b\b\u0002\u0010#\u001a\u00020\u0015\u001a\u001e\u0010$\u001a\u00020\u0001*\u00020\u00022\u0012\u0010%\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00010&\u001a\u0014\u0010\'\u001a\u00020\u0001*\u00020 2\b\b\u0002\u0010#\u001a\u00020\u0015\u001a\u0012\u0010(\u001a\u00020\u0001*\u00020\u00102\u0006\u0010)\u001a\u00020\u001e\u001a\n\u0010*\u001a\u00020\u0001*\u00020\u0002\u001a\n\u0010+\u001a\u00020\u0001*\u00020\u0002\u001a\n\u0010,\u001a\u00020\u0001*\u00020\u0002\u001a\u001e\u0010-\u001a\u00020\u0001*\u00020\u00192\u0006\u0010.\u001a\u00020\u001e2\b\b\u0002\u0010/\u001a\u00020\u0004H\u0007\u001a\u001c\u0010-\u001a\u00020\u0001*\u00020\u00022\u0006\u0010.\u001a\u00020\u001e2\b\b\u0002\u0010/\u001a\u00020\u0004\u001a\u0012\u0010-\u001a\u00020\u0001*\u00020\u00102\u0006\u0010.\u001a\u00020\u001e\u001a\u001c\u00100\u001a\u00020\u0001*\u00020\u00192\u0006\u0010.\u001a\u00020\u001e2\b\b\u0002\u0010/\u001a\u00020\u0004\u001a\u0012\u00100\u001a\u00020\u0001*\u00020\u00102\u0006\u0010.\u001a\u00020\u001e\u001a\u001c\u00101\u001a\u00020\u0001*\u00020\u00102\u0006\u00102\u001a\u00020\u001e2\b\b\u0002\u0010\r\u001a\u00020\u0004\u00a8\u00063"}, d2 = {"changeTint", "", "Landroid/view/View;", "color", "", "disableSpaces", "Landroid/widget/EditText;", "displayBlankImage", "Landroid/widget/ImageView;", "aContext", "Landroid/content/Context;", "aPlaceHolderImage", "fadeIn", "duration", "fadeOut", "getColorFromAttr", "Landroidx/fragment/app/Fragment;", "attrColor", "typedValue", "Landroid/util/TypedValue;", "resolveRefs", "", "hide", "hideKeyboard", "hideSoftKeyboard", "Landroid/app/Activity;", "invisible", "isTabletLayout", "loadImageFromUrl", "aImageUrl", "", "setGridLayout", "Landroidx/recyclerview/widget/RecyclerView;", "span", "setHorizontalLayout", "aReverseLayout", "setOnClickWithDebounce", "action", "Lkotlin/Function1;", "setVerticalLayout", "shareText", "text", "show", "slideDown", "slideUp", "snackBar", "msg", "length", "snackBarForDialog", "toast", "stringRes", "app_debug"})
public final class ViewKt {
    
    public static final void slideDown(@org.jetbrains.annotations.NotNull()
    android.view.View $this$slideDown) {
    }
    
    public static final void slideUp(@org.jetbrains.annotations.NotNull()
    android.view.View $this$slideUp) {
    }
    
    public static final void fadeIn(@org.jetbrains.annotations.NotNull()
    android.view.View $this$fadeIn, int duration) {
    }
    
    public static final void fadeOut(@org.jetbrains.annotations.NotNull()
    android.view.View $this$fadeOut, int duration) {
    }
    
    public static final void changeTint(@org.jetbrains.annotations.NotNull()
    android.view.View $this$changeTint, int color) {
    }
    
    public static final void show(@org.jetbrains.annotations.NotNull()
    android.view.View $this$show) {
    }
    
    public static final void hide(@org.jetbrains.annotations.NotNull()
    android.view.View $this$hide) {
    }
    
    public static final void invisible(@org.jetbrains.annotations.NotNull()
    android.view.View $this$invisible) {
    }
    
    public static final void loadImageFromUrl(@org.jetbrains.annotations.NotNull()
    android.widget.ImageView $this$loadImageFromUrl, @org.jetbrains.annotations.Nullable()
    java.lang.String aImageUrl, int aPlaceHolderImage) {
    }
    
    public static final void displayBlankImage(@org.jetbrains.annotations.NotNull()
    android.widget.ImageView $this$displayBlankImage, @org.jetbrains.annotations.NotNull()
    android.content.Context aContext, int aPlaceHolderImage) {
    }
    
    @android.annotation.SuppressLint(value = {"NewApi"})
    public static final void snackBar(@org.jetbrains.annotations.NotNull()
    android.app.Activity $this$snackBar, @org.jetbrains.annotations.NotNull()
    java.lang.String msg, int length) {
    }
    
    public static final void snackBarForDialog(@org.jetbrains.annotations.NotNull()
    android.app.Activity $this$snackBarForDialog, @org.jetbrains.annotations.NotNull()
    java.lang.String msg, int length) {
    }
    
    public static final void snackBar(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$snackBar, @org.jetbrains.annotations.NotNull()
    java.lang.String msg) {
    }
    
    public static final void snackBarForDialog(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$snackBarForDialog, @org.jetbrains.annotations.NotNull()
    java.lang.String msg) {
    }
    
    public static final void snackBar(@org.jetbrains.annotations.NotNull()
    android.view.View $this$snackBar, @org.jetbrains.annotations.NotNull()
    java.lang.String msg, int length) {
    }
    
    public static final void setGridLayout(@org.jetbrains.annotations.NotNull()
    androidx.recyclerview.widget.RecyclerView $this$setGridLayout, int span) {
    }
    
    public static final void setVerticalLayout(@org.jetbrains.annotations.NotNull()
    androidx.recyclerview.widget.RecyclerView $this$setVerticalLayout, boolean aReverseLayout) {
    }
    
    public static final void setHorizontalLayout(@org.jetbrains.annotations.NotNull()
    androidx.recyclerview.widget.RecyclerView $this$setHorizontalLayout, boolean aReverseLayout) {
    }
    
    public static final void hideSoftKeyboard(@org.jetbrains.annotations.NotNull()
    android.app.Activity $this$hideSoftKeyboard) {
    }
    
    public static final void shareText(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$shareText, @org.jetbrains.annotations.NotNull()
    java.lang.String text) {
    }
    
    public static final void hideKeyboard(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$hideKeyboard) {
    }
    
    @androidx.annotation.ColorInt()
    public static final int getColorFromAttr(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$getColorFromAttr, @androidx.annotation.AttrRes()
    int attrColor, @org.jetbrains.annotations.NotNull()
    android.util.TypedValue typedValue, boolean resolveRefs) {
        return 0;
    }
    
    /**
     * Determines if the current screen size should use tablet layout (≥600dp width)
     * View-based version of the Compose isTabletLayout() function
     */
    public static final boolean isTabletLayout(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$isTabletLayout) {
        return false;
    }
    
    public static final void toast(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$toast, @org.jetbrains.annotations.NotNull()
    java.lang.String stringRes, int duration) {
    }
    
    public static final void setOnClickWithDebounce(@org.jetbrains.annotations.NotNull()
    android.view.View $this$setOnClickWithDebounce, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super android.view.View, kotlin.Unit> action) {
    }
    
    public static final void disableSpaces(@org.jetbrains.annotations.NotNull()
    android.widget.EditText $this$disableSpaces) {
    }
}