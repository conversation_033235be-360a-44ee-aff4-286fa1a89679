// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class BottomAddEmployeeBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView addHeading;

  @NonNull
  public final ImageView backButton;

  @NonNull
  public final MaterialButton btnSave;

  @NonNull
  public final EditText edTxtFullName;

  @NonNull
  public final EditText edTxtHourlyRate;

  @NonNull
  public final LinearLayout header;

  @NonNull
  public final LinearLayout line1;

  @NonNull
  public final LinearLayout linearDefaultHourlyRate2;

  @NonNull
  public final ScrollView mainLayout;

  @NonNull
  public final TextView tvFullName;

  private BottomAddEmployeeBinding(@NonNull LinearLayout rootView, @NonNull TextView addHeading,
      @NonNull ImageView backButton, @NonNull MaterialButton btnSave,
      @NonNull EditText edTxtFullName, @NonNull EditText edTxtHourlyRate,
      @NonNull LinearLayout header, @NonNull LinearLayout line1,
      @NonNull LinearLayout linearDefaultHourlyRate2, @NonNull ScrollView mainLayout,
      @NonNull TextView tvFullName) {
    this.rootView = rootView;
    this.addHeading = addHeading;
    this.backButton = backButton;
    this.btnSave = btnSave;
    this.edTxtFullName = edTxtFullName;
    this.edTxtHourlyRate = edTxtHourlyRate;
    this.header = header;
    this.line1 = line1;
    this.linearDefaultHourlyRate2 = linearDefaultHourlyRate2;
    this.mainLayout = mainLayout;
    this.tvFullName = tvFullName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static BottomAddEmployeeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static BottomAddEmployeeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.bottom_add_employee, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static BottomAddEmployeeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addHeading;
      TextView addHeading = ViewBindings.findChildViewById(rootView, id);
      if (addHeading == null) {
        break missingId;
      }

      id = R.id.backButton;
      ImageView backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.btnSave;
      MaterialButton btnSave = ViewBindings.findChildViewById(rootView, id);
      if (btnSave == null) {
        break missingId;
      }

      id = R.id.edTxtFullName;
      EditText edTxtFullName = ViewBindings.findChildViewById(rootView, id);
      if (edTxtFullName == null) {
        break missingId;
      }

      id = R.id.edTxtHourlyRate;
      EditText edTxtHourlyRate = ViewBindings.findChildViewById(rootView, id);
      if (edTxtHourlyRate == null) {
        break missingId;
      }

      id = R.id.header;
      LinearLayout header = ViewBindings.findChildViewById(rootView, id);
      if (header == null) {
        break missingId;
      }

      id = R.id.line1;
      LinearLayout line1 = ViewBindings.findChildViewById(rootView, id);
      if (line1 == null) {
        break missingId;
      }

      id = R.id.linearDefaultHourlyRate2;
      LinearLayout linearDefaultHourlyRate2 = ViewBindings.findChildViewById(rootView, id);
      if (linearDefaultHourlyRate2 == null) {
        break missingId;
      }

      id = R.id.mainLayout;
      ScrollView mainLayout = ViewBindings.findChildViewById(rootView, id);
      if (mainLayout == null) {
        break missingId;
      }

      id = R.id.tvFullName;
      TextView tvFullName = ViewBindings.findChildViewById(rootView, id);
      if (tvFullName == null) {
        break missingId;
      }

      return new BottomAddEmployeeBinding((LinearLayout) rootView, addHeading, backButton, btnSave,
          edTxtFullName, edTxtHourlyRate, header, line1, linearDefaultHourlyRate2, mainLayout,
          tvFullName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
