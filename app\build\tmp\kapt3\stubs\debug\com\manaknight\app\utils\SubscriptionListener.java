package com.manaknight.app.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0006\bf\u0018\u00002\u00020\u0001J\b\u0010\u0002\u001a\u00020\u0003H&J\u0010\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0005\u001a\u00020\u0006H&J\b\u0010\u0007\u001a\u00020\u0003H&J\b\u0010\b\u001a\u00020\u0003H&J \u0010\b\u001a\u00020\u00032\u0006\u0010\t\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\u0006H&\u00a8\u0006\f"}, d2 = {"Lcom/manaknight/app/utils/SubscriptionListener;", "", "onPurchaseCanceled", "", "onPurchaseFailed", "errorMessage", "", "onPurchasePending", "onPurchaseSuccess", "purchaseToken", "productId", "amount", "app_debug"})
public abstract interface SubscriptionListener {
    
    public abstract void onPurchaseSuccess();
    
    public abstract void onPurchasePending();
    
    public abstract void onPurchaseFailed(@org.jetbrains.annotations.NotNull()
    java.lang.String errorMessage);
    
    public abstract void onPurchaseCanceled();
    
    public abstract void onPurchaseSuccess(@org.jetbrains.annotations.NotNull()
    java.lang.String purchaseToken, @org.jetbrains.annotations.NotNull()
    java.lang.String productId, @org.jetbrains.annotations.NotNull()
    java.lang.String amount);
}