// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemLineTotalBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnContinue;

  @NonNull
  public final MaterialButton btnSave;

  @NonNull
  public final TextView txtLaboutBudget;

  @NonNull
  public final TextView txtLaboutBudget1;

  @NonNull
  public final TextView txtMaterialBudget;

  @NonNull
  public final TextView txtMaterialBudget1;

  @NonNull
  public final TextView txtProfitOverhead;

  @NonNull
  public final TextView txtProfitOverhead1;

  @NonNull
  public final TextView txtSalePrice;

  @NonNull
  public final TextView txtSalePrice1;

  private ItemLineTotalBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnContinue, @NonNull MaterialButton btnSave,
      @NonNull TextView txtLaboutBudget, @NonNull TextView txtLaboutBudget1,
      @NonNull TextView txtMaterialBudget, @NonNull TextView txtMaterialBudget1,
      @NonNull TextView txtProfitOverhead, @NonNull TextView txtProfitOverhead1,
      @NonNull TextView txtSalePrice, @NonNull TextView txtSalePrice1) {
    this.rootView = rootView;
    this.btnContinue = btnContinue;
    this.btnSave = btnSave;
    this.txtLaboutBudget = txtLaboutBudget;
    this.txtLaboutBudget1 = txtLaboutBudget1;
    this.txtMaterialBudget = txtMaterialBudget;
    this.txtMaterialBudget1 = txtMaterialBudget1;
    this.txtProfitOverhead = txtProfitOverhead;
    this.txtProfitOverhead1 = txtProfitOverhead1;
    this.txtSalePrice = txtSalePrice;
    this.txtSalePrice1 = txtSalePrice1;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemLineTotalBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemLineTotalBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_line_total, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemLineTotalBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnContinue;
      MaterialButton btnContinue = ViewBindings.findChildViewById(rootView, id);
      if (btnContinue == null) {
        break missingId;
      }

      id = R.id.btnSave;
      MaterialButton btnSave = ViewBindings.findChildViewById(rootView, id);
      if (btnSave == null) {
        break missingId;
      }

      id = R.id.txtLaboutBudget;
      TextView txtLaboutBudget = ViewBindings.findChildViewById(rootView, id);
      if (txtLaboutBudget == null) {
        break missingId;
      }

      id = R.id.txtLaboutBudget_1;
      TextView txtLaboutBudget1 = ViewBindings.findChildViewById(rootView, id);
      if (txtLaboutBudget1 == null) {
        break missingId;
      }

      id = R.id.txtMaterialBudget;
      TextView txtMaterialBudget = ViewBindings.findChildViewById(rootView, id);
      if (txtMaterialBudget == null) {
        break missingId;
      }

      id = R.id.txtMaterialBudget_1;
      TextView txtMaterialBudget1 = ViewBindings.findChildViewById(rootView, id);
      if (txtMaterialBudget1 == null) {
        break missingId;
      }

      id = R.id.txtProfitOverhead;
      TextView txtProfitOverhead = ViewBindings.findChildViewById(rootView, id);
      if (txtProfitOverhead == null) {
        break missingId;
      }

      id = R.id.txtProfitOverhead_1;
      TextView txtProfitOverhead1 = ViewBindings.findChildViewById(rootView, id);
      if (txtProfitOverhead1 == null) {
        break missingId;
      }

      id = R.id.txtSalePrice;
      TextView txtSalePrice = ViewBindings.findChildViewById(rootView, id);
      if (txtSalePrice == null) {
        break missingId;
      }

      id = R.id.txtSalePrice_1;
      TextView txtSalePrice1 = ViewBindings.findChildViewById(rootView, id);
      if (txtSalePrice1 == null) {
        break missingId;
      }

      return new ItemLineTotalBinding((ConstraintLayout) rootView, btnContinue, btnSave,
          txtLaboutBudget, txtLaboutBudget1, txtMaterialBudget, txtMaterialBudget1,
          txtProfitOverhead, txtProfitOverhead1, txtSalePrice, txtSalePrice1);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
