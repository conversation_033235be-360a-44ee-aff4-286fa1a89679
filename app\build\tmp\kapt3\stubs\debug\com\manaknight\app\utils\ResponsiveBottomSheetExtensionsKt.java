package com.manaknight.app.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000^\n\u0000\n\u0002\u0010\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\"\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\u001a8\u0010\u0000\u001a\u00020\u0001*\u00020\u00022\u0006\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\nH\u0002\u001aD\u0010\u000b\u001a\u00020\u0001*\u00020\u00022\u0006\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\b0\r2\u0018\u0010\u000e\u001a\u0014\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\r\u0012\u0004\u0012\u00020\u00010\nH\u0002\u001a:\u0010\u000f\u001a\u00020\u0006*\u00020\u00022\u0006\u0010\u0007\u001a\u00020\b2\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00010\n2\u0010\b\u0002\u0010\u0010\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0011H\u0007\u001al\u0010\u0012\u001a\u00020\u0006\"\b\b\u0000\u0010\u0013*\u00020\u0014*\u00020\u00022\"\b\b\u0010\u0015\u001a\u001c\u0012\u0004\u0012\u00020\u0017\u0012\u0006\u0012\u0004\u0018\u00010\u0018\u0012\u0004\u0012\u00020\u0019\u0012\u0004\u0012\u0002H\u00130\u00162\u0010\b\n\u0010\u0010\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00112\u001a\b\u0004\u0010\u001a\u001a\u0014\u0012\u0004\u0012\u0002H\u0013\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u001bH\u0087\b\u00f8\u0001\u0000\u001aF\u0010\u001c\u001a\u00020\u0006*\u00020\u00022\u0006\u0010\u001d\u001a\u00020\u001e2\u0010\b\n\u0010\u0010\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00112\u001a\b\u0004\u0010\u001f\u001a\u0014\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u001bH\u0087\b\u00f8\u0001\u0000\u001aF\u0010 \u001a\u00020\u0006*\u00020\u00022\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\b0\r2\u0018\u0010\u000e\u001a\u0014\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\r\u0012\u0004\u0012\u00020\u00010\n2\u0010\b\u0002\u0010\u0010\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0011H\u0007\u0082\u0002\u0007\n\u0005\b\u009920\u0001\u00a8\u0006!"}, d2 = {"setupMonthFilterSheet", "", "Landroidx/fragment/app/Fragment;", "view", "Landroid/view/View;", "dialog", "Landroid/app/Dialog;", "selectedMonth", "", "onMonthSelected", "Lkotlin/Function1;", "setupStatusFilterSheet", "selectedStatuses", "", "onStatusesChanged", "showResponsiveMonthFilterSheet", "onDismiss", "Lkotlin/Function0;", "showResponsiveSheet", "T", "Landroidx/viewbinding/ViewBinding;", "bindingInflater", "Lkotlin/Function3;", "Landroid/view/LayoutInflater;", "Landroid/view/ViewGroup;", "", "onBindingReady", "Lkotlin/Function2;", "showResponsiveSheetWithLayout", "layoutRes", "", "onViewReady", "showResponsiveStatusFilterSheet", "app_debug"})
public final class ResponsiveBottomSheetExtensionsKt {
    
    /**
     * Shows a responsive sheet using ViewBinding
     * Usage: showResponsiveSheet(BottomAddEmployeeBinding::inflate) { binding, dialog ->
     *    // Setup your views here
     * }
     */
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public static final <T extends androidx.viewbinding.ViewBinding>android.app.Dialog showResponsiveSheet(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$showResponsiveSheet, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super android.view.LayoutInflater, ? super android.view.ViewGroup, ? super java.lang.Boolean, ? extends T> bindingInflater, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super T, ? super android.app.Dialog, kotlin.Unit> onBindingReady) {
        return null;
    }
    
    /**
     * Shows a responsive sheet using layout resource (for backward compatibility)
     * Usage: showResponsiveSheetWithLayout(R.layout.bottom_add_employee) { view, dialog ->
     *    // Setup your views here
     * }
     */
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public static final android.app.Dialog showResponsiveSheetWithLayout(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$showResponsiveSheetWithLayout, int layoutRes, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super android.view.View, ? super android.app.Dialog, kotlin.Unit> onViewReady) {
        return null;
    }
    
    /**
     * Shows a responsive status filter sheet
     */
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public static final android.app.Dialog showResponsiveStatusFilterSheet(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$showResponsiveStatusFilterSheet, @org.jetbrains.annotations.NotNull()
    java.util.Set<java.lang.String> selectedStatuses, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.util.Set<java.lang.String>, kotlin.Unit> onStatusesChanged, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
        return null;
    }
    
    /**
     * Shows a responsive month filter sheet
     */
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public static final android.app.Dialog showResponsiveMonthFilterSheet(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$showResponsiveMonthFilterSheet, @org.jetbrains.annotations.NotNull()
    java.lang.String selectedMonth, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onMonthSelected, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
        return null;
    }
    
    /**
     * Helper function to setup status filter sheet
     */
    private static final void setupStatusFilterSheet(androidx.fragment.app.Fragment $this$setupStatusFilterSheet, android.view.View view, android.app.Dialog dialog, java.util.Set<java.lang.String> selectedStatuses, kotlin.jvm.functions.Function1<? super java.util.Set<java.lang.String>, kotlin.Unit> onStatusesChanged) {
    }
    
    /**
     * Helper function to setup month filter sheet
     */
    private static final void setupMonthFilterSheet(androidx.fragment.app.Fragment $this$setupMonthFilterSheet, android.view.View view, android.app.Dialog dialog, java.lang.String selectedMonth, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onMonthSelected) {
    }
}