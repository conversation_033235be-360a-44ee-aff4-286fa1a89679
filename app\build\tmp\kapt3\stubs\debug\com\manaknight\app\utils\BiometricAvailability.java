package com.manaknight.app.utils;

/**
 * Enum for biometric availability status
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\t\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\t\u00a8\u0006\n"}, d2 = {"Lcom/manaknight/app/utils/BiometricAvailability;", "", "(Ljava/lang/String;I)V", "AVAILABLE", "NO_HARDWARE", "HARDWARE_UNAVAILABLE", "NONE_ENROLLED", "SECURITY_UPDATE_REQUIRED", "UNSUPPORTED", "UNKNOWN", "app_debug"})
public enum BiometricAvailability {
    /*public static final*/ AVAILABLE /* = new AVAILABLE() */,
    /*public static final*/ NO_HARDWARE /* = new NO_HARDWARE() */,
    /*public static final*/ HARDWARE_UNAVAILABLE /* = new HARDWARE_UNAVAILABLE() */,
    /*public static final*/ NONE_ENROLLED /* = new NONE_ENROLLED() */,
    /*public static final*/ SECURITY_UPDATE_REQUIRED /* = new SECURITY_UPDATE_REQUIRED() */,
    /*public static final*/ UNSUPPORTED /* = new UNSUPPORTED() */,
    /*public static final*/ UNKNOWN /* = new UNKNOWN() */;
    
    BiometricAvailability() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.manaknight.app.utils.BiometricAvailability> getEntries() {
        return null;
    }
}