package com.manaknight.app.adapter;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0011\n\u0002\u0018\u0002\n\u0002\b\u0004\u0018\u00002\f\u0012\b\u0012\u00060\u0002R\u00020\u00000\u0001:\u0001\"B\'\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\u0018\u0010\u0005\u001a\u0014\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\t0\u0006\u00a2\u0006\u0002\u0010\nJ\b\u0010\u0019\u001a\u00020\bH\u0016J\u001c\u0010\u001a\u001a\u00020\t2\n\u0010\u001b\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u001c\u001a\u00020\bH\u0016J\u001c\u0010\u001d\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u0010\u0003\u001a\u00020\bH\u0016J\u001e\u0010 \u001a\u00020\t2\u0016\u0010!\u001a\u0012\u0012\u0004\u0012\u00020\u00070\fj\b\u0012\u0004\u0012\u00020\u0007`\rR!\u0010\u000b\u001a\u0012\u0012\u0004\u0012\u00020\u00070\fj\b\u0012\u0004\u0012\u00020\u0007`\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR#\u0010\u0005\u001a\u0014\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\t0\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u001a\u0010\u0012\u001a\u00020\bX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0013\u0010\u0014\"\u0004\b\u0015\u0010\u0016R\u0011\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018\u00a8\u0006#"}, d2 = {"Lcom/manaknight/app/adapter/LinearLineAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/manaknight/app/adapter/LinearLineAdapter$ViewHolder;", "viewType", "", "onCheckBoxClick", "Lkotlin/Function2;", "Lcom/manaknight/app/model/remote/profitPro/LinearRespListModel;", "", "", "(Ljava/lang/String;Lkotlin/jvm/functions/Function2;)V", "list", "Ljava/util/ArrayList;", "Lkotlin/collections/ArrayList;", "getList", "()Ljava/util/ArrayList;", "getOnCheckBoxClick", "()Lkotlin/jvm/functions/Function2;", "selectedLinerID", "getSelectedLinerID", "()I", "setSelectedLinerID", "(I)V", "getViewType", "()Ljava/lang/String;", "getItemCount", "onBindViewHolder", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "refresh", "newList", "ViewHolder", "app_debug"})
public final class LinearLineAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.manaknight.app.adapter.LinearLineAdapter.ViewHolder> {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String viewType = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function2<com.manaknight.app.model.remote.profitPro.LinearRespListModel, java.lang.Integer, kotlin.Unit> onCheckBoxClick = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<com.manaknight.app.model.remote.profitPro.LinearRespListModel> list = null;
    private int selectedLinerID = -1;
    
    public LinearLineAdapter(@org.jetbrains.annotations.NotNull()
    java.lang.String viewType, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super com.manaknight.app.model.remote.profitPro.LinearRespListModel, ? super java.lang.Integer, kotlin.Unit> onCheckBoxClick) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getViewType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlin.jvm.functions.Function2<com.manaknight.app.model.remote.profitPro.LinearRespListModel, java.lang.Integer, kotlin.Unit> getOnCheckBoxClick() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<com.manaknight.app.model.remote.profitPro.LinearRespListModel> getList() {
        return null;
    }
    
    public final int getSelectedLinerID() {
        return 0;
    }
    
    public final void setSelectedLinerID(int p0) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.manaknight.app.adapter.LinearLineAdapter.ViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.adapter.LinearLineAdapter.ViewHolder holder, int position) {
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    public final void refresh(@org.jetbrains.annotations.NotNull()
    java.util.ArrayList<com.manaknight.app.model.remote.profitPro.LinearRespListModel> newList) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/manaknight/app/adapter/LinearLineAdapter$ViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "binding", "LManaknight/databinding/ItemLineLinealBinding;", "(Lcom/manaknight/app/adapter/LinearLineAdapter;LManaknight/databinding/ItemLineLinealBinding;)V", "getBinding", "()LManaknight/databinding/ItemLineLinealBinding;", "app_debug"})
    public final class ViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final Manaknight.databinding.ItemLineLinealBinding binding = null;
        
        public ViewHolder(@org.jetbrains.annotations.NotNull()
        Manaknight.databinding.ItemLineLinealBinding binding) {
            super(null);
        }
        
        @org.jetbrains.annotations.NotNull()
        public final Manaknight.databinding.ItemLineLinealBinding getBinding() {
            return null;
        }
    }
}