package com.manaknight.app.ui;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000r\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0017\u0010\u001d\u001a\u00020\u001e2\b\u0010\u001f\u001a\u0004\u0018\u00010 H\u0002\u00a2\u0006\u0002\u0010!J\u0017\u0010\"\u001a\u00020\u001e2\b\u0010#\u001a\u0004\u0018\u00010 H\u0002\u00a2\u0006\u0002\u0010!J\b\u0010$\u001a\u00020%H\u0002J\u0018\u0010&\u001a\u00020%2\u0006\u0010\'\u001a\u00020(2\u0006\u0010)\u001a\u00020*H\u0016J\b\u0010+\u001a\u00020%H\u0016J\b\u0010,\u001a\u00020%H\u0016J\b\u0010-\u001a\u00020%H\u0016J\u001a\u0010.\u001a\u00020%2\u0006\u0010/\u001a\u0002002\b\u00101\u001a\u0004\u0018\u000102H\u0016J!\u00103\u001a\u00020%2\b\u00104\u001a\u0004\u0018\u0001052\b\u0010#\u001a\u0004\u0018\u00010\u0017H\u0002\u00a2\u0006\u0002\u00106J!\u00107\u001a\u00020%2\b\u00104\u001a\u0004\u0018\u0001052\b\u0010#\u001a\u0004\u0018\u00010 H\u0002\u00a2\u0006\u0002\u00108J\b\u00109\u001a\u00020%H\u0002J\b\u0010:\u001a\u00020%H\u0002J\u0010\u0010;\u001a\u00020%2\u0006\u0010<\u001a\u00020=H\u0002R\u001b\u0010\u0003\u001a\u00020\u00048BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0007\u0010\b\u001a\u0004\b\u0005\u0010\u0006R\u001b\u0010\t\u001a\u00020\n8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\r\u0010\u000e\u001a\u0004\b\u000b\u0010\fR\u000e\u0010\u000f\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u0011\u001a\u00020\u00128BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0015\u0010\b\u001a\u0004\b\u0013\u0010\u0014R\u001e\u0010\u0016\u001a\u0004\u0018\u00010\u0017X\u0086\u000e\u00a2\u0006\u0010\n\u0002\u0010\u001c\u001a\u0004\b\u0018\u0010\u0019\"\u0004\b\u001a\u0010\u001b\u00a8\u0006>"}, d2 = {"Lcom/manaknight/app/ui/HomeFragment;", "Landroidx/fragment/app/Fragment;", "()V", "baasViewModel", "Lcom/manaknight/app/viewmodels/BaasViewModel;", "getBaasViewModel", "()Lcom/manaknight/app/viewmodels/BaasViewModel;", "baasViewModel$delegate", "Lkotlin/Lazy;", "binding", "LManaknight/databinding/FragmentHomeBinding;", "getBinding", "()LManaknight/databinding/FragmentHomeBinding;", "binding$delegate", "Lcom/manaknight/app/extensions/FragmentDelegate;", "dialog", "Landroid/app/Dialog;", "pref", "Lcom/manaknight/app/data/local/AppPreferences;", "getPref", "()Lcom/manaknight/app/data/local/AppPreferences;", "pref$delegate", "userId", "", "getUserId", "()Ljava/lang/Integer;", "setUserId", "(Ljava/lang/Integer;)V", "Ljava/lang/Integer;", "formatCurrency", "", "amount", "", "(Ljava/lang/Double;)Ljava/lang/String;", "formatPercentage", "value", "loadProjectStats", "", "onCreateOptionsMenu", "menu", "Landroid/view/Menu;", "inflater", "Landroid/view/MenuInflater;", "onPause", "onResume", "onStop", "onViewCreated", "view", "Landroid/view/View;", "savedInstanceState", "Landroid/os/Bundle;", "setValueColor", "textView", "Landroid/widget/TextView;", "(Landroid/widget/TextView;Ljava/lang/Integer;)V", "setValueColorForCurrency", "(Landroid/widget/TextView;Ljava/lang/Double;)V", "showHomepageExplainerDialog", "showHomepageExplainerIfNeeded", "updateProjectStatsUI", "response", "Lcom/manaknight/app/model/remote/GetProjectStatsResponse;", "app_debug"})
public final class HomeFragment extends androidx.fragment.app.Fragment {
    @org.jetbrains.annotations.NotNull()
    private final com.manaknight.app.extensions.FragmentDelegate binding$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy baasViewModel$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy pref$delegate = null;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Integer userId;
    private android.app.Dialog dialog;
    
    public HomeFragment() {
        super();
    }
    
    private final Manaknight.databinding.FragmentHomeBinding getBinding() {
        return null;
    }
    
    private final com.manaknight.app.viewmodels.BaasViewModel getBaasViewModel() {
        return null;
    }
    
    private final com.manaknight.app.data.local.AppPreferences getPref() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getUserId() {
        return null;
    }
    
    public final void setUserId(@org.jetbrains.annotations.Nullable()
    java.lang.Integer p0) {
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void loadProjectStats() {
    }
    
    private final void updateProjectStatsUI(com.manaknight.app.model.remote.GetProjectStatsResponse response) {
    }
    
    private final java.lang.String formatCurrency(java.lang.Double amount) {
        return null;
    }
    
    private final java.lang.String formatPercentage(java.lang.Double value) {
        return null;
    }
    
    private final void setValueColor(android.widget.TextView textView, java.lang.Integer value) {
    }
    
    private final void setValueColorForCurrency(android.widget.TextView textView, java.lang.Double value) {
    }
    
    private final void showHomepageExplainerIfNeeded() {
    }
    
    private final void showHomepageExplainerDialog() {
    }
    
    @java.lang.Override()
    public void onCreateOptionsMenu(@org.jetbrains.annotations.NotNull()
    android.view.Menu menu, @org.jetbrains.annotations.NotNull()
    android.view.MenuInflater inflater) {
    }
    
    @java.lang.Override()
    public void onPause() {
    }
    
    @java.lang.Override()
    public void onResume() {
    }
    
    @java.lang.Override()
    public void onStop() {
    }
}