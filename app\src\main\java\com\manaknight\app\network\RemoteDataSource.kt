
package com.manaknight.app.network


import com.manaknight.app.model.remote.*
import com.manaknight.app.model.remote.profitPro.CreateLineItemReqModel
import com.manaknight.app.model.remote.profitPro.CompanyRequest
import com.manaknight.app.model.remote.profitPro.CreatePercentageDrawRequest
import com.manaknight.app.model.remote.profitPro.CreatePriceDrawRequest
import com.manaknight.app.model.remote.profitPro.MaterialReqModel
import com.manaknight.app.model.remote.profitPro.CustomerModel
import com.manaknight.app.model.remote.profitPro.DefaultModel
import com.manaknight.app.model.remote.profitPro.LinearFootReqModel
import com.manaknight.app.model.remote.profitPro.MaterialRequestModel
import com.manaknight.app.model.remote.profitPro.ProjectModel
import com.manaknight.app.model.remote.profitPro.SendInvoiceRequest
import com.manaknight.app.model.remote.profitPro.UpdateDrawRequest
import com.manaknight.app.model.remote.profitPro.UpdateDrawRequest2
import com.manaknight.app.model.remote.profitPro.UpdateLineItemReqModel
import org.koin.dsl.module
import okhttp3.MultipartBody

val remoteDataSourceModule = module {
    factory { RemoteDataSource(get()) }
}

class RemoteDataSource(
  private val apiService: ApiService
) : BaseDataSource() {

    suspend fun signupCompanySeup( request: CompanyRequest) = getResult {
        apiService.signupCompanySeup(request)
    }

    suspend fun updateCompanyDefault( request: DefaultModel) = getResult {
        apiService.updateCompanyDefault(request)
    }

    suspend fun searchCustomers(searchText: String?) = getResult {
        apiService.searchCustomers(searchText, 100)
    }

    suspend fun createMaterial(request: MaterialRequestModel) = getResult {
        apiService.createMaterial(request)
    }

    suspend fun updateMaterial( request: MaterialRequestModel, id: Int?) = getResult {
        apiService.updateMaterial(request,   id)
    }

    suspend fun createCustomer(request: CustomerModel) = getResult {
        apiService.createCustomer(request)
    }

    suspend fun updateCustomer( request: CustomerModel, id: Int?) = getResult {
        apiService.updateCustomer(request,   id)
    }

    suspend fun createNewEstimation(request: ProjectModel) = getResult {
        apiService.createNewEstimation(request)
    }

    suspend fun getDefaultMaterialList( order: String?,
                                           size: String?,
                                           filter: String?,
                                           join: String?,  ) = getResult {
        apiService.getDefaultMaterialList(filter)
    }

    suspend fun createDefaultMaterial(request: MaterialReqModel ) = getResult {
        apiService.createDefaultMaterial(request)
    }

    suspend fun addLineItem(request: CreateLineItemReqModel) = getResult {
        apiService.addLineItem(request)
    }

    suspend fun updateLineItem(itemID: Int, request: UpdateLineItemReqModel) = getResult {
        apiService.updateLineItem(itemID, request)
    }

    suspend fun getSquareFootLinealFootCosts( type: String?) = getResult {
        apiService.getSquareFootLinealFootCosts(type)
    }

    suspend fun addLinealFootCost(request: LinearFootReqModel) = getResult {
        apiService.addLinealFootCost(request)
    }

    suspend fun addSquareFootCost( request: LinearFootReqModel) = getResult {
        apiService.addSquareFootCost(request)
    }

    suspend fun getSingleProjectDetails(projectId: Int) = getResult {
        apiService.getSingleProjectDetails(projectId)
    }
    suspend fun getAllProjects(user_id: Int) = getResult {
        apiService.getAllProjects("user_id,cs,${user_id}", "customer,name")
    }

    suspend fun initializeDraws(projectId: Int) = getResult {
        apiService.initializeDraws(projectId)
    }

    suspend fun getAllDraws(projectId: Int) = getResult {
        apiService.getAllDraws(projectId)
    }

    suspend fun deleteDraws(id: Int?) = getResult {
        apiService.deleteDraws(id)
    }

    suspend fun createPriceDraw( request: CreatePriceDrawRequest) = getResult {
        apiService.createPriceDraw(request)
    }

    suspend fun createPercentageDraw( request: CreatePercentageDrawRequest) = getResult {
        apiService.createPercentageDraw(request)
    }

    suspend fun sendInvoice( request: SendInvoiceRequest) = getResult {
        apiService.sendInvoice(request)
    }

    suspend fun updatePriceDraw(request: UpdateDrawRequest, id: Int?) = getResult {
        apiService.updatePriceDraw(request, id)
    }

    suspend fun updatePercentageDraw(request: UpdateDrawRequest2, id: Int?) = getResult {
        apiService.updatePercentageDraw(request, id)
    }

    suspend fun deleteLineItems(id: Int?) = getResult {
        apiService.deleteLineItems(id)
    }

























  suspend fun getAllUser() = getResult {
    apiService.getAllUser()
}
suspend fun createRoomRequests( request: CreateRoomRequests) = getResult {
  apiService.createRoomRequests(request)
}
  suspend fun getChats( request: ChatRequest) = getResult {
    apiService.getChats(request)
}

suspend fun getStartPool( user_id: Int) = getResult {
    apiService.getStartPool(user_id)
}

suspend fun getAllRoom( user_id: Int) = getResult {
    apiService.getAllRoom(user_id)
}

suspend fun sendMessageToBot( request: String) = getResult {
  apiService.sendMessageToBot(request)
}

suspend fun sendMessageToBot( request: ChatBotRequest) = getResult {
  apiService.sendMessageToBot(request)
}

suspend fun sendTextMessage( request: ChatTextRequest) = getResult {
    apiService.sendTextMessage(request)
}


      suspend fun createChangeOrder( request: CreateChangeOrderRequest,    projectId: Any,  ) = getResult {
        apiService.createChangeOrder(request,   projectId,  )
      }

      suspend fun finalizeProject(   ) = getResult {
        apiService.finalizeProject(   )
      }

      suspend fun getProjectReview(   projectId: Any,  ) = getResult {
        apiService.getProjectReview(   projectId,  )
      }

      suspend fun updateDraws( request: UpdateDrawsRequest,    projectId: Int,  ) = getResult {
        apiService.updateDraws(request,   projectId,  )
      }







      suspend fun trackingMaterial(   projectId: Int,  ) = getResult {
        apiService.trackingMaterial(   projectId,  )
      }

      suspend fun getProjectTrackingDetails(   projectId: Int,  ) = getResult {
        apiService.getProjectTrackingDetails(   projectId,  )
      }
      suspend fun trackingLabour(   projectId: Int,  ) = getResult {
        apiService.trackingLabour(   projectId,  )
      }

      suspend fun trackingDraws(   projectId: Int,   status: String?,  ) = getResult {
        apiService.trackingDraws(   projectId,   status  )
      }

      suspend fun getLineDetails(   lineId: Any,  ) = getResult {
        apiService.getLineDetails(   lineId,  )
      }



      suspend fun finalizingOnboarding(   ) = getResult {
        apiService.finalizingOnboarding(   )
      }







      suspend fun initializeUser(   ) = getResult {
        apiService.initializeUser(   )
      }

      suspend fun saveDefaultsOnbording( request: SaveDefaultsOnbordingRequest,    ) = getResult {
        apiService.saveDefaultsOnbording(request,   )
      }

      suspend fun getProjects(    type: String?,
 timePeriod: String?,  ) = getResult {
        apiService.getProjects(    type,
 timePeriod  )
      }

      suspend fun onboarding( request: OnboardingRequest,    ) = getResult {
        apiService.onboarding(request,   )
      }

      suspend fun companyOverview(   ) = getResult {
        apiService.companyOverview(   )
      }

      suspend fun companyDetails(   ) = getResult {
        apiService.companyDetails(   )
      }

      suspend fun getProjectStats(   ) = getResult {
        apiService.getProjectStats(   )
      }

      suspend fun lambdaCheck( request: LambdaCheckRequest,    ) = getResult {
        apiService.lambdaCheck(request,   )
      }

      suspend fun twoFALogin( request: TwoFALoginRequest,    ) = getResult {
        apiService.twoFALogin(request,   )
      }

      suspend fun twoFASignin( request: TwoFASigninRequest,    ) = getResult {
        apiService.twoFASignin(request,   )
      }

      suspend fun twoFAAuthorize( request: TwoFAAuthorizeRequest,    ) = getResult {
        apiService.twoFAAuthorize(request,   )
      }

      suspend fun twoFAEnable( request: TwoFAEnableRequest,    ) = getResult {
        apiService.twoFAEnable(request,   )
      }

      suspend fun twoFADisable( request: TwoFADisableRequest,    ) = getResult {
        apiService.twoFADisable(request,   )
      }

      suspend fun twoFAVerify( request: TwoFAVerifyRequest,    ) = getResult {
        apiService.twoFAVerify(request,   )
      }

      suspend fun twoFAAuth( request: TwoFAAuthRequest,    ) = getResult {
        apiService.twoFAAuth(request,   )
      }

      suspend fun analyticsLog( request: AnalyticsLogRequest,    ) = getResult {
        apiService.analyticsLog(request,   )
      }

      suspend fun getAnalytics(   ) = getResult {
        apiService.getAnalytics(   )
      }

      suspend fun logHeatmapAnalytics( request: LogHeatmapAnalyticsRequest,    ) = getResult {
        apiService.logHeatmapAnalytics(request,   )
      }

      suspend fun getHeatmapData(    customDate: String?,  ) = getResult {
        apiService.getHeatmapData(    customDate  )
      }

      suspend fun userSessionsData(    customDate: String?,  ) = getResult {
        apiService.userSessionsData(    customDate  )
      }

      suspend fun createUserSessionsAnalytics( request: CreateUserSessionsAnalyticsRequest,    ) = getResult {
        apiService.createUserSessionsAnalytics(request,   )
      }

      suspend fun appleLoginMobileEndpoint( request: AppleLoginMobileEndpointRequest,    ) = getResult {
        apiService.appleLoginMobileEndpoint(request,   )
      }

      suspend fun appleLogin(   ) = getResult {
        apiService.appleLogin(   )
      }

      suspend fun appleAuthCode( request: AppleAuthCodeRequest,    ) = getResult {
        apiService.appleAuthCode(request,   )
      }

      suspend fun googleCode(    state: String,  ) = getResult {
        apiService.googleCode(    state  )
      }

      suspend fun googleCodeMobile(    role: String?,
 isRefresh: Boolean?,
 code: String?,  ) = getResult {
        apiService.googleCodeMobile(    role,
 isRefresh,
 code  )
      }

      suspend fun googleLogin(    role: String?,
 companyId: String?,
 isRefresh: Boolean?,  ) = getResult {
        apiService.googleLogin(    role,
 companyId,
 isRefresh  )
      }

      suspend fun blogAll(    limit: Int,
 offset: Int,  ) = getResult {
        apiService.blogAll(    limit,
 offset  )
      }

      suspend fun blogSimilar(    top: Int,  ) = getResult {
        apiService.blogSimilar(    top  )
      }

      suspend fun blogFilter(    categories: ArrayList<Map<String, Any>>,
 tags: ArrayList<Map<String, Any>>,
 rule: String?,
 search: String?,
 limit: Int,
 page: Int,  ) = getResult {
        apiService.blogFilter(    categories,
 tags,
 rule,
 search,
 limit,
 page  )
      }

      suspend fun blogCreate( request: BlogCreateRequest,    ) = getResult {
        apiService.blogCreate(request,   )
      }

      suspend fun blogEdit( request: BlogEditRequest,    id: String?,  ) = getResult {
        apiService.blogEdit(request,   id,  )
      }

      suspend fun blogDelete(   id: String?,  ) = getResult {
        apiService.blogDelete(   id,  )
      }

      suspend fun blogSingle(   id: String?,  ) = getResult {
        apiService.blogSingle(   id,  )
      }

      suspend fun blogTags( request: BlogTagsRequest,    ) = getResult {
        apiService.blogTags(request,   )
      }

      suspend fun blogTagsUpdate( request: BlogTagsUpdateRequest,    ) = getResult {
        apiService.blogTagsUpdate(request,   )
      }

      suspend fun blogTagsRetrieve(    limit: Int,
 page: Int,
 name: String?,  ) = getResult {
        apiService.blogTagsRetrieve(    limit,
 page,
 name  )
      }

      suspend fun blogTagsDeleteByID(   id: String?,  ) = getResult {
        apiService.blogTagsDeleteByID(   id,  )
      }

      suspend fun createBlogCategory( request: CreateBlogCategoryRequest,    ) = getResult {
        apiService.createBlogCategory(request,   )
      }

      suspend fun updateBlogCategory( request: UpdateBlogCategoryRequest,    ) = getResult {
        apiService.updateBlogCategory(request,   )
      }

      suspend fun getBlogCategory(    limit: Int,
 page: Int,
 name: String?,  ) = getResult {
        apiService.getBlogCategory(    limit,
 page,
 name  )
      }

      suspend fun getBlogSubcategory(   id: String?,  ) = getResult {
        apiService.getBlogSubcategory(   id,  )
      }

      suspend fun deleteBlogCategory(   id: String?,  ) = getResult {
        apiService.deleteBlogCategory(   id,  )
      }

      suspend fun captchaTest(   width: Int,
 height: Int,  ) = getResult {
        apiService.captchaTest(   width,
 height,  )
      }

      suspend fun captchaGenerate(   width: Int,
 height: Int,  ) = getResult {
        apiService.captchaGenerate(   width,
 height,  )
      }

      suspend fun googleCaptchaVerify( request: GoogleCaptchaVerifyRequest,    ) = getResult {
        apiService.googleCaptchaVerify(request,   )
      }

      suspend fun createCMSLambda( request: CreateCMSLambdaRequest,    ) = getResult {
        apiService.createCMSLambda(request,   )
      }

      suspend fun updateCMSLambda( request: UpdateCMSLambdaRequest,    id: String?,  ) = getResult {
        apiService.updateCMSLambda(request,   id,  )
      }

      suspend fun deleteCMSLambda(   id: String?,  ) = getResult {
        apiService.deleteCMSLambda(   id,  )
      }

      suspend fun getCMSByIDLambda(   id: String?,  ) = getResult {
        apiService.getCMSByIDLambda(   id,  )
      }

      suspend fun getCMSByPageAndKeyLambda(   page: String?,
 key: String?,  ) = getResult {
        apiService.getCMSByPageAndKeyLambda(   page,
 key,  )
      }

      suspend fun getCMSByPageLambda(   page: String?,  ) = getResult {
        apiService.getCMSByPageLambda(   page,  )
      }

      suspend fun getAllCMSLambda(   ) = getResult {
        apiService.getAllCMSLambda(   )
      }

      suspend fun registerLambda( request: RegisterLambdaRequest,    ) = getResult {
        apiService.registerLambda(request,   )
      }

      suspend fun loginLambda( request: LoginLambdaRequest,    ) = getResult {
        apiService.loginLambda(request,   )
      }

      suspend fun marketingLoginLambda( request: MarketingLoginLambdaRequest,    ) = getResult {
        apiService.marketingLoginLambda(request,   )
      }

      suspend fun profile(   ) = getResult {
        apiService.profile(   )
      }

      suspend fun profileUpdate( request: ProfileUpdateRequest,    ) = getResult {
        apiService.profileUpdate(request,   )
      }

      suspend fun uploadImageLocalDefault(     file: MultipartBody.Part) = getResult {
        apiService.uploadImageLocalDefault(    file)
      }

      suspend fun uploadimages3(     file: MultipartBody.Part) = getResult {
        apiService.uploadimages3(    file)
      }

      suspend fun preferenceFetch(   ) = getResult {
        apiService.preferenceFetch(   )
      }

      suspend fun preferenceUpdate( request: PreferenceUpdateRequest,    ) = getResult {
        apiService.preferenceUpdate(request,   )
      }

      suspend fun getSowTree(    order: String?,
 page: String?,  ) = getResult {
        apiService.getSowTree(    order,
 page  )
      }

      suspend fun appAlertsList(    order: String?,
 page: String?,
 filter: String?,  ) = getResult {
        apiService.appAlertsList(    order,
 page,
 filter  )
      }

      suspend fun appAlertsUpdate( request: AppAlertsUpdateRequest,    id: String?,  ) = getResult {
        apiService.appAlertsUpdate(request,   id,  )
      }

      suspend fun retrieveProductDefault( request: RetrieveProductDefaultRequest,    ) = getResult {
        apiService.retrieveProductDefault(request,   )
      }

      suspend fun ecomProductByIDDefault(   ) = getResult {
        apiService.ecomProductByIDDefault(   )
      }

      suspend fun addEcomProductLambda( request: AddEcomProductLambdaRequest,    ) = getResult {
        apiService.addEcomProductLambda(request,   )
      }

      suspend fun editEcomProductLambda( request: EditEcomProductLambdaRequest,    id: Number?,  ) = getResult {
        apiService.editEcomProductLambda(request,   id,  )
      }

      suspend fun deleteEcomProductLambda(   id: Number?,  ) = getResult {
        apiService.deleteEcomProductLambda(   id,  )
      }

      suspend fun getCartItems(    userId: String?,  ) = getResult {
        apiService.getCartItems(    userId  )
      }

      suspend fun ecomAddCart( request: EcomAddCartRequest,    ) = getResult {
        apiService.ecomAddCart(request,   )
      }

      suspend fun ecomDeleteCartItem(    userId: String?,
 data: String?,  ) = getResult {
        apiService.ecomDeleteCartItem(    userId,
 data  )
      }

      suspend fun ecomGetProductReview(    productid: Int?,  ) = getResult {
        apiService.ecomGetProductReview(    productid  )
      }

      suspend fun ecomAddProductReview(    review: String?,
 productid: Int?,  ) = getResult {
        apiService.ecomAddProductReview(    review,
 productid  )
      }

      suspend fun forgotPassword( request: ForgotPasswordRequest,    ) = getResult {
        apiService.forgotPassword(request,   )
      }

      suspend fun forgotPasswordMobile( request: ForgotPasswordMobileRequest,    ) = getResult {
        apiService.forgotPasswordMobile(request,   )
      }

      suspend fun resetPassword( request: ResetPasswordRequest,    ) = getResult {
        apiService.resetPassword(request,   )
      }

      suspend fun resetPasswordMobile( request: ResetPasswordMobileRequest,    ) = getResult {
        apiService.resetPasswordMobile(request,   )
      }

      suspend fun getStripeData( request: GetStripeDataRequest,    ) = getResult {
        apiService.getStripeData(request,   )
      }

      suspend fun getOneDefaultSquareFootCost(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneDefaultSquareFootCost(   id,   join  )
      }

      suspend fun getOneSetting(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneSetting(   id,   join  )
      }

      suspend fun getOneCost(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneCost(   id,   join  )
      }

      suspend fun getOneRoom(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneRoom(   id,   join  )
      }

      suspend fun getOneLabor(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneLabor(   id,   join  )
      }

      suspend fun getOneLineItemEntry(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneLineItemEntry(   id,   join  )
      }

      suspend fun getOneCompanySettings(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneCompanySettings(   id,   join  )
      }

      suspend fun getOneCms(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneCms(   id,   join  )
      }

      suspend fun getOneTeamMember(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneTeamMember(   id,   join  )
      }

      suspend fun getOneDefaultMaterial(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneDefaultMaterial(   id,   join  )
      }

      suspend fun getOneProject(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneProject(   id,   join  )
      }

      suspend fun getOneUser(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneUser(   id,   join  )
      }

      suspend fun getOneProfile(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneProfile(   id,   join  )
      }

      suspend fun getOneLinealFootCost(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneLinealFootCost(   id,   join  )
      }

      suspend fun getOneCustomer(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneCustomer(   id,   join  )
      }

      suspend fun getOnePermission(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOnePermission(   id,   join  )
      }

      suspend fun getOneToken(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneToken(   id,   join  )
      }

      suspend fun getOneSqftCosts(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneSqftCosts(   id,   join  )
      }

      suspend fun getOneEmail(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneEmail(   id,   join  )
      }

      suspend fun getOneAlerts(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneAlerts(   id,   join  )
      }

      suspend fun getOneDraws(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneDraws(   id,   join  )
      }

      suspend fun getOneChat(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneChat(   id,   join  )
      }

      suspend fun getOneMaterial(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneMaterial(   id,   join  )
      }

      suspend fun getOneInvoice(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneInvoice(   id,   join  )
      }

      suspend fun getOneDefaultLinealFootCost(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneDefaultLinealFootCost(   id,   join  )
      }

      suspend fun getOneTriggerType(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneTriggerType(   id,   join  )
      }

      suspend fun getOneJob(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneJob(   id,   join  )
      }

      suspend fun getOneLineItems(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneLineItems(   id,   join  )
      }

      suspend fun getOnePhoto(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOnePhoto(   id,   join  )
      }

      suspend fun getOneApiKeys(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneApiKeys(   id,   join  )
      }

      suspend fun getOneChangeOrderDescription(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneChangeOrderDescription(   id,   join  )
      }

      suspend fun getOneAnalyticLog(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneAnalyticLog(   id,   join  )
      }

      suspend fun getOnePosts(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOnePosts(   id,   join  )
      }

      suspend fun getOneEmployee(   id: Int?,   join: String?,  ) = getResult {
        apiService.getOneEmployee(   id,   join  )
      }

      suspend fun getDefaultSquareFootCostList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getDefaultSquareFootCostList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getSettingList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getSettingList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getCostList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getCostList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getRoomList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getRoomList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getLaborList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getLaborList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getLineItemEntryList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getLineItemEntryList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getCompanySettingsList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getCompanySettingsList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getCmsList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getCmsList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getTeamMemberList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getTeamMemberList(    order,
 size,
 filter,
 join  )
      }



      suspend fun getProjectList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getProjectList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getUserList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getUserList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getProfileList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getProfileList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getLinealFootCostList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getLinealFootCostList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getCustomerList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getCustomerList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getPermissionList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getPermissionList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getTokenList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getTokenList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getSqftCostsList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getSqftCostsList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getEmailList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getEmailList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getAlertsList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getAlertsList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getDrawsList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getDrawsList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getChatList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getChatList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getMaterialList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getMaterialList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getInvoiceList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getInvoiceList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getDefaultLinealFootCostList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getDefaultLinealFootCostList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getTriggerTypeList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getTriggerTypeList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getJobList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getJobList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getLineItemsList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getLineItemsList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getPhotoList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getPhotoList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getApiKeysList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getApiKeysList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getChangeOrderDescriptionList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getChangeOrderDescriptionList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getAnalyticLogList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getAnalyticLogList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getPostsList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getPostsList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getEmployeeList(    order: String?,
 size: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getEmployeeList(    order,
 size,
 filter,
 join  )
      }

      suspend fun getDefaultSquareFootCostPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getDefaultSquareFootCostPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getSettingPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getSettingPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getCostPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getCostPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getRoomPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getRoomPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getLaborPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getLaborPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getLineItemEntryPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getLineItemEntryPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getCompanySettingsPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getCompanySettingsPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getCmsPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getCmsPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getTeamMemberPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getTeamMemberPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getDefaultMaterialPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getDefaultMaterialPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getProjectPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getProjectPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getUserPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getUserPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getProfilePaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getProfilePaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getLinealFootCostPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getLinealFootCostPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getCustomerPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getCustomerPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getPermissionPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getPermissionPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getTokenPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getTokenPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getSqftCostsPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getSqftCostsPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getEmailPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getEmailPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getAlertsPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getAlertsPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getDrawsPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getDrawsPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getChatPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getChatPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getMaterialPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getMaterialPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getInvoicePaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getInvoicePaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getDefaultLinealFootCostPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getDefaultLinealFootCostPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getTriggerTypePaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getTriggerTypePaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getJobPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getJobPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getLineItemsPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getLineItemsPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getPhotoPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getPhotoPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getApiKeysPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getApiKeysPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getChangeOrderDescriptionPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getChangeOrderDescriptionPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getAnalyticLogPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getAnalyticLogPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getPostsPaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getPostsPaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun getEmployeePaginated(    order: String?,
 page: String?,
 filter: String?,
 join: String?,  ) = getResult {
        apiService.getEmployeePaginated(    order,
 page,
 filter,
 join  )
      }

      suspend fun createDefaultSquareFootCost( request: CreateDefaultSquareFootCostRequest,    ) = getResult {
        apiService.createDefaultSquareFootCost(request,   )
      }

      suspend fun createSetting( request: CreateSettingRequest,    ) = getResult {
        apiService.createSetting(request,   )
      }

      suspend fun createCost( request: CreateCostRequest,    ) = getResult {
        apiService.createCost(request,   )
      }

      suspend fun createRoom( request: CreateRoomRequest,    ) = getResult {
        apiService.createRoom(request,   )
      }

      suspend fun createLabor( request: CreateLaborRequest,    ) = getResult {
        apiService.createLabor(request,   )
      }

      suspend fun createLineItemEntry( request: CreateLineItemEntryRequest,    ) = getResult {
        apiService.createLineItemEntry(request,   )
      }

      suspend fun createCompanySettings( request: CreateCompanySettingsRequest,    ) = getResult {
        apiService.createCompanySettings(request,   )
      }

      suspend fun createCms( request: CreateCmsRequest,    ) = getResult {
        apiService.createCms(request,   )
      }

      suspend fun createTeamMember( request: CreateTeamMemberRequest,    ) = getResult {
        apiService.createTeamMember(request,   )
      }

      suspend fun createDraw( request: CreateDrawRequest,    ) = getResult {
        apiService.createDraw(request,   )
      }



      suspend fun createProject( request: CreateProjectRequest,    ) = getResult {
        apiService.createProject(request,   )
      }

      suspend fun createUser( request: CreateUserRequest,    ) = getResult {
        apiService.createUser(request,   )
      }

      suspend fun createProfile( request: CreateProfileRequest,    ) = getResult {
        apiService.createProfile(request,   )
      }

      suspend fun createLinealFootCost( request: CreateLinealFootCostRequest,    ) = getResult {
        apiService.createLinealFootCost(request,   )
      }



      suspend fun createPermission( request: CreatePermissionRequest,    ) = getResult {
        apiService.createPermission(request,   )
      }

      suspend fun createToken( request: CreateTokenRequest,    ) = getResult {
        apiService.createToken(request,   )
      }

      suspend fun createSqftCosts( request: CreateSqftCostsRequest,    ) = getResult {
        apiService.createSqftCosts(request,   )
      }

      suspend fun createEmail( request: CreateEmailRequest,    ) = getResult {
        apiService.createEmail(request,   )
      }

      suspend fun createAlerts( request: CreateAlertsRequest,    ) = getResult {
        apiService.createAlerts(request,   )
      }

      suspend fun createChat( request: CreateChatRequest,    ) = getResult {
        apiService.createChat(request,   )
      }

      suspend fun createMaterial( request: CreateMaterialRequest,    ) = getResult {
        apiService.createMaterial(request,   )
      }

      suspend fun createInvoice( request: CreateInvoiceRequest,    ) = getResult {
        apiService.createInvoice(request,   )
      }

      suspend fun createDefaultLinealFootCost( request: CreateDefaultLinealFootCostRequest,    ) = getResult {
        apiService.createDefaultLinealFootCost(request,   )
      }

      suspend fun createTriggerType( request: CreateTriggerTypeRequest,    ) = getResult {
        apiService.createTriggerType(request,   )
      }

      suspend fun createJob( request: CreateJobRequest,    ) = getResult {
        apiService.createJob(request,   )
      }

      suspend fun createLineItems( request: CreateLineItemsRequest,    ) = getResult {
        apiService.createLineItems(request,   )
      }

      suspend fun createPhoto( request: CreatePhotoRequest,    ) = getResult {
        apiService.createPhoto(request,   )
      }

      suspend fun createApiKeys( request: CreateApiKeysRequest,    ) = getResult {
        apiService.createApiKeys(request,   )
      }

      suspend fun createChangeOrderDescription( request: CreateChangeOrderDescriptionRequest,    ) = getResult {
        apiService.createChangeOrderDescription(request,   )
      }

      suspend fun createAnalyticLog( request: CreateAnalyticLogRequest,    ) = getResult {
        apiService.createAnalyticLog(request,   )
      }

      suspend fun createPosts( request: CreatePostsRequest,    ) = getResult {
        apiService.createPosts(request,   )
      }

      suspend fun createEmployee( request: CreateEmployeeRequest,    ) = getResult {
        apiService.createEmployee(request,   )
      }

      suspend fun updateDefaultSquareFootCost( request: UpdateDefaultSquareFootCostRequest,    id: Int?,  ) = getResult {
        apiService.updateDefaultSquareFootCost(request,   id,  )
      }

      suspend fun updateSetting( request: UpdateSettingRequest,    id: Int?,  ) = getResult {
        apiService.updateSetting(request,   id,  )
      }

      suspend fun updateCost( request: UpdateCostRequest,    id: Int?,  ) = getResult {
        apiService.updateCost(request,   id,  )
      }

      suspend fun updateRoom( request: UpdateRoomRequest,    id: Int?,  ) = getResult {
        apiService.updateRoom(request,   id,  )
      }

      suspend fun updateLabor( request: UpdateLaborRequest,    id: Int?,  ) = getResult {
        apiService.updateLabor(request,   id,  )
      }

      suspend fun updateLineItemEntry( request: UpdateLineItemEntryRequest,    id: Int?,  ) = getResult {
        apiService.updateLineItemEntry(request,   id,  )
      }

      suspend fun updateCompanySettings( request: UpdateCompanySettingsRequest,    id: Int?,  ) = getResult {
        apiService.updateCompanySettings(request,   id,  )
      }

      suspend fun updateCms( request: UpdateCmsRequest,    id: Int?,  ) = getResult {
        apiService.updateCms(request,   id,  )
      }

      suspend fun updateTeamMember( request: UpdateTeamMemberRequest,    id: Int?,  ) = getResult {
        apiService.updateTeamMember(request,   id,  )
      }

      suspend fun updateTeamMemberHours( request: UpdateTeamMemberHoursRequest,    id: Int?,  ) = getResult {
        apiService.updateTeamMemberHours(request,   id,  )
      }

      suspend fun updateDefaultMaterial( request: UpdateDefaultMaterialRequest,    id: Int?,  ) = getResult {
        apiService.updateDefaultMaterial(request,   id,  )
      }
      suspend fun updateLinealFootCost( request: LinearFootReqModel,    id: Int?,  ) = getResult {
        apiService.updateLinealFootCost(request,   id,  )
      }
      suspend fun updateSquareFootCost( request: LinearFootReqModel,    id: Int?,  ) = getResult {
        apiService.updateSquareFootCost(request,   id,  )
      }

      suspend fun updateProject( request: UpdateProjectRequest,    id: Int?,  ) = getResult {
        apiService.updateProject(request,   id,  )
      }

      suspend fun updateUser( request: UpdateUserRequest,    id: Int?,  ) = getResult {
        apiService.updateUser(request,   id,  )
      }

      suspend fun updateProfile( request: UpdateProfileRequest,    id: Int?,  ) = getResult {
        apiService.updateProfile(request,   id,  )
      }

      suspend fun updateLinealFootCost( request: UpdateLinealFootCostRequest,    id: Int?,  ) = getResult {
        apiService.updateLinealFootCost(request,   id,  )
      }

      suspend fun updatePermission( request: UpdatePermissionRequest,    id: Int?,  ) = getResult {
        apiService.updatePermission(request,   id,  )
      }

      suspend fun updateToken( request: UpdateTokenRequest,    id: Int?,  ) = getResult {
        apiService.updateToken(request,   id,  )
      }

      suspend fun updateSqftCosts( request: UpdateSqftCostsRequest,    id: Int?,  ) = getResult {
        apiService.updateSqftCosts(request,   id,  )
      }

      suspend fun updateEmail( request: UpdateEmailRequest,    id: Int?,  ) = getResult {
        apiService.updateEmail(request,   id,  )
      }

      suspend fun updateAlerts( request: UpdateAlertsRequest,    id: Int?,  ) = getResult {
        apiService.updateAlerts(request,   id,  )
      }

      suspend fun updateDraws( request: UpdateDrawsRequest,    id: Int?,  ) = getResult {
        apiService.updateDraws(request,   id,  )
      }

      suspend fun updateChat( request: UpdateChatRequest,    id: Int?,  ) = getResult {
        apiService.updateChat(request,   id,  )
      }

      suspend fun updateMaterial( request: UpdateMaterialRequest,    id: Int?,  ) = getResult {
        apiService.updateMaterial(request,   id,  )
      }

      suspend fun updateInvoice( request: UpdateInvoiceRequest,    id: Int?,  ) = getResult {
        apiService.updateInvoice(request,   id,  )
      }

      suspend fun updateDefaultLinealFootCost( request: UpdateDefaultLinealFootCostRequest,    id: Int?,  ) = getResult {
        apiService.updateDefaultLinealFootCost(request,   id,  )
      }

      suspend fun updateTriggerType( request: UpdateTriggerTypeRequest,    id: Int?,  ) = getResult {
        apiService.updateTriggerType(request,   id,  )
      }

      suspend fun updateJob( request: UpdateJobRequest,    id: Int?,  ) = getResult {
        apiService.updateJob(request,   id,  )
      }

      suspend fun updateLineItems( request: UpdateLineItemsRequest,    id: Int?,  ) = getResult {
        apiService.updateLineItems(request,   id,  )
      }

      suspend fun updatePhoto( request: UpdatePhotoRequest,    id: Int?,  ) = getResult {
        apiService.updatePhoto(request,   id,  )
      }

      suspend fun updateApiKeys( request: UpdateApiKeysRequest,    id: Int?,  ) = getResult {
        apiService.updateApiKeys(request,   id,  )
      }

      suspend fun updateChangeOrderDescription( request: UpdateChangeOrderDescriptionRequest,    id: Int?,  ) = getResult {
        apiService.updateChangeOrderDescription(request,   id,  )
      }

      suspend fun updateAnalyticLog( request: UpdateAnalyticLogRequest,    id: Int?,  ) = getResult {
        apiService.updateAnalyticLog(request,   id,  )
      }

      suspend fun updatePosts( request: UpdatePostsRequest,    id: Int?,  ) = getResult {
        apiService.updatePosts(request,   id,  )
      }

      suspend fun updateEmployee( request: UpdateEmployeeRequest,    id: Int?,  ) = getResult {
        apiService.updateEmployee(request,   id,  )
      }

      suspend fun deleteDefaultSquareFootCost(   id: Int?,  ) = getResult {
        apiService.deleteDefaultSquareFootCost(   id,  )
      }

      suspend fun deleteSetting(   id: Int?,  ) = getResult {
        apiService.deleteSetting(   id,  )
      }

      suspend fun deleteCost(   id: Int?,  ) = getResult {
        apiService.deleteCost(   id,  )
      }

      suspend fun deleteRoom(   id: Int?,  ) = getResult {
        apiService.deleteRoom(   id,  )
      }

      suspend fun deleteLabor(   id: Int?,  ) = getResult {
        apiService.deleteLabor(   id,  )
      }

      suspend fun deleteLineItemEntry(   id: Int?,  ) = getResult {
        apiService.deleteLineItemEntry(   id,  )
      }

      suspend fun deleteCompanySettings(   id: Int?,  ) = getResult {
        apiService.deleteCompanySettings(   id,  )
      }

      suspend fun deleteCms(   id: Int?,  ) = getResult {
        apiService.deleteCms(   id,  )
      }

      suspend fun deleteTeamMember(   id: Int?,  ) = getResult {
        apiService.deleteTeamMember(   id,  )
      }

      suspend fun deleteDefaultMaterial(   id: Int?,  ) = getResult {
        apiService.deleteDefaultMaterial(   id,  )
      }
      suspend fun deleteLinealFootCosts(   id: Int?,  ) = getResult {
        apiService.deleteLinealFootCosts(   id,  )
      }
      suspend fun deleteSquareFootCost(   id: Int?,  ) = getResult {
        apiService.deleteSquareFootCost(   id,  )
      }

      suspend fun deleteProject(   id: Int?,  ) = getResult {
        apiService.deleteProject(   id,  )
      }

      suspend fun deleteUser(   id: Int?,  ) = getResult {
        apiService.deleteUser(   id,  )
      }

      suspend fun deleteProfile(   id: Int?,  ) = getResult {
        apiService.deleteProfile(   id,  )
      }

      suspend fun deleteLinealFootCost(   id: Int?,  ) = getResult {
        apiService.deleteLinealFootCost(   id,  )
      }

      suspend fun deleteCustomer(   id: Int?,  ) = getResult {
        apiService.deleteCustomer(   id,  )
      }

      suspend fun deletePermission(   id: Int?,  ) = getResult {
        apiService.deletePermission(   id,  )
      }

      suspend fun deleteToken(   id: Int?,  ) = getResult {
        apiService.deleteToken(   id,  )
      }

      suspend fun deleteSqftCosts(   id: Int?,  ) = getResult {
        apiService.deleteSqftCosts(   id,  )
      }

      suspend fun deleteEmail(   id: Int?,  ) = getResult {
        apiService.deleteEmail(   id,  )
      }

      suspend fun deleteAlerts(   id: Int?,  ) = getResult {
        apiService.deleteAlerts(   id,  )
      }



      suspend fun deleteChat(   id: Int?,  ) = getResult {
        apiService.deleteChat(   id,  )
      }

      suspend fun deleteMaterial(   id: Int?,  ) = getResult {
        apiService.deleteMaterial(   id,  )
      }

      suspend fun deleteInvoice(   id: Int?,  ) = getResult {
        apiService.deleteInvoice(   id,  )
      }

      suspend fun deleteDefaultLinealFootCost(   id: Int?,  ) = getResult {
        apiService.deleteDefaultLinealFootCost(   id,  )
      }

      suspend fun deleteTriggerType(   id: Int?,  ) = getResult {
        apiService.deleteTriggerType(   id,  )
      }

      suspend fun deleteJob(   id: Int?,  ) = getResult {
        apiService.deleteJob(   id,  )
      }



      suspend fun deletePhoto(   id: Int?,  ) = getResult {
        apiService.deletePhoto(   id,  )
      }

      suspend fun deleteApiKeys(   id: Int?,  ) = getResult {
        apiService.deleteApiKeys(   id,  )
      }

      suspend fun deleteChangeOrderDescription(   id: Int?,  ) = getResult {
        apiService.deleteChangeOrderDescription(   id,  )
      }

      suspend fun deleteAnalyticLog(   id: Int?,  ) = getResult {
        apiService.deleteAnalyticLog(   id,  )
      }

      suspend fun deletePosts(   id: Int?,  ) = getResult {
        apiService.deletePosts(   id,  )
      }

      suspend fun deleteEmployee(   id: Int?,  ) = getResult {
        apiService.deleteEmployee(   id,  )
      }

    // Subscription remote data source functions
    suspend fun createSubscription(request: CreateSubscriptionRequest) = getResult {
        apiService.createSubscription(request)
    }

    suspend fun getPlans() = getResult {
        apiService.getPlans()
    }

    suspend fun getUserSubscriptions(userId: Int) = getResult {
        apiService.getUserSubscriptions(userId)
    }

    suspend fun cancelSubscription(request: CancelSubscriptionRequest) = getResult {
        apiService.cancelSubscription(request, request.subscription_id)
    }

    suspend fun getPaymentHistory(userId: Int) = getResult {
        apiService.getPaymentHistory(userId)
    }

    suspend fun getSubscriptionStatus(subscriptionId: Int) = getResult {
        apiService.getSubscriptionStatus(subscriptionId)
    }

}
