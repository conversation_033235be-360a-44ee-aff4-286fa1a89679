// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentCreateCustomerBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnContinue;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final RelativeLayout container;

  @NonNull
  public final ConstraintLayout coss;

  @NonNull
  public final EditText edTxtAddress;

  @NonNull
  public final EditText edTxtName;

  @NonNull
  public final EditText edTxtPhone;

  @NonNull
  public final EditText edTxtUserName;

  @NonNull
  public final HeaderBinding headerInclude;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final ConstraintLayout innerConstraintLayout;

  @NonNull
  public final LinearLayout line1;

  @NonNull
  public final ScrollView scrollableContent;

  @NonNull
  public final TextView tvEmail;

  @NonNull
  public final TextView tvFirstName;

  @NonNull
  public final TextView tvLastName;

  @NonNull
  public final TextView tvPhone;

  private FragmentCreateCustomerBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnContinue, @Nullable RelativeLayout container,
      @NonNull ConstraintLayout coss, @NonNull EditText edTxtAddress, @NonNull EditText edTxtName,
      @NonNull EditText edTxtPhone, @NonNull EditText edTxtUserName,
      @NonNull HeaderBinding headerInclude, @Nullable ConstraintLayout innerConstraintLayout,
      @NonNull LinearLayout line1, @NonNull ScrollView scrollableContent, @NonNull TextView tvEmail,
      @NonNull TextView tvFirstName, @NonNull TextView tvLastName, @NonNull TextView tvPhone) {
    this.rootView = rootView;
    this.btnContinue = btnContinue;
    this.container = container;
    this.coss = coss;
    this.edTxtAddress = edTxtAddress;
    this.edTxtName = edTxtName;
    this.edTxtPhone = edTxtPhone;
    this.edTxtUserName = edTxtUserName;
    this.headerInclude = headerInclude;
    this.innerConstraintLayout = innerConstraintLayout;
    this.line1 = line1;
    this.scrollableContent = scrollableContent;
    this.tvEmail = tvEmail;
    this.tvFirstName = tvFirstName;
    this.tvLastName = tvLastName;
    this.tvPhone = tvPhone;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentCreateCustomerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentCreateCustomerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_create_customer, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentCreateCustomerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnContinue;
      MaterialButton btnContinue = ViewBindings.findChildViewById(rootView, id);
      if (btnContinue == null) {
        break missingId;
      }

      id = R.id.container;
      RelativeLayout container = ViewBindings.findChildViewById(rootView, id);

      ConstraintLayout coss = (ConstraintLayout) rootView;

      id = R.id.edTxtAddress;
      EditText edTxtAddress = ViewBindings.findChildViewById(rootView, id);
      if (edTxtAddress == null) {
        break missingId;
      }

      id = R.id.edTxtName;
      EditText edTxtName = ViewBindings.findChildViewById(rootView, id);
      if (edTxtName == null) {
        break missingId;
      }

      id = R.id.edTxtPhone;
      EditText edTxtPhone = ViewBindings.findChildViewById(rootView, id);
      if (edTxtPhone == null) {
        break missingId;
      }

      id = R.id.edTxtUserName;
      EditText edTxtUserName = ViewBindings.findChildViewById(rootView, id);
      if (edTxtUserName == null) {
        break missingId;
      }

      id = R.id.headerInclude;
      View headerInclude = ViewBindings.findChildViewById(rootView, id);
      if (headerInclude == null) {
        break missingId;
      }
      HeaderBinding binding_headerInclude = HeaderBinding.bind(headerInclude);

      id = R.id.innerConstraintLayout;
      ConstraintLayout innerConstraintLayout = ViewBindings.findChildViewById(rootView, id);

      id = R.id.line1;
      LinearLayout line1 = ViewBindings.findChildViewById(rootView, id);
      if (line1 == null) {
        break missingId;
      }

      id = R.id.scrollableContent;
      ScrollView scrollableContent = ViewBindings.findChildViewById(rootView, id);
      if (scrollableContent == null) {
        break missingId;
      }

      id = R.id.tvEmail;
      TextView tvEmail = ViewBindings.findChildViewById(rootView, id);
      if (tvEmail == null) {
        break missingId;
      }

      id = R.id.tvFirstName;
      TextView tvFirstName = ViewBindings.findChildViewById(rootView, id);
      if (tvFirstName == null) {
        break missingId;
      }

      id = R.id.tvLastName;
      TextView tvLastName = ViewBindings.findChildViewById(rootView, id);
      if (tvLastName == null) {
        break missingId;
      }

      id = R.id.tvPhone;
      TextView tvPhone = ViewBindings.findChildViewById(rootView, id);
      if (tvPhone == null) {
        break missingId;
      }

      return new FragmentCreateCustomerBinding((ConstraintLayout) rootView, btnContinue, container,
          coss, edTxtAddress, edTxtName, edTxtPhone, edTxtUserName, binding_headerInclude,
          innerConstraintLayout, line1, scrollableContent, tvEmail, tvFirstName, tvLastName,
          tvPhone);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
