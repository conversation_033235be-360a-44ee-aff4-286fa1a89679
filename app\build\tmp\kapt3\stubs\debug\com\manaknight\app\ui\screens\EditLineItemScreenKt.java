package com.manaknight.app.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000L\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\u001a\u00a8\u0001\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0007\u001a\u00020\u00052\u0006\u0010\b\u001a\u00020\u00052\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00102\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00010\u00122\u0006\u0010\u0013\u001a\u00020\u00052\u0012\u0010\u0014\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u00122\u0012\u0010\u0015\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00122\b\b\u0002\u0010\u0016\u001a\u00020\u00172\b\b\u0002\u0010\u0018\u001a\u00020\u0017H\u0003\u001a\\\u0010\u0019\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0007\u001a\u00020\u00052\u0006\u0010\b\u001a\u00020\u00052\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\u0012\u0010\u0015\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0012H\u0007\u001aN\u0010\u001a\u001a\u00020\u00012\u0006\u0010\u001b\u001a\u00020\u000e2\u0012\u0010\u001c\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00122\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00010\u001e2\u0006\u0010\u000f\u001a\u00020\u00102\u0012\u0010\u001f\u001a\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00010\u0012H\u0007\u001a\u0018\u0010 \u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\t\u001a\u00020\nH\u0003\u001a@\u0010!\u001a\u00020\u00012\u0006\u0010\u0013\u001a\u00020\u00052\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u00032\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\"\u001a\u00020#H\u0002\u00a8\u0006$"}, d2 = {"EditLineItemDetailContent", "", "projectID", "", "customerName", "", "lineItemID", "initialDescription", "initialType", "navController", "Landroidx/navigation/NavController;", "baasViewModel", "Lcom/manaknight/app/viewmodels/BaasViewModel;", "item", "Lcom/manaknight/app/model/remote/profitPro/JobDetailsRespModel;", "description", "Landroidx/compose/ui/text/input/TextFieldValue;", "onDescriptionChange", "Lkotlin/Function1;", "selectedType", "onTypeChange", "deleteItem", "showTopBar", "", "showDetailHeader", "EditLineItemScreen", "EditLineItemScreen2", "jobDetail", "onRemoveClick", "onEditEstimatedByClick", "Lkotlin/Function0;", "onDescriptionChanged", "EditLineItemTopBar", "handleEditSaveAction", "context", "Landroid/content/Context;", "app_debug"})
public final class EditLineItemScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void EditLineItemScreen(int projectID, @org.jetbrains.annotations.NotNull()
    java.lang.String customerName, int lineItemID, @org.jetbrains.annotations.NotNull()
    java.lang.String initialDescription, @org.jetbrains.annotations.NotNull()
    java.lang.String initialType, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.viewmodels.BaasViewModel baasViewModel, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.JobDetailsRespModel item, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> deleteItem) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void EditLineItemDetailContent(int projectID, java.lang.String customerName, int lineItemID, java.lang.String initialDescription, java.lang.String initialType, androidx.navigation.NavController navController, com.manaknight.app.viewmodels.BaasViewModel baasViewModel, com.manaknight.app.model.remote.profitPro.JobDetailsRespModel item, androidx.compose.ui.text.input.TextFieldValue description, kotlin.jvm.functions.Function1<? super androidx.compose.ui.text.input.TextFieldValue, kotlin.Unit> onDescriptionChange, java.lang.String selectedType, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onTypeChange, kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> deleteItem, boolean showTopBar, boolean showDetailHeader) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void EditLineItemScreen2(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.JobDetailsRespModel jobDetail, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onRemoveClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onEditEstimatedByClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.text.input.TextFieldValue description, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super androidx.compose.ui.text.input.TextFieldValue, kotlin.Unit> onDescriptionChanged) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void EditLineItemTopBar(java.lang.String customerName, androidx.navigation.NavController navController) {
    }
    
    private static final void handleEditSaveAction(java.lang.String selectedType, androidx.compose.ui.text.input.TextFieldValue description, int projectID, int lineItemID, com.manaknight.app.model.remote.profitPro.JobDetailsRespModel item, androidx.navigation.NavController navController, android.content.Context context) {
    }
}