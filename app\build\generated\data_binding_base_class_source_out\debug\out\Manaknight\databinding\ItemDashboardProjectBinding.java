// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemDashboardProjectBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView imgProjectMenu;

  @NonNull
  public final TextView txtProjectDate;

  @NonNull
  public final TextView txtProjectId;

  @NonNull
  public final TextView txtProjectName;

  @NonNull
  public final TextView txtProjectProfit;

  private ItemDashboardProjectBinding(@NonNull LinearLayout rootView,
      @NonNull ImageView imgProjectMenu, @NonNull TextView txtProjectDate,
      @NonNull TextView txtProjectId, @NonNull TextView txtProjectName,
      @NonNull TextView txtProjectProfit) {
    this.rootView = rootView;
    this.imgProjectMenu = imgProjectMenu;
    this.txtProjectDate = txtProjectDate;
    this.txtProjectId = txtProjectId;
    this.txtProjectName = txtProjectName;
    this.txtProjectProfit = txtProjectProfit;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemDashboardProjectBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemDashboardProjectBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_dashboard_project, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemDashboardProjectBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.imgProjectMenu;
      ImageView imgProjectMenu = ViewBindings.findChildViewById(rootView, id);
      if (imgProjectMenu == null) {
        break missingId;
      }

      id = R.id.txtProjectDate;
      TextView txtProjectDate = ViewBindings.findChildViewById(rootView, id);
      if (txtProjectDate == null) {
        break missingId;
      }

      id = R.id.txtProjectId;
      TextView txtProjectId = ViewBindings.findChildViewById(rootView, id);
      if (txtProjectId == null) {
        break missingId;
      }

      id = R.id.txtProjectName;
      TextView txtProjectName = ViewBindings.findChildViewById(rootView, id);
      if (txtProjectName == null) {
        break missingId;
      }

      id = R.id.txtProjectProfit;
      TextView txtProjectProfit = ViewBindings.findChildViewById(rootView, id);
      if (txtProjectProfit == null) {
        break missingId;
      }

      return new ItemDashboardProjectBinding((LinearLayout) rootView, imgProjectMenu,
          txtProjectDate, txtProjectId, txtProjectName, txtProjectProfit);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
