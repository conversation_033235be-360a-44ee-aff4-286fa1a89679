// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentSignUpBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView btnPolicy;

  @NonNull
  public final MaterialButton btnSignUp;

  @NonNull
  public final TextView btnSignin;

  @NonNull
  public final TextView btnTerms;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final RelativeLayout container;

  @NonNull
  public final ConstraintLayout coss;

  @NonNull
  public final EditText edTxtCompanyName;

  @NonNull
  public final EditText edTxtFirstName;

  @NonNull
  public final EditText edTxtLastName;

  @NonNull
  public final EditText edTxtPassword;

  @NonNull
  public final EditText edTxtUserName;

  @NonNull
  public final HeaderBinding headerInclude;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final ConstraintLayout innerConstraintLayout;

  @NonNull
  public final LinearLayout line1;

  @NonNull
  public final LinearLayout priv;

  @NonNull
  public final ScrollView scrollableContent;

  @NonNull
  public final TextView tvCompanyName;

  @NonNull
  public final TextView tvEmail;

  @NonNull
  public final TextView tvFirstName;

  @NonNull
  public final TextView tvLastName;

  @NonNull
  public final TextView tvPassword;

  private FragmentSignUpBinding(@NonNull ConstraintLayout rootView, @NonNull TextView btnPolicy,
      @NonNull MaterialButton btnSignUp, @NonNull TextView btnSignin, @NonNull TextView btnTerms,
      @Nullable RelativeLayout container, @NonNull ConstraintLayout coss,
      @NonNull EditText edTxtCompanyName, @NonNull EditText edTxtFirstName,
      @NonNull EditText edTxtLastName, @NonNull EditText edTxtPassword,
      @NonNull EditText edTxtUserName, @NonNull HeaderBinding headerInclude,
      @Nullable ConstraintLayout innerConstraintLayout, @NonNull LinearLayout line1,
      @NonNull LinearLayout priv, @NonNull ScrollView scrollableContent,
      @NonNull TextView tvCompanyName, @NonNull TextView tvEmail, @NonNull TextView tvFirstName,
      @NonNull TextView tvLastName, @NonNull TextView tvPassword) {
    this.rootView = rootView;
    this.btnPolicy = btnPolicy;
    this.btnSignUp = btnSignUp;
    this.btnSignin = btnSignin;
    this.btnTerms = btnTerms;
    this.container = container;
    this.coss = coss;
    this.edTxtCompanyName = edTxtCompanyName;
    this.edTxtFirstName = edTxtFirstName;
    this.edTxtLastName = edTxtLastName;
    this.edTxtPassword = edTxtPassword;
    this.edTxtUserName = edTxtUserName;
    this.headerInclude = headerInclude;
    this.innerConstraintLayout = innerConstraintLayout;
    this.line1 = line1;
    this.priv = priv;
    this.scrollableContent = scrollableContent;
    this.tvCompanyName = tvCompanyName;
    this.tvEmail = tvEmail;
    this.tvFirstName = tvFirstName;
    this.tvLastName = tvLastName;
    this.tvPassword = tvPassword;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentSignUpBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentSignUpBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_sign_up, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentSignUpBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnPolicy;
      TextView btnPolicy = ViewBindings.findChildViewById(rootView, id);
      if (btnPolicy == null) {
        break missingId;
      }

      id = R.id.btnSignUp;
      MaterialButton btnSignUp = ViewBindings.findChildViewById(rootView, id);
      if (btnSignUp == null) {
        break missingId;
      }

      id = R.id.btnSignin;
      TextView btnSignin = ViewBindings.findChildViewById(rootView, id);
      if (btnSignin == null) {
        break missingId;
      }

      id = R.id.btnTerms;
      TextView btnTerms = ViewBindings.findChildViewById(rootView, id);
      if (btnTerms == null) {
        break missingId;
      }

      id = R.id.container;
      RelativeLayout container = ViewBindings.findChildViewById(rootView, id);

      ConstraintLayout coss = (ConstraintLayout) rootView;

      id = R.id.edTxtCompanyName;
      EditText edTxtCompanyName = ViewBindings.findChildViewById(rootView, id);
      if (edTxtCompanyName == null) {
        break missingId;
      }

      id = R.id.edTxtFirstName;
      EditText edTxtFirstName = ViewBindings.findChildViewById(rootView, id);
      if (edTxtFirstName == null) {
        break missingId;
      }

      id = R.id.edTxtLastName;
      EditText edTxtLastName = ViewBindings.findChildViewById(rootView, id);
      if (edTxtLastName == null) {
        break missingId;
      }

      id = R.id.edTxtPassword;
      EditText edTxtPassword = ViewBindings.findChildViewById(rootView, id);
      if (edTxtPassword == null) {
        break missingId;
      }

      id = R.id.edTxtUserName;
      EditText edTxtUserName = ViewBindings.findChildViewById(rootView, id);
      if (edTxtUserName == null) {
        break missingId;
      }

      id = R.id.headerInclude;
      View headerInclude = ViewBindings.findChildViewById(rootView, id);
      if (headerInclude == null) {
        break missingId;
      }
      HeaderBinding binding_headerInclude = HeaderBinding.bind(headerInclude);

      id = R.id.innerConstraintLayout;
      ConstraintLayout innerConstraintLayout = ViewBindings.findChildViewById(rootView, id);

      id = R.id.line1;
      LinearLayout line1 = ViewBindings.findChildViewById(rootView, id);
      if (line1 == null) {
        break missingId;
      }

      id = R.id.priv;
      LinearLayout priv = ViewBindings.findChildViewById(rootView, id);
      if (priv == null) {
        break missingId;
      }

      id = R.id.scrollableContent;
      ScrollView scrollableContent = ViewBindings.findChildViewById(rootView, id);
      if (scrollableContent == null) {
        break missingId;
      }

      id = R.id.tvCompanyName;
      TextView tvCompanyName = ViewBindings.findChildViewById(rootView, id);
      if (tvCompanyName == null) {
        break missingId;
      }

      id = R.id.tvEmail;
      TextView tvEmail = ViewBindings.findChildViewById(rootView, id);
      if (tvEmail == null) {
        break missingId;
      }

      id = R.id.tvFirstName;
      TextView tvFirstName = ViewBindings.findChildViewById(rootView, id);
      if (tvFirstName == null) {
        break missingId;
      }

      id = R.id.tvLastName;
      TextView tvLastName = ViewBindings.findChildViewById(rootView, id);
      if (tvLastName == null) {
        break missingId;
      }

      id = R.id.tvPassword;
      TextView tvPassword = ViewBindings.findChildViewById(rootView, id);
      if (tvPassword == null) {
        break missingId;
      }

      return new FragmentSignUpBinding((ConstraintLayout) rootView, btnPolicy, btnSignUp, btnSignin,
          btnTerms, container, coss, edTxtCompanyName, edTxtFirstName, edTxtLastName, edTxtPassword,
          edTxtUserName, binding_headerInclude, innerConstraintLayout, line1, priv,
          scrollableContent, tvCompanyName, tvEmail, tvFirstName, tvLastName, tvPassword);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
