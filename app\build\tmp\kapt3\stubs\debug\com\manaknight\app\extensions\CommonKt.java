package com.manaknight.app.extensions;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000P\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0005\u001a\u0006\u0010\u0002\u001a\u00020\u0003\u001a\n\u0010\u0004\u001a\u00020\u0003*\u00020\u0005\u001a\n\u0010\u0004\u001a\u00020\u0003*\u00020\u0006\u001a2\u0010\u0007\u001a\u00020\u0003*\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\f\u001a2\u0010\u0007\u001a\u00020\u0003*\u00020\u00062\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\f\u001a\u0012\u0010\u000e\u001a\u00020\u0003*\u00020\u00062\u0006\u0010\u000f\u001a\u00020\t\u001a\u0012\u0010\u0010\u001a\u00020\f*\u00020\u00112\u0006\u0010\u0012\u001a\u00020\f\u001a\f\u0010\u0013\u001a\u00020\t*\u00020\u0014H\u0007\u001a\n\u0010\u0015\u001a\u00020\u0003*\u00020\u0005\u001a\u001a\u0010\u0016\u001a\u00020\u0003*\u00020\u00052\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001a\u001a\n\u0010\u001b\u001a\u00020\u0003*\u00020\u0006\u001a\u0014\u0010\u001c\u001a\u00020\t*\u00020\t2\u0006\u0010\u001d\u001a\u00020\tH\u0007\u001aR\u0010\u001e\u001a\u00020\u0003*\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u001f\u001a\u00020\t2\b\b\u0002\u0010 \u001a\u00020\t2\u000e\b\u0002\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00030\"2\u000e\b\u0002\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00030\"\u001aR\u0010\u001e\u001a\u00020\u0003*\u00020\u00062\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u001f\u001a\u00020\t2\b\b\u0002\u0010 \u001a\u00020\t2\u000e\b\u0002\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00030\"2\u000e\b\u0002\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00030\"\u001a\n\u0010$\u001a\u00020\u0003*\u00020\u0006\u001a\u001a\u0010%\u001a\u00020\u0003*\u00020\u00052\u0006\u0010&\u001a\u00020\t2\u0006\u0010\u0019\u001a\u00020\u001a\"\u0010\u0010\u0000\u001a\u0004\u0018\u00010\u0001X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\'"}, d2 = {"customLoading", "Lcom/saksham/customloadingdialog/CustomLoader;", "hideProgressBar", "", "askForCameraPermission", "Landroidx/appcompat/app/AppCompatActivity;", "Landroidx/fragment/app/Fragment;", "displayNotification", "title", "", "message", "priority", "", "id", "downloadFile", "pdfUrl", "getColor", "Landroid/view/View;", "colorInt", "getDeviceId", "Landroid/app/Activity;", "getNotificationPermission", "observeFCMToken", "preferences", "Lcom/manaknight/app/data/local/AppPreferences;", "viewModel", "Lcom/manaknight/app/viewmodels/BaasViewModel;", "openSettings", "parseDate", "pattern", "showDialog", "positiveButtonText", "negativeButtonText", "onPositiveButtonClick", "Lkotlin/Function0;", "onNegativeButtonClick", "showProgressBar", "updateFcmToken", "token", "app_debug"})
public final class CommonKt {
    @org.jetbrains.annotations.Nullable()
    private static com.saksham.customloadingdialog.CustomLoader customLoading;
    
    public static final void openSettings(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$openSettings) {
    }
    
    @android.annotation.SuppressLint(value = {"NewApi"})
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String parseDate(@org.jetbrains.annotations.NotNull()
    java.lang.String $this$parseDate, @org.jetbrains.annotations.NotNull()
    java.lang.String pattern) {
        return null;
    }
    
    public static final int getColor(@org.jetbrains.annotations.NotNull()
    android.view.View $this$getColor, int colorInt) {
        return 0;
    }
    
    @android.annotation.SuppressLint(value = {"HardwareIds"})
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getDeviceId(@org.jetbrains.annotations.NotNull()
    android.app.Activity $this$getDeviceId) {
        return null;
    }
    
    public static final void showProgressBar(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$showProgressBar) {
    }
    
    public static final void hideProgressBar() {
    }
    
    public static final void downloadFile(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$downloadFile, @org.jetbrains.annotations.NotNull()
    java.lang.String pdfUrl) {
    }
    
    public static final void getNotificationPermission(@org.jetbrains.annotations.NotNull()
    androidx.appcompat.app.AppCompatActivity $this$getNotificationPermission) {
    }
    
    public static final void askForCameraPermission(@org.jetbrains.annotations.NotNull()
    androidx.appcompat.app.AppCompatActivity $this$askForCameraPermission) {
    }
    
    public static final void askForCameraPermission(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$askForCameraPermission) {
    }
    
    public static final void displayNotification(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$displayNotification, @org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String message, int priority, int id) {
    }
    
    public static final void displayNotification(@org.jetbrains.annotations.NotNull()
    androidx.appcompat.app.AppCompatActivity $this$displayNotification, @org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String message, int priority, int id) {
    }
    
    public static final void observeFCMToken(@org.jetbrains.annotations.NotNull()
    androidx.appcompat.app.AppCompatActivity $this$observeFCMToken, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.data.local.AppPreferences preferences, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.viewmodels.BaasViewModel viewModel) {
    }
    
    public static final void updateFcmToken(@org.jetbrains.annotations.NotNull()
    androidx.appcompat.app.AppCompatActivity $this$updateFcmToken, @org.jetbrains.annotations.NotNull()
    java.lang.String token, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.viewmodels.BaasViewModel viewModel) {
    }
    
    public static final void showDialog(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$showDialog, @org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String message, @org.jetbrains.annotations.NotNull()
    java.lang.String positiveButtonText, @org.jetbrains.annotations.NotNull()
    java.lang.String negativeButtonText, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onPositiveButtonClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNegativeButtonClick) {
    }
    
    public static final void showDialog(@org.jetbrains.annotations.NotNull()
    androidx.appcompat.app.AppCompatActivity $this$showDialog, @org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String message, @org.jetbrains.annotations.NotNull()
    java.lang.String positiveButtonText, @org.jetbrains.annotations.NotNull()
    java.lang.String negativeButtonText, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onPositiveButtonClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNegativeButtonClick) {
    }
}