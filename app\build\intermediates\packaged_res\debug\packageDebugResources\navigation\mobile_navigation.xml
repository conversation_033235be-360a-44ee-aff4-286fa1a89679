<?xml version="1.0" encoding="utf-8"?>
  <navigation xmlns:android="http://schemas.android.com/apk/res/android"
      xmlns:app="http://schemas.android.com/apk/res-auto"
      xmlns:tools="http://schemas.android.com/tools"
      android:id="@+id/main_nav"
      app:startDestination="@id/login">

      <fragment
  android:id="@+id/accountview"
  android:name="com.manaknight.app.ui.AccountFragment"
  android:label="fragment_accountview"
  tools:layout="@layout/fragment_accountview">

          <action
              android:id="@+id/action_accountFragment_to_viewProfileFragment"
              app:destination="@id/profileView"/>

          <action
              android:id="@+id/action_accountFragment_to_planAndBillingFragment"
              app:destination="@id/planAndBillingFragment"/>


</fragment>

    <fragment
        android:id="@+id/profileView"
        android:name="com.manaknight.app.ui.ProfieViewFragment"
        android:label="fragment_accountview"
        tools:layout="@layout/fragment_accountview">


    </fragment>

    <!-- Plan and Billing Fragments -->
    <fragment
        android:id="@+id/planAndBillingFragment"
        android:name="com.manaknight.app.ui.fragments.accountview.PlanAndBillingFragment"
        android:label="Plan and Billing">

        <action
            android:id="@+id/action_planAndBillingFragment_to_paymentsFragment"
            app:destination="@id/paymentsFragment"/>

        <action
            android:id="@+id/action_planAndBillingFragment_to_finalCancelFragment"
            app:destination="@id/finalCancelFragment"/>

        <action
            android:id="@+id/action_planAndBillingFragment_to_subscriptionTestFragment"
            app:destination="@id/subscriptionTestFragment"/>
    </fragment>

    <fragment
        android:id="@+id/paymentsFragment"
        android:name="com.manaknight.app.ui.fragments.accountview.PaymentsFragment"
        android:label="Payments">
    </fragment>

    <fragment
        android:id="@+id/finalCancelFragment"
        android:name="com.manaknight.app.ui.fragments.accountview.FinalCancelFragment"
        android:label="Cancel Subscription">
    </fragment>

    <fragment
        android:id="@+id/cancelSubscriptionFragment"
        android:name="com.manaknight.app.ui.fragments.accountview.CancelSubscriptionFragment"
        android:label="Cancel Subscription">
    </fragment>

    <fragment
        android:id="@+id/subscriptionTestFragment"
        android:name="com.manaknight.app.ui.fragments.accountview.SubscriptionTestFragment"
        android:label="Subscription API Test">
    </fragment>

<fragment
  android:id="@+id/costview"
  android:name="com.manaknight.app.ui.CostFragment"
  android:label="fragment_costview"
  tools:layout="@layout/fragment_costview">


</fragment>
<fragment
  android:id="@+id/dashboardview"
  android:name="com.manaknight.app.ui.DashboardviewFragment"
  android:label="fragment_dashboardview"
  tools:layout="@layout/fragment_dashboardview">


</fragment>
<fragment
  android:id="@+id/home"
  android:name="com.manaknight.app.ui.HomeFragment"
  android:label="fragment_home"
  tools:layout="@layout/fragment_home">
    <action
        android:id="@+id/action_homeFragment_to_createEstimationView"
        app:destination="@id/createEstimationView"/>
    <action
        android:id="@+id/action_homeFragment_to_dashboardview"
        app:destination="@id/dashboardview"/>
    <action
        android:id="@+id/action_homeFragment_to_projectsFragment"
        app:destination="@id/projectview"/>

</fragment>

    <fragment
        android:id="@+id/createEstimationView"
        android:name="com.manaknight.app.ui.CreateEstimationFragment"
        android:label="fragment_create_estimation"
        tools:layout="@layout/fragment_create_estimation">

        <action
            android:id="@+id/action_createEstimationView_to_createCustomerView"
            app:destination="@id/createCustomerView"/>

        <action
            android:id="@+id/action_createEstimationView_to_lineItemView"
            app:destination="@id/lineItemView"/>


    </fragment>

    <fragment
        android:id="@+id/createCustomerView"
        android:name="com.manaknight.app.ui.fragments.home.CreateCustomerFragment"
        android:label="fragment_create_customer"
        tools:layout="@layout/fragment_create_customer">


        <argument
            android:name="customerID"
            app:argType="integer"
            android:defaultValue="0"/>

        <argument
            android:name="customerName"
            app:argType="string"
            android:defaultValue=""/>

        <argument
            android:name="customerAddress"
            app:argType="string"
            android:defaultValue=""/>

        <argument
            android:name="customerEmail"
            app:argType="string"
            android:defaultValue=""/>

        <argument
            android:name="customerPhoneNumber"
            app:argType="string"
            android:defaultValue=""/>
    </fragment>

    <fragment
        android:id="@+id/lineItemView"
        android:name="com.manaknight.app.ui.fragments.home.LineItemsFragment"
        android:label="fragment_line_items"
        tools:layout="@layout/fragment_line_items">


        <action
            android:id="@+id/action_lineItemView_to_addlineItemView"
            app:destination="@id/addlineItemView"/>

        <action
            android:id="@+id/action_lineItemView_to_drawsDetailView"
            app:destination="@id/drawsDetailView"/>

        <action
            android:id="@+id/action_lineItemView_to_addMateriallineItemView"
            app:destination="@id/addMateriallineItemView"/>

        <action
            android:id="@+id/action_lineItemView_to_addLinearlineItemView"
            app:destination="@id/addLinearlineItemView"/>

        <argument
            android:name="projectID"
            app:argType="integer"
            android:defaultValue="0"/>

        <argument
            android:name="customerName"
            app:argType="string"
            android:defaultValue=""/>
    </fragment>

<!--    For compose fragments navigation-->
    <fragment
        android:id="@+id/lineItemViewCompose"
        android:name="com.manaknight.app.ui.fragments.LineItemsComposeFragment"
        android:label="fragment_line_items_compose"
        >


        <action
            android:id="@+id/action_lineItemViewCompose_to_previewProjectDetailsFragment"
            app:destination="@id/previewProjectDetailsFragment"/>
        <action
            android:id="@+id/action_lineItemViewCompose_to_editlineItemViewCompose"
            app:destination="@id/editlineItemViewCompose"/>

        <action
            android:id="@+id/action_lineItemViewCompose_to_addlineItemViewCompose"
            app:destination="@id/addlineItemViewCompose"/>

        <action
            android:id="@+id/action_lineItemViewCompose_to_drawsDetailViewCompose"
            app:destination="@id/drawsDetailView"/>

        <action
            android:id="@+id/action_lineItemViewCompose_to_addMateriallineItemViewCompose"
            app:destination="@id/addMateriallineItemViewCompose"/>

        <action
            android:id="@+id/action_lineItemViewCompose_to_addLinearlineItemViewCompose"
            app:destination="@id/addLinearlineItemViewCompose"/>

        <argument
            android:name="projectID"
            app:argType="integer"
            android:defaultValue="0"/>

        <argument
            android:name="customerName"
            app:argType="string"
            android:defaultValue=""/>
    </fragment>

    <fragment
        android:id="@+id/drawsDetailView"
        android:name="com.manaknight.app.ui.fragments.home.DrawsFragment"
        android:label="fragment_draws"
        tools:layout="@layout/fragment_draws">


        <argument
            android:name="projectID"
            app:argType="integer"
            android:defaultValue="0"/>

        <argument
            android:name="customerName"
            app:argType="string"
            android:defaultValue=""/>

    </fragment>

    <fragment
        android:id="@+id/previewProjectDetailsFragment"
        android:name="com.manaknight.app.ui.fragments.PreviewProjectDetailsFragment"
        android:label="Project Details">
        <action
            android:id="@+id/action_previewProjectDetailsFragment_to_InvoiceFragment"
            app:destination="@id/InvoiceFragment" />
        <action
            android:id="@+id/action_previewProjectDetailsFragment_to_lineItemView"
            app:destination="@id/lineItemView" />
        <argument
            android:name="projectID"
            app:argType="integer"
            android:defaultValue="0"/>
    </fragment>


    <fragment
        android:id="@+id/addlineItemView"
        android:name="com.manaknight.app.ui.fragments.home.AddLineItemFragment"
        android:label="fragment_add_line_items"
        tools:layout="@layout/fragment_add_line_items">


        <action
            android:id="@+id/action_addlineItemView_to_addMateriallineItemView"
            app:destination="@id/addMateriallineItemView"/>

        <action
            android:id="@+id/action_addlineItemView_to_addLinearlineItemView"
            app:destination="@id/addLinearlineItemView"/>


        <argument
            android:name="projectID"
            app:argType="integer"
            android:defaultValue="0"/>

        <argument
            android:name="lineItem"
            app:argType="integer"
            android:defaultValue="0"/>
    </fragment>
    <fragment
        android:id="@+id/addlineItemViewCompose"
        android:name="com.manaknight.app.ui.fragments.AddLineItemComposeFragment"
        android:label="fragment_add_line_items"
       >


        <action
            android:id="@+id/action_addlineItemViewCompose_to_addMateriallineItemViewCompose"
            app:destination="@id/addMateriallineItemViewCompose"/>

        <action
            android:id="@+id/action_addlineItemViewCompose_to_addLinearlineItemViewCompose"
            app:destination="@id/addLinearlineItemViewCompose"/>


        <argument
            android:name="projectID"
            app:argType="integer"
            android:defaultValue="0"/>

        <argument
            android:name="isEdit"
            app:argType="integer"
            android:defaultValue="0"/>

        <argument
            android:name="customerName"
            app:argType="string"
            android:defaultValue=" "/>
<!--        <argument-->
<!--            android:name="lineItem"-->
<!--            app:argType="integer"-->
<!--            android:defaultValue="0"/>-->
    </fragment>

    <fragment
        android:id="@+id/editlineItemViewCompose"
        android:name="com.manaknight.app.ui.fragments.EditLineItemComposeFragment"
        android:label="fragment_edit_line_items"
       >


        <action
            android:id="@+id/action_editlineItemViewCompose_to_addMateriallineItemViewCompose"
            app:destination="@id/addMateriallineItemViewCompose"/>

        <action
            android:id="@+id/action_editlineItemViewCompose_to_addLinearlineItemViewCompose"
            app:destination="@id/addLinearlineItemViewCompose"/>


        <argument
            android:name="projectID"
            app:argType="integer"
            android:defaultValue="0"/>
        <argument
            android:name="customerName"
            app:argType="string"
            android:defaultValue=""/>
        <argument
            android:name="lineItemID"
            app:argType="integer"
            android:defaultValue="0"/>
        <argument
            android:name="initialDescription"
            app:argType="string"
            android:defaultValue=""/>
        <argument
            android:name="initialType"
            app:argType="string"
            android:defaultValue=""/>
        <argument
            android:name="item"
            app:argType="com.manaknight.app.model.remote.profitPro.JobDetailsRespModel" />
    </fragment>


    <fragment
        android:id="@+id/addMateriallineItemView"
        android:name="com.manaknight.app.ui.fragments.home.MaterialLineItemFragment"
        android:label="fragment_material_line_item"
        tools:layout="@layout/fragment_material_line_item">


        <argument
            android:name="projectID"
            app:argType="integer"
            android:defaultValue="0"/>

        <argument
            android:name="lineItem"
            app:argType="integer"
            android:defaultValue="0"/>

        <argument
            android:name="lineDescrition"
            app:argType="string"
            android:defaultValue="0"/>

        <argument
            android:name="lineEstimateType"
            app:argType="string"
            android:defaultValue="0"/>

        <argument
            android:name="isEditable"
            app:argType="integer"
            android:defaultValue="0"/>

        <argument
            android:name="labourHours"
            app:argType="string"
            android:defaultValue="0"/>

        <argument
            android:name="itemLineID"
            app:argType="integer"
            android:defaultValue="0"/>


        <argument
            android:name="materialItem"
            app:argType="com.manaknight.app.model.remote.profitPro.MaterialRespListModel2" />

    </fragment>
    <fragment
        android:id="@+id/addMateriallineItemViewCompose"
        android:name="com.manaknight.app.ui.fragments.AddEditMaterialLineItemFragment"
        android:label="fragment_material_line_item_compose"
       >


        <argument
            android:name="projectID"
            app:argType="integer"
            android:defaultValue="0"/>

        <argument
            android:name="lineItem"
            app:argType="integer"
            android:defaultValue="0"/>

        <argument
            android:name="lineDescrition"
            app:argType="string"
            android:defaultValue="0"/>

        <argument
            android:name="lineEstimateType"
            app:argType="string"
            android:defaultValue="0"/>

        <argument
            android:name="isEditable"
            app:argType="integer"
            android:defaultValue="0"/>

        <argument
            android:name="labourHours"
            app:argType="string"
            android:defaultValue="0"/>

        <argument
            android:name="itemLineID"
            app:argType="integer"
            android:defaultValue="0"/>


        <argument
            android:name="materialItem"
            app:argType="com.manaknight.app.model.remote.profitPro.MaterialRespListModel2" />

    </fragment>

    <fragment
        android:id="@+id/addLinearlineItemView"
        android:name="com.manaknight.app.ui.fragments.home.LinealLineItemFragment"
        android:label="fragment_linear_line_item"
        tools:layout="@layout/fragment_linear_line_item">


        <argument
            android:name="projectID"
            app:argType="integer"
            android:defaultValue="0"/>

        <argument
            android:name="lineItem"
            app:argType="integer"
            android:defaultValue="0"/>

        <argument
            android:name="lineDescrition"
            app:argType="string"
            android:defaultValue="0"/>

        <argument
            android:name="lineEstimateType"
            app:argType="string"
            android:defaultValue="0"/>


        <argument
            android:name="isEditable"
            app:argType="integer"
            android:defaultValue="0"/>

        <argument
            android:name="labourHours"
            app:argType="string"
            android:defaultValue="0"/>

        <argument
            android:name="itemLineID"
            app:argType="integer"
            android:defaultValue="0"/>

        <argument
            android:name="materialItem"
            app:argType="com.manaknight.app.model.remote.profitPro.MaterialRespListModel2" />
    </fragment>

    <fragment
        android:id="@+id/addLinearlineItemViewCompose"
        android:name="com.manaknight.app.ui.fragments.AddEditLinealLineItemFragment"
            android:label="fragment_linear_line_item_compose"
        tools:layout="@layout/fragment_linear_line_item">


        <argument
            android:name="projectID"
            app:argType="integer"
            android:defaultValue="0"/>

        <argument
            android:name="lineItem"
            app:argType="integer"
            android:defaultValue="0"/>

        <argument
            android:name="lineDescrition"
            app:argType="string"
            android:defaultValue="0"/>

        <argument
            android:name="lineEstimateType"
            app:argType="string"
            android:defaultValue="0"/>


        <argument
            android:name="isEditable"
            app:argType="integer"
            android:defaultValue="0"/>

        <argument
            android:name="labourHours"
            app:argType="string"
            android:defaultValue="0"/>

        <argument
            android:name="itemLineID"
            app:argType="integer"
            android:defaultValue="0"/>

        <argument
            android:name="materialItem"
            app:argType="com.manaknight.app.model.remote.profitPro.MaterialRespListModel2" />
    </fragment>

<fragment
  android:id="@+id/labortrackingview"
  android:name="com.manaknight.app.ui.LabortrackingViewFragment"
  android:label="fragment_create_estimation"
  tools:layout="@layout/fragment_create_estimation">


</fragment>
<fragment
  android:id="@+id/projectview"
  android:name="com.manaknight.app.ui.ProjectsFragment"
  android:label="fragment_projectview"
  tools:layout="@layout/fragment_projectview">
    <action
        android:id="@+id/action_projectsFragment_to_createEstimationView"
        app:destination="@id/createEstimationView"/>

    <action
        android:id="@+id/action_projectsFragment_to_projectDetailsFragment"
        app:destination="@id/projectDetailsFragment" />
    <action
        android:id="@+id/action_projectsFragment_to_projectChangeOrderFragment"
        app:destination="@id/lineItemViewCompose" />
    <action
        android:id="@+id/action_projectsFragment_to_projectViewEstimateFragment"
        app:destination="@id/previewProjectDetailsFragment" />

    <argument
        android:name="selectedStatus"
        app:argType="string"
        android:defaultValue="All"/>
</fragment>
<fragment
  android:id="@+id/projectDetailsFragment"
  android:name="com.manaknight.app.ui.fragments.ProjectDetailsFragment"
  android:label="fragment_projectDetailsFragmentview">
    <action
        android:id="@+id/action_projectDetailsFragment_to_ProjectTrackingFragment"
        app:destination="@+id/ProjectTrackingFragment" />
    <action
        android:id="@+id/action_projectDetailsFragment_to_projectChangeOrderFragment"
        app:destination="@id/lineItemViewCompose" />


</fragment>


<fragment
  android:id="@+id/InvoiceFragment"
  android:name="com.manaknight.app.ui.fragments.InvoiceFragment"
  android:label="fragment_invoiceview">
    <argument
        android:name="projectID"
        app:argType="integer"
        android:defaultValue="0"/>

    <action
        android:id="@+id/action_InvoiceFragment_to_homeFragment"
        app:destination="@id/home"
        app:popUpTo="@id/home"
        app:popUpToInclusive="true" />

</fragment>
<fragment
  android:id="@+id/ProjectTrackingFragment"
  android:name="com.manaknight.app.ui.fragments.ProjectTrackingFragment"
  android:label="fragment_trackingview">
    <argument
        android:name="projectId"
        app:argType="integer"
        android:defaultValue="0"/>

    <argument
        android:name="customerName"
        app:argType="string"
        android:defaultValue=""/>

    <action
        android:id="@+id/action_ProjectTrackingFragment_to_InvoiceFragment"
        app:destination="@id/InvoiceFragment" />

</fragment>
<fragment
  android:id="@+id/workerview"
  android:name="com.manaknight.app.ui.WorkersFragment"
  android:label="fragment_workerview"
  tools:layout="@layout/fragment_workerview">


</fragment>

  <fragment
  android:id="@+id/login"
  android:name="com.manaknight.app.ui.fragments.LoginFragment"
  android:label="fragment_login"
  tools:layout="@layout/fragment_login">
  <action
      android:id="@+id/action_loginFragment_to_homeFragment"
      app:destination="@id/home"/>
  <action
      android:id="@+id/action_loginFragment_to_signUpFragment"
      app:destination="@id/signUp" />
  <action
      android:id="@+id/action_loginFragment_to_forgetPasswordFragment"
      app:destination="@id/forgetPassword" />

      <action
          android:id="@+id/action_loginFragment_to_subscriptionFragment"
          app:destination="@id/subscripton" />
</fragment>

<fragment
    android:id="@+id/forgetPassword"
    android:name="com.manaknight.app.ui.fragments.ForgetPasswordFragment"
    android:label="fragment_forget_password"
    tools:layout="@layout/fragment_forget_password">

    <action
      android:id="@+id/resetPasswordFragment"
      app:destination="@id/resetPassword" />
</fragment>

<fragment
    android:id="@+id/resetPassword"
    android:name="com.manaknight.app.ui.fragments.ResetPasswordFragment"
    android:label="fragment_reset_password"
    tools:layout="@layout/fragment_reset_password">

    <action
      android:id="@+id/loginFragment"
      app:destination="@id/login" />
</fragment>

<fragment
  android:id="@+id/signUp"
  android:name="com.manaknight.app.ui.fragments.SignUpFragment"
  android:label="fragment_sign_up"
  tools:layout="@layout/fragment_sign_up">
  <action
      android:id="@+id/action_signUpFragment_to_homeFragment"
      app:destination="@id/home" />
  <action
      android:id="@+id/action_signUpFragment_to_loginFragment"
      app:destination="@id/login" />

    <action
        android:id="@+id/action_signUpFragment_to_subscriptionFragment"
        app:destination="@id/subscripton" />
</fragment>

    <fragment
        android:id="@+id/subscripton"
        android:name="com.manaknight.app.ui.SubscriptionFragment"
        android:label="fragment_reset_password"
        tools:layout="@layout/fragment_reset_password">

        <action
            android:id="@+id/action_subscriptonFragment_to_companySetupFragment"
            app:destination="@id/companysetup" />

    </fragment>

    <fragment
        android:id="@+id/companysetup"
        android:name="com.manaknight.app.ui.CompanySetupFragment"
        android:label="fragment_companysetup"
        tools:layout="@layout/fragment_companysetup">



        <action
            android:id="@+id/action_companysetupFragment_to_materialsetupFragment"
            app:destination="@id/materialsetup" />
    </fragment>

    <fragment
        android:id="@+id/materialsetup"
        android:name="com.manaknight.app.ui.MaterialSetupFragment"
        android:label="fragment_materialsetup"
        tools:layout="@layout/fragment_materialsetup">


        <action
            android:id="@+id/action_materialsetupFragment_tolinealsetupFragment"
            app:destination="@id/linealsetup" />
    </fragment>



    <fragment
        android:id="@+id/linealsetup"
        android:name="com.manaknight.app.ui.LinealSetupFragment"
        android:label="fragment_linealsetup"
        tools:layout="@layout/fragment_linealsetup">



        <action
            android:id="@+id/action_linealsetupFragment_tosquaresetupFragment"
            app:destination="@id/squaresetup" />
    </fragment>

    <fragment
        android:id="@+id/squaresetup"
        android:name="com.manaknight.app.ui.SquareSetupFragment"
        android:label="fragment_squaresetup"
        tools:layout="@layout/fragment_squresetup">


        <action
            android:id="@+id/action_squaresetupFragment_tofinishsetupFragment"
            app:destination="@id/finishsetup" />

    </fragment>

    <fragment
        android:id="@+id/finishsetup"
        android:name="com.manaknight.app.ui.CompleteSetupFragment"
        android:label="fragment_finishsetup"
        tools:layout="@layout/fragment_completesetup">

        <action
            android:id="@+id/action_completeFragment_to_HomeFragment"
            app:destination="@id/home"/>


    </fragment>


    <fragment
      android:id="@+id/profile"
      android:name="com.manaknight.app.ui.fragments.ProfileFragment"
      android:label="fragment_my_account"
      tools:layout="@layout/fragment_profile">

    <action
      android:id="@+id/action_profileFragment_to_profileEditFragment"
      app:destination="@id/profileEdit" />

  </fragment>


  <fragment
        android:id="@+id/profileEdit"
        android:name="com.manaknight.app.ui.fragments.ProfileEditFragment"
        android:label="fragment_profile_edit"
        tools:layout="@layout/fragment_profile_edit">

    <action
        android:id="@+id/action_profileEditFragment_to_homeFragment"
        app:destination="@id/home" />

    <action
        android:id="@+id/action_profileEditFragment_to_loginFragment"
        app:destination="@id/login" />

    </fragment>

  <fragment
  android:id="@+id/splash"
  android:name="com.manaknight.app.ui.fragments.SplashFragment"
  android:label="fragment_splash"
  tools:layout="@layout/fragment_splash">
  <action
      android:id="@+id/action_splashFragment_to_loginFragment"
      app:destination="@id/login" />
  <action
      android:id="@+id/action_splashFragment_to_signUpFragment"
      app:destination="@id/signUp" />
  <action
      android:id="@+id/action_splashFragment_to_homeFragment"
      app:destination="@id/home" />
</fragment>




      <!-- ALERTS-->
    <fragment
        android:id="@+id/alerts"
        android:name="com.manaknight.app.ui.fragments.alerts.AlertsFragment"
        android:label="fragment_alerts"
        tools:layout="@layout/fragment_alerts"></fragment>

  </navigation>
