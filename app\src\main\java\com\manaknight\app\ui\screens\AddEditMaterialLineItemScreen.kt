package com.manaknight.app.ui.screens


import Manaknight.R
import android.annotation.SuppressLint
import android.widget.Toast
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Save
import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.manaknight.app.model.remote.profitPro.CreateLineEntriesReqModel
import com.manaknight.app.model.remote.profitPro.CreateLineItemReqModel
import com.manaknight.app.model.remote.profitPro.MaterialRespListModel
import com.manaknight.app.model.remote.profitPro.UpdateLineEntriesReqModel
import com.manaknight.app.model.remote.profitPro.UpdateLineItemReqModel
//import com.manaknight.app.ui.components.MaterialListItem

import androidx.compose.material.*
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
//import com.manaknight.app.model.remote.profitPro.ProjectDetailsRespModel
import com.manaknight.app.network.Status
import com.manaknight.app.viewmodels.BaasViewModel

import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.material3.*
import androidx.compose.ui.unit.sp
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.MoreVert
import com.manaknight.app.viewmodels.*
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.navigation.NavController
import com.manaknight.app.model.remote.TrackingLabourResponse
import com.manaknight.app.model.remote.TrackingMaterialResponse
import com.manaknight.app.model.remote.profitPro.Draws
import com.manaknight.app.model.remote.profitPro.Labor
import com.manaknight.app.model.remote.profitPro.MaterialItem
import com.manaknight.app.model.remote.profitPro.Materials
import com.manaknight.app.model.remote.profitPro.ProjectTrackingResponse
import com.manaknight.app.network.Resource
import com.manaknight.app.utils.CustomUtils.formatDate
import androidx.compose.ui.window.Dialog
import androidx.navigation.fragment.findNavController
import com.manaknight.app.extensions.snackBarForDialog

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.*
import androidx.compose.ui.Alignment.Companion.CenterVertically
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.*
import com.manaknight.app.model.remote.ProjectResponseModel
import com.manaknight.app.model.remote.list
import com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel
import com.manaknight.app.model.remote.profitPro.MaterialRespListModel2
import com.manaknight.app.ui.fragments.LineItemsComposeFragmentDirections
//import androidx.compose.ui.graphics.BorderStroke
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import android.os.Bundle
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.*
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.unit.dp
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.*
import androidx.compose.ui.unit.sp
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.MoreVert
import com.manaknight.app.viewmodels.*
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import com.manaknight.app.utils.CustomUtils.formatDate
import androidx.compose.ui.window.Dialog
import androidx.navigation.fragment.findNavController
import com.manaknight.app.extensions.snackBarForDialog
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.withStyle
//import androidx.compose.ui.text.input.KeyboardOptions
import com.manaknight.app.data.local.AppPreferences
import org.koin.android.ext.android.inject
import com.manaknight.app.ui.components.CustomCheckbox
import com.manaknight.app.ui.components.LineItemMasterDetailLayout
import com.manaknight.app.ui.components.LineItemsMasterPanel
import com.manaknight.app.ui.utils.isTabletLayout
import com.manaknight.app.ui.components.ResponsiveSheetContainer


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddEditMaterialLineItemScreen(
    projectID: Int,
    customerName: String,
    isEditable: Int,
    lineItemNumber: Int,
    lineDescription: String,
    lineEstimateType: String,
    labourHours: String?,
    materialItem: MaterialRespListModel2?,
    itemLineID: Int?,
    navController: NavController,
    baasViewModel: BaasViewModel,
    userId:Int
) {
    val laborHoursState = remember { mutableStateOf(labourHours ?: "") }
    val materialListState = remember { mutableStateOf<List<MaterialRespListModel>>(emptyList()) }
    val context = LocalContext.current
    var showAddNewMaterialDialog by remember { mutableStateOf(false) }
    val selectedLinearItemId = remember { mutableStateOf<Int?>(null) }
    val sheetState = rememberModalBottomSheetState(
        skipPartiallyExpanded = true // Ensures it fully expands instead of partially
    )

    val defaultMaterialsResource by baasViewModel.materialListResource.observeAsState()
    val materialItems by baasViewModel.materialItems.observeAsState(emptyList())

    LaunchedEffect(Unit) {
        baasViewModel.fetchDefaultMaterialList(filter = "user_id,cs,${userId}")
    }

//    val defaultMaterialsResource by baasViewModel.fetchDefaultMaterialList(filter = "user_id,cs,${userId}").observeAsState()

    LaunchedEffect(defaultMaterialsResource) {
        if (defaultMaterialsResource?.status == Status.SUCCESS) {
            materialListState.value = defaultMaterialsResource?.data?.list ?: emptyList()
            if (isEditable == 1 && materialItem?.list != null) {
                val updatedList = materialListState.value.map { defaultMaterial ->
                    val matchingItem = materialItem.list.find { it.name == defaultMaterial.name }
                    if (matchingItem != null) {
                        defaultMaterial.copy(
                            isSelected = true,
                            units = matchingItem.quantity,
                            selectedID = matchingItem.id
                        )
                    } else {
                        defaultMaterial
                    }
                }
                materialListState.value = updatedList
            }
        } else if (defaultMaterialsResource?.status == Status.ERROR) {
            Toast.makeText(context, "Error loading materials: ${defaultMaterialsResource?.message}", Toast.LENGTH_SHORT).show()
        }
    }

    if (isTabletLayout()) {
        // Tablet layout with full-width top bar
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // Full-width top bar for tablet
            AddEditMaterialLineItemTopBar(
                customerName = customerName,
                isEditable = isEditable,
                navController = navController
            )

            // Master-detail layout below the top bar
            LineItemMasterDetailLayout(
                masterContent = {
                    LineItemsMasterPanel(
                        projectID = projectID,
                        customerName = customerName,
                        baasViewModel = baasViewModel,
                        navController = navController,
                        onAddLineItem = {
                            // Navigate to add line item screen
                            navController.popBackStack()
                        },
                        onDeleteItem = { lineItemId ->
                            // Handle delete if needed
                        }
                    )
                },
                detailContent = {
                    AddEditMaterialLineItemDetailContent(
                        projectID = projectID,
                        customerName = customerName,
                        isEditable = isEditable,
                        lineItemNumber = lineItemNumber,
                        lineDescription = lineDescription,
                        lineEstimateType = lineEstimateType,
                        labourHours = labourHours,
                        materialItem = materialItem,
                        itemLineID = itemLineID,
                        navController = navController,
                        baasViewModel = baasViewModel,
                        userId = userId,
                        laborHoursState = laborHoursState,
                        materialListState = materialListState,
                        showAddNewMaterialDialog = showAddNewMaterialDialog,
                        onShowAddNewMaterialDialogChange = { showAddNewMaterialDialog = it },
                        showTopBar = false, // Hide the detail's top bar on tablet
                        showDetailHeader = true // Show the detail panel header with buttons
                    )
                }
            )
        }
    } else {
        // Mobile layout - keep existing implementation
        LineItemMasterDetailLayout(
            masterContent = {
                // No master content on mobile
            },
            detailContent = {
                AddEditMaterialLineItemDetailContent(
                    projectID = projectID,
                    customerName = customerName,
                    isEditable = isEditable,
                    lineItemNumber = lineItemNumber,
                    lineDescription = lineDescription,
                    lineEstimateType = lineEstimateType,
                    labourHours = labourHours,
                    materialItem = materialItem,
                    itemLineID = itemLineID,
                    navController = navController,
                    baasViewModel = baasViewModel,
                    userId = userId,
                    laborHoursState = laborHoursState,
                    materialListState = materialListState,
                    showAddNewMaterialDialog = showAddNewMaterialDialog,
                    onShowAddNewMaterialDialogChange = { showAddNewMaterialDialog = it },
                    showTopBar = true, // Show the detail's top bar on mobile
                    showDetailHeader = false // Don't show detail header on mobile
                )
            }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AddEditMaterialLineItemDetailContent(
    projectID: Int,
    customerName: String,
    isEditable: Int,
    lineItemNumber: Int,
    lineDescription: String,
    lineEstimateType: String,
    labourHours: String?,
    materialItem: MaterialRespListModel2?,
    itemLineID: Int?,
    navController: NavController,
    baasViewModel: BaasViewModel,
    userId: Int,
    laborHoursState: MutableState<String>,
    materialListState: MutableState<List<MaterialRespListModel>>,
    showAddNewMaterialDialog: Boolean,
    onShowAddNewMaterialDialogChange: (Boolean) -> Unit,
    showTopBar: Boolean = true,
    showDetailHeader: Boolean = false
) {
    val context = LocalContext.current
    val sheetState = rememberModalBottomSheetState(
        skipPartiallyExpanded = true
    )

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Detail header for tablet (with Save and Cancel buttons)
        if (showDetailHeader) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = CenterVertically
            ) {
                Text(
                    text = if (isEditable == 1) "Update Line Item" else "Add Material Line Item",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    color = colorResource(R.color.profit_black)
                )

                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Button(
                        shape = RoundedCornerShape(4.dp),
                        colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.profit_blue)),
                        contentPadding = PaddingValues(horizontal = 12.dp, vertical = 6.dp),
                        onClick = {
                            val selectedMaterials = materialListState.value.filter { it.isSelected == true }
                            saveMaterialLineItem(
                                isEditable = isEditable,
                                projectID = projectID,
                                lineDescription = lineDescription,
                                lineEstimateType = lineEstimateType,
                                laborHours = laborHoursState.value.toIntOrNull() ?: 0,
                                selectedMaterials = selectedMaterials,
                                itemLineID = itemLineID,
                                baasViewModel = baasViewModel,
                                navController = navController
                            )
                        }
                    ) {
                        Text(
                            text = "Save",
                            fontSize = 16.sp,
                            color = Color.White
                        )
                    }

                    Button(
                        shape = RoundedCornerShape(4.dp),
                        colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
                        border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
                        contentPadding = PaddingValues(horizontal = 12.dp, vertical = 6.dp),
                        onClick = { navController.popBackStack() }
                    ) {
                        Text(
                            text = "Cancel",
                            fontSize = 16.sp,
                            color = colorResource(R.color.profit_blue)
                        )
                    }
                }
            }
        }

        // Main content with optional top bar
        Scaffold(
            containerColor = Color.White,
            topBar = {
                if (showTopBar) {
                    CenterAlignedTopAppBar(
                        title = {
                            Text(
                                if (isEditable == 1) "Update Line Item"
                                else "New Line Item #${lineItemNumber}",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium,
                                color = colorResource(R.color.profit_black)
                            )
                        },
                        navigationIcon = {
                            IconButton(onClick = { navController.popBackStack() }) {
                                Icon(Icons.Filled.ArrowBack, contentDescription = "Back")
                            }
                        },
                        actions = {
                            Button(
                                onClick = {
                                    // Filter the materialListState for selected items.
                                    val selectedMaterials = materialListState.value.filter { it.isSelected == true }
                                    saveMaterialLineItem(
                                        isEditable = isEditable,
                                        projectID = projectID,
                                        lineDescription = lineDescription,
                                        lineEstimateType = lineEstimateType,
                                        laborHours = laborHoursState.value.toIntOrNull() ?: 0,
                                        selectedMaterials = selectedMaterials,
                                        itemLineID = itemLineID,
                                        baasViewModel = baasViewModel,
                                        navController = navController
                                    )
                                },
                                shape = RoundedCornerShape(4.dp),
                                colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
                                border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
                                contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp),
                                modifier = Modifier
                                    .height(28.dp) // Adjust height as needed
                            ) {
                                Text(
                                    "Save", fontSize = 16.sp,
                                    color = colorResource(R.color.profit_blue)
                                )
                            }

                        },
                        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                            containerColor = Color.White
                        )
                    )
                }
            },
            content = { paddingValues ->
                Column(
                    modifier = Modifier
                        .padding(paddingValues)
                        .padding(16.dp)
                        .fillMaxSize()
                ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = if (lineEstimateType == "square_foot") "Square Foot Costs" else "Linear Foot Costs",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = colorResource(id = R.color.gray)
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Button(onClick = { onShowAddNewMaterialDialogChange(true) },shape = RoundedCornerShape(4.dp),
                        colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
                        border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
                        contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp),
                        modifier = Modifier
                            .height(28.dp) // Adjust height as needed)
                    ) {
                        Text("Add New Cost",fontSize = 16.sp,
                            color = colorResource(R.color.profit_blue))
                    }
                }
                Spacer(modifier = Modifier.height(16.dp))

                Text(
                    "Labor hours",
                    color = colorResource(id = R.color.profit_black),
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(bottom = 4.dp)
                )
                BasicTextField(
                    value = laborHoursState.value,
                    onValueChange = { laborHoursState.value = it },
                    singleLine = true,
                    textStyle = LocalTextStyle.current.copy(color = Color.Black),
                    modifier = Modifier
                        .fillMaxWidth()
                        .border(1.dp, Color.Gray, shape = RoundedCornerShape(8.dp))
                        .padding(horizontal = 12.dp, vertical = 10.dp),
                    decorationBox = { innerTextField ->
                        Column {

                            innerTextField()
                        }
                    }
                )

                Spacer(modifier = Modifier.height(16.dp))
                LazyColumn ( verticalArrangement = Arrangement.spacedBy(12.dp)){
                    items(materialListState.value) { material ->
                        MaterialListItem(
                            material = material,
                            onItemSelected = { isSelected ->
                                // Update the isSelected property of the specific material
                                materialListState.value = materialListState.value.map {
                                    if (it.id == material.id) {
                                        it.copy(isSelected = isSelected)
                                    } else {
                                        it
                                    }
                                }
                            },
                            onUnitsChanged = { units ->
                                // Update the units property of the specific material
                                materialListState.value = materialListState.value.map {
                                    if (it.id == material.id) {
                                        it.copy(units = units.toIntOrNull())
                                    } else {
                                        it
                                    }
                                }
                            },

                        )
                    }
                }
                }
            }
        )

        if (showAddNewMaterialDialog) {
        var materialName by remember { mutableStateOf("") }
        var unitCost by remember { mutableStateOf("") }
        var shouldAddToDefaultList by remember { mutableStateOf(true) }

        ResponsiveSheetContainer(
            showSheet = showAddNewMaterialDialog,
            onDismiss = { onShowAddNewMaterialDialogChange(false) },
            sheetState = sheetState,
            headerContent = {
                AddNewMaterialDialogHeader(
                    materialName = materialName,
                    unitCost = unitCost,
                    onDismiss = { onShowAddNewMaterialDialogChange(false) },
                    onSave = { _, _ ->
                        // Refresh the material list after a new material is added
                        baasViewModel.getDefaultMaterialList(filter = "user_id,cs,${userId}")
                        onShowAddNewMaterialDialogChange(false)
                    },
                    baasViewModel = baasViewModel,
                    userId = userId
                )
            },
            content = {
                AddNewMaterialDialogContentBody(
                    materialName = materialName,
                    onMaterialNameChange = { materialName = it },
                    unitCost = unitCost,
                    onUnitCostChange = { unitCost = it },
                    shouldAddToDefaultList = shouldAddToDefaultList,
                    onShouldAddToDefaultListChange = { shouldAddToDefaultList = it }
                )
            }
        )
    }
    }
}


@Composable
fun MaterialListItem(
    material: MaterialRespListModel,
    onItemSelected: (Boolean) -> Unit,
    onUnitsChanged: (String) -> Unit,
) {
    Column(modifier = Modifier.fillMaxWidth()
        .border(
            width = 1.dp,
            color = colorResource(R.color.gray),
            shape = RoundedCornerShape(8.dp)
        ).padding(8.dp)) {
        Row(
            verticalAlignment = CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 4.dp)
        ) {
            CustomCheckbox(
                checked = material.isSelected ?: false,
                onCheckedChange = onItemSelected
            )
            Spacer(modifier = Modifier.width(8.dp))
            Column(
                modifier = Modifier.weight(1f)
            ) {


                Text(
                    text = material.name ?: "No Name",
//                    modifier = Modifier.weight(1f),
                    color = colorResource(R.color.profit_black),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                Row(
//                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
//                        .fillMaxWidth()
//                        .padding(vertical = 4.dp)
                ) {
                    Text(text = "$1/unit", modifier = Modifier.weight(1f))
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(text = "Total:  ${material.cost}", modifier = Modifier.weight(1f))

                }


            }
        }

        // ✅ Only show input when this item is selected
        if (material.isSelected == true) {
            Column(modifier = Modifier.padding(start = 40.dp)) {
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = buildAnnotatedString {
                        withStyle(style = SpanStyle(color = Color.Red)) { append("*") }
                        withStyle(style = SpanStyle(color = colorResource(R.color.black))) { append("Units") }
                    },
                    fontSize = 14.sp,
                    color = colorResource(R.color.profit_black),
                    fontWeight = FontWeight.Medium
                )

                Spacer(modifier = Modifier.height(4.dp))

                BasicTextField(
                    value = material.units?.toString() ?: "",
                    onValueChange = onUnitsChanged,
                    singleLine = true,
                    textStyle = LocalTextStyle.current.copy(color = Color.Black),
                    modifier = Modifier
                        .fillMaxWidth()
                        .defaultMinSize(minHeight = 40.dp)
                        .border(1.dp, Color.Gray, shape = RoundedCornerShape(8.dp))
                        .padding(horizontal = 12.dp, vertical = 10.dp),
                    decorationBox = { innerTextField ->
                        if ((material.units?.toString() ?: "").isEmpty()) {
                            Text(
                                "Units",
                                color = Color.Gray,
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                        innerTextField()
                    }
                )
            }
        }
    }
}


fun saveMaterialLineItem(
    isEditable: Int,
    projectID: Int,
    lineDescription: String,
    lineEstimateType: String,
    laborHours: Int,
    selectedMaterials: List<MaterialRespListModel>,
    itemLineID: Int?,
    baasViewModel: BaasViewModel,
    navController: NavController
) {
    val lineEntries = selectedMaterials.map { material ->
        if (isEditable == 1 && material.selectedID != null) {
            UpdateLineEntriesReqModel(
                quantity = material.units ?: 0,
                item_id = material.id ?: 0,
                id = material.selectedID,
                name = material.name ?: ""
            )
        } else {
            CreateLineEntriesReqModel(
                quantity = material.units ?: 0,
                item_id = material.id ?: 0
            )
        }
    }

    if (isEditable == 1 && itemLineID != null) {
        baasViewModel.updateLineItem(
            itemLineID,
            UpdateLineItemReqModel(
                lineDescription,
                lineEstimateType,
                projectID,
                laborHours,
                lineEntries as List<UpdateLineEntriesReqModel>
            )
        ).observeForever { resource ->
            if (resource.status == Status.SUCCESS) {
                navController.popBackStack()
                navController.popBackStack()
            } else if (resource.status == Status.ERROR) {
                Toast.makeText(navController.context, resource.message, Toast.LENGTH_SHORT).show()
            }
        }
    } else {
        baasViewModel.addLineItem(
            CreateLineItemReqModel(
                lineDescription,
                lineEstimateType,
                projectID,
                laborHours,
                lineEntries as List<CreateLineEntriesReqModel>
            )
        ).observeForever { resource ->
            if (resource.status == Status.SUCCESS) {
                navController.popBackStack()
                navController.popBackStack()
            } else if (resource.status == Status.ERROR) {
                Toast.makeText(navController.context, resource.message, Toast.LENGTH_SHORT).show()
            }
        }
    }
}

@SuppressLint("SuspiciousIndentation")
@Composable
fun AddNewMaterialDialog(
    onDismissRequest: () -> Unit,
    onMaterialAdded: (String, String) -> Unit,
    baasViewModel: BaasViewModel,
    userId:Int
) {
    var materialName by remember { mutableStateOf("") }
    var unitCost by remember { mutableStateOf("") }
    var shouldAddToDefaultList by remember { mutableStateOf(true) } // Default to true as in the layout


        Column(
            modifier = Modifier
                .background(colorResource(id = R.color.white), shape = RoundedCornerShape(8.dp))
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = CenterVertically
            ) {
                IconButton(onClick = onDismissRequest) {
                    Icon(Icons.Filled.Close, contentDescription = "Close")
                }
                Text(
                    text = "New Material",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = colorResource(id = R.color.profit_black)
                )
                Button (
                    shape = RoundedCornerShape(4.dp),
                    colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
                    border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
                    contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp),
                    modifier = Modifier
                        .padding(2.dp)
                        .height(28.dp)
                        .wrapContentWidth()
                        .defaultMinSize(minWidth = 0.dp),
                    onClick = {
                        if (materialName.isNotBlank() && unitCost.isNotBlank()) {
                            // Call ViewModel to add new material
                            baasViewModel.createDefaultMaterial(
                                userId = userId,
                                cost = unitCost.toInt() ?: 0,
                                name = materialName
                            ).observeForever { resource ->
                                if (resource.status == Status.SUCCESS) {
                                    onMaterialAdded(materialName, unitCost) // Notify the screen to refresh the list
                                    onDismissRequest()
                                } else if (resource.status == Status.ERROR) {
                                    // Show error message
                                    // TODO: Implement proper error handling (e.g., using a Snackbar)
                                }
                            }
                        }
                    },

                ) {
                    Text(
                        text = "Save",
                        fontSize = 16.sp,
                        color = colorResource(R.color.profit_blue)
                    )
                }

            }
            Spacer(modifier = Modifier.height(16.dp))
            OutlinedTextField(
                value = materialName,
                onValueChange = { materialName = it },
                label = { Text("Material Name") },
                modifier = Modifier.fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(8.dp))
            OutlinedTextField(
                value = unitCost,
                onValueChange = { unitCost = it },
                label = { Text("Unit Cost") },
                modifier = Modifier.fillMaxWidth(),
                leadingIcon = { Text("$") },
//                keyboardOptions = androidx.compose.ui.text.input.KeyboardOptions(keyboardType = KeyboardType.NumberDecimal)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Row(
                verticalAlignment = CenterVertically,

            ) {
                CustomCheckbox(
                    checked = shouldAddToDefaultList,
                    onCheckedChange = { shouldAddToDefaultList = it }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "Add to default material list",
                    fontSize = 14.sp,
                    color = colorResource(id = R.color.gray)
                )
            }
            Spacer(modifier = Modifier.height(16.dp))

        }

}

@Composable
fun AddNewMaterialDialogHeader(
    materialName: String,
    unitCost: String,
    onDismiss: () -> Unit,
    onSave: (String, String) -> Unit,
    baasViewModel: BaasViewModel,
    userId: Int
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = CenterVertically
    ) {
        IconButton(onClick = onDismiss) {
            Icon(Icons.Filled.Close, contentDescription = "Close")
        }
        Text(
            text = "New Material",
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = colorResource(id = R.color.profit_black)
        )
        Button(
            shape = RoundedCornerShape(4.dp),
            colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
            border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
            contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp),
            modifier = Modifier
                .padding(2.dp)
                .height(28.dp)
                .wrapContentWidth()
                .defaultMinSize(minWidth = 0.dp),
            onClick = {
                if (materialName.isNotBlank() && unitCost.isNotBlank()) {
                    // Call ViewModel to add new material
                    baasViewModel.createDefaultMaterial(
                        userId = userId,
                        cost = unitCost.toInt() ?: 0,
                        name = materialName
                    ).observeForever { resource ->
                        if (resource.status == Status.SUCCESS) {
                            onSave(materialName, unitCost) // Notify the screen to refresh the list
                            onDismiss()
                        } else if (resource.status == Status.ERROR) {
                            // Show error message
                            // TODO: Implement proper error handling (e.g., using a Snackbar)
                        }
                    }
                }
            }
        ) {
            Text(
                text = "Save",
                fontSize = 16.sp,
                color = colorResource(R.color.profit_blue)
            )
        }
    }
}

@Composable
fun AddNewMaterialDialogContentBody(
    materialName: String,
    onMaterialNameChange: (String) -> Unit,
    unitCost: String,
    onUnitCostChange: (String) -> Unit,
    shouldAddToDefaultList: Boolean,
    onShouldAddToDefaultListChange: (Boolean) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        OutlinedTextField(
            value = materialName,
            onValueChange = onMaterialNameChange,
            label = { Text("Material Name") },
            modifier = Modifier.fillMaxWidth()
        )
        Spacer(modifier = Modifier.height(8.dp))
        OutlinedTextField(
            value = unitCost,
            onValueChange = onUnitCostChange,
            label = { Text("Unit Cost") },
            modifier = Modifier.fillMaxWidth(),
            leadingIcon = { Text("$") }
        )
        Spacer(modifier = Modifier.height(16.dp))
        Row(
            verticalAlignment = CenterVertically
        ) {
            CustomCheckbox(
                checked = shouldAddToDefaultList,
                onCheckedChange = onShouldAddToDefaultListChange
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "Add to default material list",
                fontSize = 14.sp,
                color = colorResource(id = R.color.gray)
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AddEditMaterialLineItemTopBar(
    customerName: String,
    isEditable: Int,
    navController: NavController
) {
    CenterAlignedTopAppBar(
        title = {
            Text(
                text = customerName,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = colorResource(R.color.profit_black)
            )
        },
        navigationIcon = {
            IconButton(onClick = { navController.popBackStack() }) {
                Icon(Icons.Filled.ArrowBack, contentDescription = "Back")
            }
        },
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
            containerColor = Color.White
        ),
        modifier = Modifier.fillMaxWidth()
    )
}

// ==================== PREVIEW SECTION ====================

@Preview(name = "Add Material Line Item - Mobile", showBackground = true)
@Composable
fun AddEditMaterialLineItemScreenMobilePreview() {
    val mockBaasViewModel = remember {
        object : BaasViewModel() {
            override fun fetchDefaultMaterialList(filter: String) {
                // Mock implementation
            }
        }
    }
    
    val mockNavController = remember {
        object : NavController(LocalContext.current) {
            override fun popBackStack(): Boolean = true
        }
    }
    
    val sampleMaterials = listOf(
        MaterialRespListModel(
            id = 1,
            name = "Lumber - 2x4",
            cost = 8.50.toString(),
            isSelected = true,
            units = 10,
            selectedID = TODO(),
            user_id = TODO()
        ),
        MaterialRespListModel(
            id = 2,
            name = "Drywall Sheets",
            cost = 12.75,
            isSelected = false,
            units = null
        ),
        MaterialRespListModel(
            id = 3,
            name = "Paint - Interior",
            cost = 25.00,
            isSelected = true,
            units = 5
        ),
        MaterialRespListModel(
            id = 4,
            name = "Nails - 3 inch",
            cost = 5.25,
            isSelected = false,
            units = null
        )
    )
    
    AddEditMaterialLineItemScreen(
        projectID = 123,
        customerName = "John Smith",
        isEditable = 0, // Add mode
        lineItemNumber = 5,
        lineDescription = "Kitchen Renovation - Materials",
        lineEstimateType = "square_foot",
        labourHours = "8",
        materialItem = null,
        itemLineID = null,
        navController = mockNavController,
        baasViewModel = mockBaasViewModel,
        userId = 456
    )
}

@Preview(name = "Edit Material Line Item - Mobile", showBackground = true)
@Composable
fun EditMaterialLineItemScreenMobilePreview() {
    val mockBaasViewModel = remember {
        object : BaasViewModel() {
            override fun fetchDefaultMaterialList(filter: String) {
                // Mock implementation
            }
        }
    }
    
    val mockNavController = remember {
        object : NavController(LocalContext.current) {
            override fun popBackStack(): Boolean = true
        }
    }
    
    val existingMaterialItem = MaterialRespListModel2(
        list = listOf(
            MaterialRespListModel(
                id = 1,
                name = "Lumber - 2x4",
                cost = 8.50,
                isSelected = true,
                units = 15,
                selectedID = 101
            ),
            MaterialRespListModel(
                id = 3,
                name = "Paint - Interior",
                cost = 25.00,
                isSelected = true,
                units = 8,
                selectedID = 103
            )
        )
    )
    
    AddEditMaterialLineItemScreen(
        projectID = 123,
        customerName = "John Smith",
        isEditable = 1, // Edit mode
        lineItemNumber = 5,
        lineDescription = "Kitchen Renovation - Materials",
        lineEstimateType = "linear_foot",
        labourHours = "12",
        materialItem = existingMaterialItem,
        itemLineID = 789,
        navController = mockNavController,
        baasViewModel = mockBaasViewModel,
        userId = 456
    )
}

@Preview(name = "Material List Item - Selected", showBackground = true)
@Composable
fun MaterialListItemSelectedPreview() {
    val material = MaterialRespListModel(
        id = 1,
        name = "Lumber - 2x4 Premium Grade",
        cost = 12.50,
        isSelected = true,
        units = 25
    )
    
    MaterialListItem(
        material = material,
        onItemSelected = { },
        onUnitsChanged = { }
    )
}

@Preview(name = "Material List Item - Unselected", showBackground = true)
@Composable
fun MaterialListItemUnselectedPreview() {
    val material = MaterialRespListModel(
        id = 2,
        name = "Drywall Sheets - 4x8",
        cost = 15.75,
        isSelected = false,
        units = null
    )
    
    MaterialListItem(
        material = material,
        onItemSelected = { },
        onUnitsChanged = { }
    )
}

@Preview(name = "Add New Material Dialog", showBackground = true)
@Composable
fun AddNewMaterialDialogPreview() {
    val mockBaasViewModel = remember {
        object : BaasViewModel() {
            override fun createDefaultMaterial(userId: Int, cost: Int, name: String) {
                // Mock implementation
            }
        }
    }
    
    AddNewMaterialDialog(
        onDismissRequest = { },
        onMaterialAdded = { _, _ -> },
        baasViewModel = mockBaasViewModel,
        userId = 456
    )
}

@Preview(name = "Add New Material Dialog Header", showBackground = true)
@Composable
fun AddNewMaterialDialogHeaderPreview() {
    val mockBaasViewModel = remember {
        object : BaasViewModel() {
            override fun createDefaultMaterial(userId: Int, cost: Int, name: String) {
                // Mock implementation
            }
        }
    }
    
    AddNewMaterialDialogHeader(
        materialName = "New Material",
        unitCost = "25.50",
        onDismiss = { },
        onSave = { _, _ -> },
        baasViewModel = mockBaasViewModel,
        userId = 456
    )
}

@Preview(name = "Add New Material Dialog Content", showBackground = true)
@Composable
fun AddNewMaterialDialogContentPreview() {
    AddNewMaterialDialogContentBody(
        materialName = "Premium Hardwood",
        onMaterialNameChange = { },
        unitCost = "45.00",
        onUnitCostChange = { },
        shouldAddToDefaultList = true,
        onShouldAddToDefaultListChange = { }
    )
}

// Tablet Layout Preview (simulated)
@Preview(name = "Tablet Layout - Master Detail", showBackground = true, widthDp = 1024, heightDp = 768)
@Composable
fun TabletLayoutPreview() {
    val mockBaasViewModel = remember {
        object : BaasViewModel() {
            override fun fetchDefaultMaterialList(filter: String) {
                // Mock implementation
            }
        }
    }
    
    val mockNavController = remember {
        object : NavController(LocalContext.current) {
            override fun popBackStack(): Boolean = true
        }
    }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Full-width top bar for tablet
        AddEditMaterialLineItemTopBar(
            customerName = "John Smith - Kitchen Renovation",
            isEditable = 0,
            navController = mockNavController
        )
        
        // Master-detail layout simulation
        Row(
            modifier = Modifier.fillMaxSize()
        ) {
            // Master panel (left side - 1/3 width)
            Column(
                modifier = Modifier
                    .weight(1f)
                    .background(Color(0xFFF5F5F5))
                    .padding(16.dp)
            ) {
                Text(
                    text = "Line Items",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = colorResource(R.color.profit_black)
                )
                Spacer(modifier = Modifier.height(16.dp))
                
                // Sample line items in master panel
                listOf(
                    "Line Item #1 - Foundation Work",
                    "Line Item #2 - Framing",
                    "Line Item #3 - Electrical",
                    "Line Item #4 - Plumbing",
                    "Line Item #5 - Materials (Current)"
                ).forEach { item ->
                    Text(
                        text = item,
                        fontSize = 14.sp,
                        color = if (item.contains("Materials")) colorResource(R.color.profit_blue) else colorResource(R.color.profit_black),
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp)
                            .background(
                                if (item.contains("Materials")) Color(0xFFE3F2FD) else Color.Transparent,
                                RoundedCornerShape(4.dp)
                            )
                            .padding(8.dp)
                    )
                }
            }
            
            // Detail panel (right side - 2/3 width)
            Column(
                modifier = Modifier
                    .weight(2f)
                    .background(Color.White)
                    .padding(16.dp)
            ) {
                // Detail header with Save/Cancel buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = CenterVertically
                ) {
                    Text(
                        text = "Add Material Line Item",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium,
                        color = colorResource(R.color.profit_black)
                    )
                    
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Button(
                            shape = RoundedCornerShape(4.dp),
                            colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.profit_blue)),
                            contentPadding = PaddingValues(horizontal = 12.dp, vertical = 6.dp),
                            onClick = { }
                        ) {
                            Text("Save", fontSize = 16.sp, color = Color.White)
                        }
                        
                        Button(
                            shape = RoundedCornerShape(4.dp),
                            colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
                            border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
                            contentPadding = PaddingValues(horizontal = 12.dp, vertical = 6.dp),
                            onClick = { }
                        ) {
                            Text("Cancel", fontSize = 16.sp, color = colorResource(R.color.profit_blue))
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // Content area
                Column {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = CenterVertically
                    ) {
                        Text(
                            text = "Square Foot Costs",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            color = colorResource(R.color.gray)
                        )
                        Button(
                            onClick = { },
                            shape = RoundedCornerShape(4.dp),
                            colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.white)),
                            border = BorderStroke(1.dp, colorResource(R.color.profit_blue)),
                            contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp),
                            modifier = Modifier.height(28.dp)
                        ) {
                            Text("Add New Cost", fontSize = 16.sp, color = colorResource(R.color.profit_blue))
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Text(
                        "Labor hours",
                        color = colorResource(R.color.profit_black),
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.padding(bottom = 4.dp)
                    )
                    
                    BasicTextField(
                        value = "8",
                        onValueChange = { },
                        singleLine = true,
                        textStyle = LocalTextStyle.current.copy(color = Color.Black),
                        modifier = Modifier
                            .fillMaxWidth()
                            .border(1.dp, Color.Gray, shape = RoundedCornerShape(8.dp))
                            .padding(horizontal = 12.dp, vertical = 10.dp)
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // Sample material items
                    listOf(
                        MaterialRespListModel(id = 1, name = "Lumber - 2x4", cost = 8.50, isSelected = true, units = 10),
                        MaterialRespListModel(id = 2, name = "Drywall Sheets", cost = 12.75, isSelected = false, units = null),
                        MaterialRespListModel(id = 3, name = "Paint - Interior", cost = 25.00, isSelected = true, units = 5)
                    ).forEach { material ->
                        MaterialListItem(
                            material = material,
                            onItemSelected = { },
                            onUnitsChanged = { }
                        )
                        Spacer(modifier = Modifier.height(12.dp))
                    }
                }
            }
        }
    }
}

// Responsive Layout Preview
@Preview(name = "Responsive Layout - Mobile", showBackground = true, widthDp = 360, heightDp = 640)
@Composable
fun ResponsiveLayoutMobilePreview() {
    AddEditMaterialLineItemScreenMobilePreview()
}

@Preview(name = "Responsive Layout - Tablet", showBackground = true, widthDp = 1024, heightDp = 768)
@Composable
fun ResponsiveLayoutTabletPreview() {
    TabletLayoutPreview()
}

// Custom Checkbox Preview
@Preview(name = "Custom Checkbox", showBackground = true)
@Composable
fun CustomCheckboxPreview() {
    var checked by remember { mutableStateOf(false) }
    
    Row(
        verticalAlignment = CenterVertically,
        modifier = Modifier.padding(16.dp)
    ) {
        CustomCheckbox(
            checked = checked,
            onCheckedChange = { checked = it }
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text(
            text = "Sample Material Item",
            fontSize = 16.sp,
            color = colorResource(R.color.profit_black)
        )
    }
}

// Error State Preview
@Preview(name = "Error State - Empty Materials", showBackground = true)
@Composable
fun ErrorStatePreview() {
    val mockBaasViewModel = remember {
        object : BaasViewModel() {
            override fun fetchDefaultMaterialList(filter: String) {
                // Mock implementation
            }
        }
    }
    
    val mockNavController = remember {
        object : NavController(LocalContext.current) {
            override fun popBackStack(): Boolean = true
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(16.dp)
    ) {
        Text(
            text = "No materials available",
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = colorResource(R.color.gray),
            modifier = Modifier.align(Alignment.CenterHorizontally)
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Button(
            onClick = { },
            shape = RoundedCornerShape(4.dp),
            colors = ButtonDefaults.buttonColors(containerColor = colorResource(R.color.profit_blue)),
            modifier = Modifier.align(Alignment.CenterHorizontally)
        ) {
            Text("Add New Material", color = Color.White)
        }
    }
}