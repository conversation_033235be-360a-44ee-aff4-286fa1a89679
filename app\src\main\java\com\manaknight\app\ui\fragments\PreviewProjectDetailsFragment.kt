package com.manaknight.app.ui.fragments

import Manaknight.R
import android.app.Dialog
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.manaknight.app.extensions.snackBar
import com.manaknight.app.extensions.snackBarForDialog
import com.manaknight.app.network.Status
//import androidx.fragment.app.viewModels
//import com.manaknight.app.viewmodels.BaasViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel
import com.manaknight.app.ui.screens.PreviewProjectDetailScreen
import com.manaknight.app.ui.screens.ProjectDetailMainScreen
import com.manaknight.app.utils.ProgressDialog.Companion.progressDialog
import com.manaknight.app.viewmodels.BaasViewModel

class PreviewProjectDetailsFragment : Fragment() {

    private val baasViewModel: BaasViewModel by viewModel()
    private lateinit var dialog: Dialog
    private val args by navArgs<PreviewProjectDetailsFragmentArgs>()
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val projectId = args.projectID ?: 234
        val customerName =  ""  // fallback if not present
//        TODO:
//        val customerName = try { args.customerName } catch (e: Exception) { "" } // fallback if not present

        dialog = progressDialog(requireContext())

        return ComposeView(requireContext()).apply {
            setContent {
                ProjectDetailMainScreen(
                    projectId,
                    baasViewModel = baasViewModel,
                    onClick = { email ->
                        sendInvoice(projectId, email)
                    },
                    navController = findNavController(),
                    dialog = dialog,
                    onNavigateBack = {
                        navigateToLineItems(projectId, customerName)
                    }
                )
            }
        }
    }

    fun sendInvoice(projectId:Int,email:String){
        baasViewModel.sendInvoice(
            pdf="", session_name="",
            email = email,project_id=projectId
        )
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> {
                        dialog.dismiss()
                    }
                    Status.LOADING -> {
                        dialog.show()
                    }

                    Status.SUCCESS -> {
                        dialog.dismiss()
                        if (it.data?.error == true) {
                            if (it.data.message?.contains("Draw percentage exceed") == true) {
                                snackBarForDialog("Draw percentage exceeds the remaining value.")
                            } else {
                                snackBarForDialog(it.data.message ?: "Server Error")
                            }
                        } else {
                            // Fetch project details to check status
                            baasViewModel.getSingleProjectDetails(projectId).observe(viewLifecycleOwner) { projectResource ->
                                val projectStatus = projectResource.data?.status
                                // if (projectStatus == 1) {
                                    // Retrieve required fields
                                    val clientDetails = projectResource.data?.client_details
                                    val totals = projectResource.data?.totals
                                    val changeCount = 0 // or retrieve if available
                                    val customerId = clientDetails?.customer_id ?: 0
                                    val userId = clientDetails?.user_id ?: 0
                                    val profitOverhead = totals?.total_profit_overhead?.toString() ?: "0"
                                    val hourlyRate = totals?.labour_budget?.toString() ?: "0"
                                    dialog.show()
                                    baasViewModel.updateProject(
                                        changeCount = changeCount,
                                        customerId = customerId,
                                        userId = userId,
                                        status = 2, // set to 2 after sending invoice
                                        profitOverhead = profitOverhead,
                                        hourlyRate = hourlyRate,
                                        id = projectId
                                    ).observe(viewLifecycleOwner) { updateResource ->
                                        when (updateResource.status) {
                                            Status.LOADING -> dialog.show()
                                            Status.SUCCESS -> {
                                                dialog.dismiss()
                                                snackBarForDialog("Project status updated to sent.")
                                                val bundle = Bundle().apply {
                                                    putInt("projectID", projectId)
                                                }
                                                findNavController().navigate(R.id.action_previewProjectDetailsFragment_to_InvoiceFragment, bundle)
                                            }
                                            Status.ERROR -> {
                                                dialog.dismiss()
                                                snackBarForDialog(updateResource.message ?: "Failed to update project status.")
                                            }
                                        }
                                    }
                                // } else {
                                //     // Status is not 1, just navigate to invoice
                                //     val bundle = Bundle().apply {
                                //         putInt("projectID", projectId)
                                //     }
                                //     findNavController().navigate(R.id.action_previewProjectDetailsFragment_to_InvoiceFragment, bundle)
                                // }
                            }
                        }
                    }
                }
            }
    }

    private fun navigateToLineItems(projectId: Int, customerName: String?) {
        val action = PreviewProjectDetailsFragmentDirections.actionPreviewProjectDetailsFragmentToLineItemView(
            projectID = projectId,
            customerName = customerName ?: ""
        )
        findNavController().navigate(action)
    }
}
