// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemLineMaterialBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final CheckBox checkBoxMaterial;

  @NonNull
  public final EditText edtUnits;

  @NonNull
  public final TextView txtMaterialName;

  @NonNull
  public final TextView txtPrice;

  @NonNull
  public final TextView txtTotalPrice;

  @NonNull
  public final TextView txtUnit;

  @NonNull
  public final TextView txtUnitStart;

  private ItemLineMaterialBinding(@NonNull CardView rootView, @NonNull CheckBox checkBoxMaterial,
      @NonNull EditText edtUnits, @NonNull TextView txtMaterialName, @NonNull TextView txtPrice,
      @NonNull TextView txtTotalPrice, @NonNull TextView txtUnit, @NonNull TextView txtUnitStart) {
    this.rootView = rootView;
    this.checkBoxMaterial = checkBoxMaterial;
    this.edtUnits = edtUnits;
    this.txtMaterialName = txtMaterialName;
    this.txtPrice = txtPrice;
    this.txtTotalPrice = txtTotalPrice;
    this.txtUnit = txtUnit;
    this.txtUnitStart = txtUnitStart;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemLineMaterialBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemLineMaterialBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_line_material, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemLineMaterialBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.checkBoxMaterial;
      CheckBox checkBoxMaterial = ViewBindings.findChildViewById(rootView, id);
      if (checkBoxMaterial == null) {
        break missingId;
      }

      id = R.id.edtUnits;
      EditText edtUnits = ViewBindings.findChildViewById(rootView, id);
      if (edtUnits == null) {
        break missingId;
      }

      id = R.id.txtMaterialName;
      TextView txtMaterialName = ViewBindings.findChildViewById(rootView, id);
      if (txtMaterialName == null) {
        break missingId;
      }

      id = R.id.txtPrice;
      TextView txtPrice = ViewBindings.findChildViewById(rootView, id);
      if (txtPrice == null) {
        break missingId;
      }

      id = R.id.txtTotalPrice;
      TextView txtTotalPrice = ViewBindings.findChildViewById(rootView, id);
      if (txtTotalPrice == null) {
        break missingId;
      }

      id = R.id.txtUnit;
      TextView txtUnit = ViewBindings.findChildViewById(rootView, id);
      if (txtUnit == null) {
        break missingId;
      }

      id = R.id.txtUnitStart;
      TextView txtUnitStart = ViewBindings.findChildViewById(rootView, id);
      if (txtUnitStart == null) {
        break missingId;
      }

      return new ItemLineMaterialBinding((CardView) rootView, checkBoxMaterial, edtUnits,
          txtMaterialName, txtPrice, txtTotalPrice, txtUnit, txtUnitStart);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
