// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class BottomSheetMultiSelectStatusFilterBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final ImageView closeButton;

  @NonNull
  public final RecyclerView statusRecyclerView;

  private BottomSheetMultiSelectStatusFilterBinding(@NonNull LinearLayout rootView,
      @Nullable ImageView closeButton, @NonNull RecyclerView statusRecyclerView) {
    this.rootView = rootView;
    this.closeButton = closeButton;
    this.statusRecyclerView = statusRecyclerView;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static BottomSheetMultiSelectStatusFilterBinding inflate(
      @NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static BottomSheetMultiSelectStatusFilterBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.bottom_sheet_multi_select_status_filter, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static BottomSheetMultiSelectStatusFilterBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.closeButton;
      ImageView closeButton = ViewBindings.findChildViewById(rootView, id);

      id = R.id.statusRecyclerView;
      RecyclerView statusRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (statusRecyclerView == null) {
        break missingId;
      }

      return new BottomSheetMultiSelectStatusFilterBinding((LinearLayout) rootView, closeButton,
          statusRecyclerView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
