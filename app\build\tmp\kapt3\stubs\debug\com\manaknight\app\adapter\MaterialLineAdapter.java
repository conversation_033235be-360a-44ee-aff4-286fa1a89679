package com.manaknight.app.adapter;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0006\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0005\u0018\u00002\f\u0012\b\u0012\u00060\u0002R\u00020\u00000\u0001:\u0001\u001dB\u001f\u0012\u0018\u0010\u0003\u001a\u0014\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0004\u00a2\u0006\u0002\u0010\bJ\u0010\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0005H\u0002J\b\u0010\u0013\u001a\u00020\u0006H\u0016J\u001c\u0010\u0014\u001a\u00020\u00072\n\u0010\u0015\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u0016\u001a\u00020\u0006H\u0016J\u001c\u0010\u0017\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u0006H\u0016J\u001e\u0010\u001b\u001a\u00020\u00072\u0016\u0010\u001c\u001a\u0012\u0012\u0004\u0012\u00020\u00050\nj\b\u0012\u0004\u0012\u00020\u0005`\u000bR!\u0010\t\u001a\u0012\u0012\u0004\u0012\u00020\u00050\nj\b\u0012\u0004\u0012\u00020\u0005`\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR#\u0010\u0003\u001a\u0014\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000f\u00a8\u0006\u001e"}, d2 = {"Lcom/manaknight/app/adapter/MaterialLineAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/manaknight/app/adapter/MaterialLineAdapter$ViewHolder;", "onCheckBoxClick", "Lkotlin/Function2;", "Lcom/manaknight/app/model/remote/profitPro/MaterialRespListModel;", "", "", "(Lkotlin/jvm/functions/Function2;)V", "list", "Ljava/util/ArrayList;", "Lkotlin/collections/ArrayList;", "getList", "()Ljava/util/ArrayList;", "getOnCheckBoxClick", "()Lkotlin/jvm/functions/Function2;", "calculateTotal", "", "material", "getItemCount", "onBindViewHolder", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "refresh", "newList", "ViewHolder", "app_debug"})
public final class MaterialLineAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.manaknight.app.adapter.MaterialLineAdapter.ViewHolder> {
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function2<com.manaknight.app.model.remote.profitPro.MaterialRespListModel, java.lang.Integer, kotlin.Unit> onCheckBoxClick = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<com.manaknight.app.model.remote.profitPro.MaterialRespListModel> list = null;
    
    public MaterialLineAdapter(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super com.manaknight.app.model.remote.profitPro.MaterialRespListModel, ? super java.lang.Integer, kotlin.Unit> onCheckBoxClick) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlin.jvm.functions.Function2<com.manaknight.app.model.remote.profitPro.MaterialRespListModel, java.lang.Integer, kotlin.Unit> getOnCheckBoxClick() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.ArrayList<com.manaknight.app.model.remote.profitPro.MaterialRespListModel> getList() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.manaknight.app.adapter.MaterialLineAdapter.ViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.adapter.MaterialLineAdapter.ViewHolder holder, int position) {
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    public final void refresh(@org.jetbrains.annotations.NotNull()
    java.util.ArrayList<com.manaknight.app.model.remote.profitPro.MaterialRespListModel> newList) {
    }
    
    private final double calculateTotal(com.manaknight.app.model.remote.profitPro.MaterialRespListModel material) {
        return 0.0;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/manaknight/app/adapter/MaterialLineAdapter$ViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "binding", "LManaknight/databinding/ItemLineMaterialBinding;", "(Lcom/manaknight/app/adapter/MaterialLineAdapter;LManaknight/databinding/ItemLineMaterialBinding;)V", "getBinding", "()LManaknight/databinding/ItemLineMaterialBinding;", "app_debug"})
    public final class ViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final Manaknight.databinding.ItemLineMaterialBinding binding = null;
        
        public ViewHolder(@org.jetbrains.annotations.NotNull()
        Manaknight.databinding.ItemLineMaterialBinding binding) {
            super(null);
        }
        
        @org.jetbrains.annotations.NotNull()
        public final Manaknight.databinding.ItemLineMaterialBinding getBinding() {
            return null;
        }
    }
}