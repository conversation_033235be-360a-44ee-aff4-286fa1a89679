// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentProfileviewBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnContinue;

  @NonNull
  public final ImageView btnEdit;

  @NonNull
  public final ConstraintLayout constraint;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final RelativeLayout container;

  @NonNull
  public final HeaderBinding headerInclude;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final ConstraintLayout innerConstraintLayout;

  @NonNull
  public final LinearLayout line1;

  @NonNull
  public final ScrollView scrollable;

  @NonNull
  public final TextView txtCompanyName;

  @NonNull
  public final TextView txtEmail;

  @NonNull
  public final TextView txtFName;

  @NonNull
  public final TextView txtLName;

  private FragmentProfileviewBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnContinue, @NonNull ImageView btnEdit,
      @NonNull ConstraintLayout constraint, @Nullable RelativeLayout container,
      @NonNull HeaderBinding headerInclude, @Nullable ConstraintLayout innerConstraintLayout,
      @NonNull LinearLayout line1, @NonNull ScrollView scrollable, @NonNull TextView txtCompanyName,
      @NonNull TextView txtEmail, @NonNull TextView txtFName, @NonNull TextView txtLName) {
    this.rootView = rootView;
    this.btnContinue = btnContinue;
    this.btnEdit = btnEdit;
    this.constraint = constraint;
    this.container = container;
    this.headerInclude = headerInclude;
    this.innerConstraintLayout = innerConstraintLayout;
    this.line1 = line1;
    this.scrollable = scrollable;
    this.txtCompanyName = txtCompanyName;
    this.txtEmail = txtEmail;
    this.txtFName = txtFName;
    this.txtLName = txtLName;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentProfileviewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentProfileviewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_profileview, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentProfileviewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnContinue;
      MaterialButton btnContinue = ViewBindings.findChildViewById(rootView, id);
      if (btnContinue == null) {
        break missingId;
      }

      id = R.id.btnEdit;
      ImageView btnEdit = ViewBindings.findChildViewById(rootView, id);
      if (btnEdit == null) {
        break missingId;
      }

      ConstraintLayout constraint = (ConstraintLayout) rootView;

      id = R.id.container;
      RelativeLayout container = ViewBindings.findChildViewById(rootView, id);

      id = R.id.headerInclude;
      View headerInclude = ViewBindings.findChildViewById(rootView, id);
      if (headerInclude == null) {
        break missingId;
      }
      HeaderBinding binding_headerInclude = HeaderBinding.bind(headerInclude);

      id = R.id.innerConstraintLayout;
      ConstraintLayout innerConstraintLayout = ViewBindings.findChildViewById(rootView, id);

      id = R.id.line1;
      LinearLayout line1 = ViewBindings.findChildViewById(rootView, id);
      if (line1 == null) {
        break missingId;
      }

      id = R.id.scrollable;
      ScrollView scrollable = ViewBindings.findChildViewById(rootView, id);
      if (scrollable == null) {
        break missingId;
      }

      id = R.id.txtCompanyName;
      TextView txtCompanyName = ViewBindings.findChildViewById(rootView, id);
      if (txtCompanyName == null) {
        break missingId;
      }

      id = R.id.txtEmail;
      TextView txtEmail = ViewBindings.findChildViewById(rootView, id);
      if (txtEmail == null) {
        break missingId;
      }

      id = R.id.txtFName;
      TextView txtFName = ViewBindings.findChildViewById(rootView, id);
      if (txtFName == null) {
        break missingId;
      }

      id = R.id.txtLName;
      TextView txtLName = ViewBindings.findChildViewById(rootView, id);
      if (txtLName == null) {
        break missingId;
      }

      return new FragmentProfileviewBinding((ConstraintLayout) rootView, btnContinue, btnEdit,
          constraint, container, binding_headerInclude, innerConstraintLayout, line1, scrollable,
          txtCompanyName, txtEmail, txtFName, txtLName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
