
package com.manaknight.app.ui.fragments.home

import android.os.Bundle
import android.view.View
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import android.widget.Toast
import Manaknight.R
import Manaknight.databinding.BottomAddDrawBinding
import Manaknight.databinding.BottomAddEmployeeBinding
import Manaknight.databinding.FragmentCreateCustomerBinding
import Manaknight.databinding.FragmentDrawsBinding
import Manaknight.databinding.FragmentLineItemsBinding
import Manaknight.databinding.FragmentSignUpBinding
import android.app.Activity
import android.app.Dialog
import android.content.ContentValues.TAG
import android.graphics.Color
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.MotionEvent
import android.view.ViewGroup
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.CompoundButton
import android.widget.EditText
import android.widget.RadioButton
import com.manaknight.app.extensions.checkIsEmpty
import com.manaknight.app.extensions.disableSpaces
import com.manaknight.app.extensions.hide
import com.manaknight.app.extensions.hideSoftKeyboard
import com.manaknight.app.extensions.setOnClickWithDebounce
import com.manaknight.app.extensions.show
import com.manaknight.app.extensions.snackBar
import com.manaknight.app.extensions.textToString
import com.manaknight.app.extensions.viewBinding
import com.manaknight.app.network.Status
import com.manaknight.app.viewmodels.BaasViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.LiveData
import androidx.navigation.fragment.navArgs
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.manaknight.app.adapter.CustomerAdapter
import com.manaknight.app.adapter.DrawsAdapter
import com.manaknight.app.data.local.AppPreferences
import com.manaknight.app.extensions.isEmailValid
import com.manaknight.app.extensions.setVerticalLayout
import com.manaknight.app.extensions.showProgressBar
import com.manaknight.app.extensions.snackBarForDialog
import com.manaknight.app.model.remote.EmplyeeModel
import com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel
import com.manaknight.app.model.remote.profitPro.ClientDetailRespModel
import com.manaknight.app.model.remote.profitPro.CommonResponse
import com.manaknight.app.model.remote.profitPro.CompanyRequest
import com.manaknight.app.model.remote.profitPro.CustomerRespListModel
import com.manaknight.app.model.remote.profitPro.DrawInfoModelRespModel
import com.manaknight.app.model.remote.profitPro.DrawInfoRespModel
import com.manaknight.app.model.remote.profitPro.DrawsRespModel
import com.manaknight.app.model.remote.profitPro.JobDetailsRespModel
import com.manaknight.app.model.remote.profitPro.LinearRespListModel
import com.manaknight.app.model.remote.profitPro.TotalRespModel
import com.manaknight.app.network.Resource
import com.manaknight.app.ui.CreateEstimationFragment
import com.manaknight.app.ui.CreateEstimationFragmentDirections
import com.manaknight.app.utils.CustomUtils.getOrdinal
import com.manaknight.app.utils.ProgressDialog.Companion.progressDialog
import org.koin.android.ext.android.inject


class DrawsFragment : Fragment(R.layout.fragment_draws) {

    private val binding by viewBinding(FragmentDrawsBinding::bind)
    private val args by navArgs<DrawsFragmentArgs>()
    private val baasViewModel: BaasViewModel by viewModel()

    private lateinit var dialog: Dialog
    private val pref by inject<AppPreferences>()

    private val drawAdapter by lazy { DrawsAdapter(this::onReviewClick,this::onAddDrawClick, this::onEditClick, this::onDeleteClick) }

    private val allDrawList: ArrayList<DrawsRespModel> = ArrayList()
    private var infoModel: DrawInfoModelRespModel? = null
    private var drawSelection = 0


    private fun onEditClick(item: DrawsRespModel, position: Int) {

        drawSelection = 0
        var drawInfo = ""
        var isEditable = true
        if (position == 0) {
            drawInfo = "First Draw Info"
            isEditable = false
        } else if (position == allDrawList.count() - 1) {
            drawInfo = "Last Draw Info"
            isEditable = false
        } else {
//            drawInfo = "${position+1} Draw Info"
            drawInfo = "${getOrdinal(position)} Draw Info"
        }

        showEditDraw(item, drawInfo, isEditable)
    }

    private fun onDeleteClick(item: DrawsRespModel, position: Int) {

        getDeleteDraw(item.id ?: 0)

    }

    private fun onAddDrawClick() {

        showAddNewDraw()
    }
    private fun onReviewClick() {

        val bundle = Bundle().apply {
            putInt("projectID", args.projectID)
        }
        findNavController().navigate(R.id.previewProjectDetailsFragment, bundle)

        // Trigger navigation to PreviewProjectDetailScreen
//        val navController = findNavController()
////        navController.navigate(R.id.action_yourFragment_to_previewProjectDetailScreen, )
//        navController.navigate(R.id.previewProjectDetailsFragment)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog = progressDialog(requireContext())
        binding.headerInclude.backButton.setOnClickListener {
            findNavController().popBackStack()
        }

        binding.headerInclude.addPlaterTitle.text =  args.customerName + " New Estimation"

        binding.materialRecylerView.apply {
            setVerticalLayout()
            adapter = drawAdapter
        }
    }

    private fun getAllDraws() {

        baasViewModel.getAllDraws(args.projectID)
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> {
                        snackBar(it.message ?: "Server Error")
                        dialog.dismiss()
                    }
                    Status.LOADING -> {
                        dialog.show()
                    }

                    Status.SUCCESS -> {
                        dialog.dismiss()

                        allDrawList.clear()
                        it.data?.model?.let { it1 ->
                            infoModel = it1
                            it1.draws.let { it2 ->



                                getDrawList()?.let { list ->
                                    allDrawList.addAll(list)
                                }
                            }
                            updateView()
                        }
                        drawAdapter.refresh(allDrawList)
                        
                        // Check if we should auto-show the first draw popup
                        checkAndShowFirstDrawPopup()
                    }
                }
            }
    }

    private fun checkAndShowFirstDrawPopup() {
        // Check if this is the first time opening draws for this project
        if (!pref.hasShownFirstDrawPopup(args.projectID) && allDrawList.isNotEmpty()) {
            // Auto-show the first draw edit popup
            val firstDraw = allDrawList[0]
            onEditClick(firstDraw, 0)
            // Mark that we've shown the popup for this project
            pref.setFirstDrawPopupShown(args.projectID, true)
        }
    }

    private fun getDeleteDraw(id: Int) {

        baasViewModel.deleteDraws(id)
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> {
                        snackBar(it.message ?: "Server Error")
                        dialog.dismiss()
                    }
                    Status.LOADING -> {
                        dialog.show()
                    }

                    Status.SUCCESS -> {
                        dialog.dismiss()
                        snackBar("Draw deleted successfully.")
                        getAllDraws()
                    }
                }
            }
    }

    private fun addNewPriceDraw(description: String, amount: String, sheetDialog: BottomSheetDialog) {

        baasViewModel.createPriceDraw(args.projectID, description, amount)
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> {
                        sheetDialog.dismiss()
                        snackBar(it.message ?: "Server Error")
                        dialog.dismiss()
                    }
                    Status.LOADING -> {
                        dialog.show()
                    }

                    Status.SUCCESS -> {
                        dialog.dismiss()
                        sheetDialog.dismiss()
                        snackBarForDialog("Draw created successfully.")
                        getAllDraws()
                    }
                }
            }
    }

    private fun updatePercentageDraw(description: String, percentage: String, id: Int, sheet: BottomSheetDialog) {

        baasViewModel.updatePercentageDraw(args.projectID, description, percentage, id)
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> {
                        sheet.dismiss()
                        snackBar(it.message ?: "Server Error")
                        dialog.dismiss()
                    }
                    Status.LOADING -> {
                        dialog.show()
                    }

                    Status.SUCCESS -> {
                        dialog.dismiss()
                        sheet.dismiss()
                        snackBarForDialog("Draw updated successfully.")
                        getAllDraws()
                    }
                }
            }
    }

    private fun updatePriceDraw(description: String, amount: String, id: Int, sheetDialog: BottomSheetDialog) {

        baasViewModel.updatePriceDraw(args.projectID, description, amount, id)
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> {
                        sheetDialog.dismiss()
                        snackBar(it.message ?: "Server Error")
                        dialog.dismiss()
                    }
                    Status.LOADING -> {
                        dialog.show()
                    }

                    Status.SUCCESS -> {
                        dialog.dismiss()
                        sheetDialog.dismiss()
                        snackBarForDialog("Draw updated successfully.")
                        getAllDraws()
                    }
                }
            }
    }

    private fun addNewPercentageDraw(description: String, percentage: String, sheetDialog: BottomSheetDialog) {

        baasViewModel.createPercentageDraw(args.projectID, description, percentage)
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> {
                        sheetDialog.dismiss()
                        snackBar(it.message ?: "Server Error")
                        dialog.dismiss()
                    }
                    Status.LOADING -> {
                        dialog.show()
                    }

                    Status.SUCCESS -> {
                        dialog.dismiss()
                        sheetDialog.dismiss()
                        snackBarForDialog("Draw created successfully.")
                        getAllDraws()
                    }
                }
            }
    }


    fun getDrawList(): List<DrawsRespModel>? {
        val drawList = infoModel?.draws ?: return null

        return drawList.sortedByDescending { draw ->
            when (draw.description) {
                "At Contract Signing" -> 2  // Highest priority
                "On Project Completion" -> 0  // Lowest priority
                else -> 1  // Middle priority
            }
        }
    }

    fun updateView() {

        infoModel?.let { info ->
            binding.txtSalePrice.text = "$${info.sale_price ?: 0}"

            //binding.txtUnaccounted.text = "$${info.unaccounted_amount ?: 0}"
            val amount = info.unaccounted_amount?.toDoubleOrNull() ?: 0.0
            val displayAmount = if (amount < 0.02) "0.00" else "$$amount"

            binding.txtUnaccounted.text = displayAmount

        }
    }

    private fun showAddNewDraw() {

        drawSelection = 0
        // Declare listeners first
        lateinit var dollarListener: CompoundButton.OnCheckedChangeListener
        lateinit var percentageListener: CompoundButton.OnCheckedChangeListener

        val sheet = BottomSheetDialog(requireContext(), R.style.BottomSheetDialogTheme)
        val view = BottomAddDrawBinding.inflate(layoutInflater)

        sheet.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
        sheet.setOnShowListener { dialogInterface ->
            val bottomSheetDialog = dialogInterface as BottomSheetDialog
            val bottomSheet = bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)

            if (bottomSheet != null) {
                // Set height to full screen
                bottomSheet.layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT

                // Add a top margin of 100px
                val layoutParams = bottomSheet.layoutParams as ViewGroup.MarginLayoutParams
                layoutParams.topMargin = 0 // Adjust margin as needed
                layoutParams.bottomMargin = 0
                bottomSheet.layoutParams = layoutParams

                // Ensure the bottom sheet is expanded
                val behavior = BottomSheetBehavior.from(bottomSheet)
                behavior.state = BottomSheetBehavior.STATE_EXPANDED
                behavior.isFitToContents = false
                behavior.skipCollapsed = true
            }
        }
        view.apply {

            btnSave.isEnabled = false
            btnSave.alpha = 0.5f

            // Get available amount from infoModel
            val availableAmount = infoModel?.unaccounted_amount?.toDoubleOrNull() ?: 0.0
            val totalAmount = infoModel?.sale_price?.toDoubleOrNull() ?: 0.0
            val availablePercentage = if (totalAmount > 0) (availableAmount / totalAmount) * 100 else 0.0

            // Display available amounts
            tvAvailableAmount?.text = "Available Amount: $${String.format("%.2f", availableAmount)}"
            tvAvailablePercentage?.text = "Available Percentage: ${String.format("%.2f", availablePercentage)}%"

//            addHeading.text =  "${allDrawList.count()} Draw Info"
            addHeading.text = "${getOrdinal(allDrawList.count()-1)} Draw Info"

            // Validation function
            fun validateInputs() {
                val isDescriptionValid = descInput.text.isNotEmpty()
                var isAmountValid = false
                var isWithinLimit = false

                if (drawSelection == 0) {
                    // Dollar amount validation
                    val inputAmount = edTxtPrice.text.toString().toDoubleOrNull() ?: 0.0
                    isAmountValid = inputAmount > 0
                    isWithinLimit = inputAmount <= availableAmount
                } else {
                    // Percentage validation
                    val inputPercentage = edTxtPercentage.text.toString().toDoubleOrNull() ?: 0.0
                    isAmountValid = inputPercentage > 0
                    isWithinLimit = inputPercentage <= availablePercentage
                }

                btnSave.isEnabled = isDescriptionValid && isAmountValid && isWithinLimit
                btnSave.alpha = if (btnSave.isEnabled) 1f else 0.5f
            }

            val textWatcher = object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    validateInputs()
                }

                override fun afterTextChanged(s: Editable?) {}
            }

            if (drawSelection == 0) {
                radioButtonDollar.isChecked = true
                radioButtonPercentage.isChecked = false
                priceLayout.visibility = View.VISIBLE
                percentageLayout.visibility = View.GONE
            } else {
                radioButtonDollar.isChecked = false
                radioButtonPercentage.isChecked = true

                priceLayout.visibility = View.GONE
                percentageLayout.visibility = View.VISIBLE
            }

            descInput.addTextChangedListener(textWatcher)
            edTxtPrice.addTextChangedListener(textWatcher)
            edTxtPercentage.addTextChangedListener(textWatcher)

            backButton.setOnClickListener {
                sheet.dismiss()
            }
            btnSave.setOnClickListener {
                if (drawSelection == 0) {
                    addNewPriceDraw(descInput.text.toString(), edTxtPrice.text.toString(), sheet)
                } else {
                    addNewPercentageDraw(descInput.text.toString(), edTxtPercentage.text.toString(), sheet)
                }
            }


            // Inside onCreate() or appropriate function
            dollarListener = CompoundButton.OnCheckedChangeListener { _, isChecked ->
                if (isChecked) {
                    // Remove listener before changing state to prevent infinite loop
                    drawSelection = 0
                    radioButtonPercentage.setOnCheckedChangeListener(null)
                    radioButtonPercentage.isChecked = false
                    radioButtonPercentage.setOnCheckedChangeListener(percentageListener)

                    priceLayout.visibility = View.VISIBLE
                    percentageLayout.visibility = View.GONE
                    validateInputs() // Re-validate when switching types
                }
            }

            percentageListener = CompoundButton.OnCheckedChangeListener { _, isChecked ->
                if (isChecked) {
                    // Remove listener before changing state to prevent infinite loop
                    drawSelection = 1
                    radioButtonDollar.setOnCheckedChangeListener(null)
                    radioButtonDollar.isChecked = false
                    radioButtonDollar.setOnCheckedChangeListener(dollarListener)

                    priceLayout.visibility = View.GONE
                    percentageLayout.visibility = View.VISIBLE
                    validateInputs() // Re-validate when switching types
                }
            }

// Attach listeners to radio buttons
            radioButtonDollar.setOnCheckedChangeListener(dollarListener)
            radioButtonPercentage.setOnCheckedChangeListener(percentageListener)

            /*radioButtonDollar.setOnCheckedChangeListener { _, isChecked ->


                radioButtonDollar.isChecked = true
                radioButtonPercentage.isChecked = false

                priceLayout.visibility = View.VISIBLE
                percentageLayout.visibility = View.GONE
            }

            radioButtonPercentage.setOnCheckedChangeListener { _, isChecked ->
                radioButtonDollar.isChecked = false
                radioButtonPercentage.isChecked = true

                priceLayout.visibility = View.GONE
                percentageLayout.visibility = View.VISIBLE
            }*/

            line1.setOnClickListener() {
                val imm = requireActivity().getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
                imm.hideSoftInputFromWindow(view.root.windowToken, 0)

            }
        }


        sheet.setOnDismissListener {
            requireActivity().hideSoftKeyboard()
        }

        sheet.setCancelable(true)
        sheet.setContentView(view.root)
        sheet.show()
    }

    private fun showEditDraw(item: DrawsRespModel, drawInfo: String, isEditable: Boolean) {

        drawSelection = 0
        lateinit var dollarListener: CompoundButton.OnCheckedChangeListener
        lateinit var percentageListener: CompoundButton.OnCheckedChangeListener


        val sheet = BottomSheetDialog(requireContext(), R.style.BottomSheetDialogTheme)

        val view = BottomAddDrawBinding.inflate(layoutInflater)
        sheet.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
        sheet.setOnShowListener { dialogInterface ->
            val bottomSheetDialog = dialogInterface as BottomSheetDialog
            val bottomSheet = bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)

            if (bottomSheet != null) {
                // Set height to full screen
                bottomSheet.layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT

                // Add a top margin of 100px
                val layoutParams = bottomSheet.layoutParams as ViewGroup.MarginLayoutParams
                layoutParams.topMargin = 0 // Adjust margin as needed
                layoutParams.bottomMargin = 0
                bottomSheet.layoutParams = layoutParams

                // Ensure the bottom sheet is expanded
                val behavior = BottomSheetBehavior.from(bottomSheet)
                behavior.state = BottomSheetBehavior.STATE_EXPANDED
                behavior.isFitToContents = false
                behavior.skipCollapsed = true
            }

        }
        view.apply {

            // Get available amount from infoModel
            val availableAmount = infoModel?.unaccounted_amount?.toDoubleOrNull() ?: 0.0
            val totalAmount = infoModel?.sale_price?.toDoubleOrNull() ?: 0.0
            val availablePercentage = if (totalAmount > 0) (availableAmount / totalAmount) * 100 else 0.0

            // Display available amounts
            tvAvailableAmount?.text = "Available Amount: $${String.format("%.2f", availableAmount)}"
            tvAvailablePercentage?.text = "Available Percentage: ${String.format("%.2f", availablePercentage)}%"

            addHeading.text = drawInfo
            descInput.setText(item.description)
            edTxtPrice.setText(item.amount)

            if (isEditable) {

            } else {

                descInput.isEnabled = false // Disables input
                descInput.isFocusable = false // Prevents focus
                descInput.isFocusableInTouchMode = false

                descInput.setTextColor(Color.parseColor("#868C98")) // Example color
                descInput.setBackgroundResource(R.drawable.rounded_edittext_none_editable)
            }

            // Validation function
            fun validateInputs() {
                val isDescriptionValid = descInput.text.isNotEmpty()
                var isAmountValid = false
                var isWithinLimit = false

                if (drawSelection == 0) {
                    // Dollar amount validation
                    val inputAmount = edTxtPrice.text.toString().toDoubleOrNull() ?: 0.0
                    isAmountValid = inputAmount > 0
                    isWithinLimit = inputAmount <= availableAmount
                } else {
                    // Percentage validation
                    val inputPercentage = edTxtPercentage.text.toString().toDoubleOrNull() ?: 0.0
                    isAmountValid = inputPercentage > 0
                    isWithinLimit = inputPercentage <= availablePercentage
                }

                btnSave.isEnabled = isDescriptionValid && isAmountValid && isWithinLimit
                btnSave.alpha = if (btnSave.isEnabled) 1f else 0.5f
            }

            val textWatcher = object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    validateInputs()
                }

                override fun afterTextChanged(s: Editable?) {}
            }

            descInput.addTextChangedListener(textWatcher)
            edTxtPrice.addTextChangedListener(textWatcher)
            edTxtPercentage.addTextChangedListener(textWatcher)

            backButton.setOnClickListener {
                sheet.dismiss()
            }
            btnSave.setOnClickListener {

                if (drawSelection == 0) {
                    updatePriceDraw(descInput.text.toString(), edTxtPrice.text.toString(), item.id ?: 0, sheet)
                } else {
                    updatePercentageDraw(descInput.text.toString(), edTxtPercentage.text.toString(), item.id ?: 0, sheet)
                }

            }

            // Inside onCreate() or appropriate function
            dollarListener = CompoundButton.OnCheckedChangeListener { _, isChecked ->
                if (isChecked) {
                    // Remove listener before changing state to prevent infinite loop
                    drawSelection = 0
                    radioButtonPercentage.setOnCheckedChangeListener(null)
                    radioButtonPercentage.isChecked = false
                    radioButtonPercentage.setOnCheckedChangeListener(percentageListener)

                    priceLayout.visibility = View.VISIBLE
                    percentageLayout.visibility = View.GONE
                    validateInputs() // Re-validate when switching types
                }
            }

            percentageListener = CompoundButton.OnCheckedChangeListener { _, isChecked ->
                if (isChecked) {
                    // Remove listener before changing state to prevent infinite loop
                    drawSelection = 1
                    radioButtonDollar.setOnCheckedChangeListener(null)
                    radioButtonDollar.isChecked = false
                    radioButtonDollar.setOnCheckedChangeListener(dollarListener)

                    priceLayout.visibility = View.GONE
                    percentageLayout.visibility = View.VISIBLE
                    validateInputs() // Re-validate when switching types
                }
            }

// Attach listeners to radio buttons
            radioButtonDollar.setOnCheckedChangeListener(dollarListener)
            radioButtonPercentage.setOnCheckedChangeListener(percentageListener)

            /*radioButtonDollar.setOnCheckedChangeListener { _, isChecked ->
                radioButtonDollar.isChecked = true
                radioButtonPercentage.isChecked = false

                priceLayout.visibility = View.VISIBLE
                percentageLayout.visibility = View.GONE
            }

            radioButtonPercentage.setOnCheckedChangeListener { _, isChecked ->
                radioButtonDollar.isChecked = false
                radioButtonPercentage.isChecked = true

                priceLayout.visibility = View.GONE
                percentageLayout.visibility = View.VISIBLE
            }*/

            line1.setOnClickListener() {
                val imm = requireActivity().getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
                imm.hideSoftInputFromWindow(view.root.windowToken, 0)

            }


        }

        sheet.setOnDismissListener {
            requireActivity().hideSoftKeyboard()
        }

        sheet.setCancelable(true)
        sheet.setContentView(view.root)
        sheet.show()
    }


    override fun onResume() {
            super.onResume()
            (activity as AppCompatActivity?)?.supportActionBar?.hide()
      binding.headerInclude.backButton.show()
      getAllDraws()
        }

        override fun onStop() {
            super.onStop()
            (activity as AppCompatActivity?)?.supportActionBar?.show()
        }

}
    
