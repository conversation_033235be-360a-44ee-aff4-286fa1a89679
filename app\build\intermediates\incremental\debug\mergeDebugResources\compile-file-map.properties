#Sat Aug 02 09:50:14 WAT 2025
Manaknight.app-main-109\:/anim/scanner_animation.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_scanner_animation.xml.flat
Manaknight.app-main-109\:/color/bottom_nav_color.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_bottom_nav_color.xml.flat
Manaknight.app-main-109\:/drawable/add_shape.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_add_shape.xml.flat
Manaknight.app-main-109\:/drawable/apple.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_apple.webp.flat
Manaknight.app-main-109\:/drawable/bg_round_corners.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_round_corners.xml.flat
Manaknight.app-main-109\:/drawable/bg_round_corners_2.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_round_corners_2.xml.flat
Manaknight.app-main-109\:/drawable/bg_round_white.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_round_white.xml.flat
Manaknight.app-main-109\:/drawable/bg_stat_card.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_stat_card.xml.flat
Manaknight.app-main-109\:/drawable/bottom_sheet_background.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bottom_sheet_background.xml.flat
Manaknight.app-main-109\:/drawable/bottom_sheet_rounded_background.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bottom_sheet_rounded_background.xml.flat
Manaknight.app-main-109\:/drawable/chat_input_shape.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_chat_input_shape.xml.flat
Manaknight.app-main-109\:/drawable/check.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_check.png.flat
Manaknight.app-main-109\:/drawable/checkbox_checked.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_checkbox_checked.xml.flat
Manaknight.app-main-109\:/drawable/checkbox_color_selector.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_checkbox_color_selector.xml.flat
Manaknight.app-main-109\:/drawable/checkbox_unchecked.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_checkbox_unchecked.xml.flat
Manaknight.app-main-109\:/drawable/checked.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_checked.png.flat
Manaknight.app-main-109\:/drawable/chevron_bottom.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_chevron_bottom.png.flat
Manaknight.app-main-109\:/drawable/chevron_up.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_chevron_up.png.flat
Manaknight.app-main-109\:/drawable/compney_health.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_compney_health.png.flat
Manaknight.app-main-109\:/drawable/cross.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_cross.png.flat
Manaknight.app-main-109\:/drawable/custom_checkbox.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_custom_checkbox.xml.flat
Manaknight.app-main-109\:/drawable/custom_checkbox_selector.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_custom_checkbox_selector.xml.flat
Manaknight.app-main-109\:/drawable/delete.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_delete.png.flat
Manaknight.app-main-109\:/drawable/dialog_background.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dialog_background.xml.flat
Manaknight.app-main-109\:/drawable/edit.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_edit.png.flat
Manaknight.app-main-109\:/drawable/filter_button_background.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_filter_button_background.xml.flat
Manaknight.app-main-109\:/drawable/google.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_google.webp.flat
Manaknight.app-main-109\:/drawable/green_check.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_green_check.png.flat
Manaknight.app-main-109\:/drawable/green_check_mark.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_green_check_mark.xml.flat
Manaknight.app-main-109\:/drawable/ic_add.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add.png.flat
Manaknight.app-main-109\:/drawable/ic_arrow_right.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_arrow_right.png.flat
Manaknight.app-main-109\:/drawable/ic_arrow_up_right.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_arrow_up_right.png.flat
Manaknight.app-main-109\:/drawable/ic_baseline_add_24.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_baseline_add_24.xml.flat
Manaknight.app-main-109\:/drawable/ic_baseline_camera_alt_24.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_baseline_camera_alt_24.xml.flat
Manaknight.app-main-109\:/drawable/ic_baseline_close_24.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_baseline_close_24.xml.flat
Manaknight.app-main-109\:/drawable/ic_baseline_image_24.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_baseline_image_24.xml.flat
Manaknight.app-main-109\:/drawable/ic_baseline_play_circle_24.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_baseline_play_circle_24.xml.flat
Manaknight.app-main-109\:/drawable/ic_baseline_send_24.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_baseline_send_24.xml.flat
Manaknight.app-main-109\:/drawable/ic_baseline_video_library_24.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_baseline_video_library_24.xml.flat
Manaknight.app-main-109\:/drawable/ic_cart.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_cart.xml.flat
Manaknight.app-main-109\:/drawable/ic_close.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_close.xml.flat
Manaknight.app-main-109\:/drawable/ic_default.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_default.xml.flat
Manaknight.app-main-109\:/drawable/ic_delete_icon.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_delete_icon.png.flat
Manaknight.app-main-109\:/drawable/ic_dropdown.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_dropdown.png.flat
Manaknight.app-main-109\:/drawable/ic_edit_icon.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_edit_icon.png.flat
Manaknight.app-main-109\:/drawable/ic_info_custom_fill.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_info_custom_fill.png.flat
Manaknight.app-main-109\:/drawable/ic_launcher_background.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
Manaknight.app-main-109\:/drawable/ic_loc_active.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_loc_active.png.flat
Manaknight.app-main-109\:/drawable/ic_menu.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_menu.xml.flat
Manaknight.app-main-109\:/drawable/ic_option_light.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_option_light.png.flat
Manaknight.app-main-109\:/drawable/ic_options_bold.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_options_bold.png.flat
Manaknight.app-main-109\:/drawable/ic_profile.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_profile.xml.flat
Manaknight.app-main-109\:/drawable/ic_search.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_search.xml.flat
Manaknight.app-main-109\:/drawable/icon_left.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_icon_left.png.flat
Manaknight.app-main-109\:/drawable/image_shape.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_image_shape.xml.flat
Manaknight.app-main-109\:/drawable/info.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_info.png.flat
Manaknight.app-main-109\:/drawable/logout_icon.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_logout_icon.png.flat
Manaknight.app-main-109\:/drawable/new_checked.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_new_checked.png.flat
Manaknight.app-main-109\:/drawable/new_unchecked.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_new_unchecked.png.flat
Manaknight.app-main-109\:/drawable/plus_button.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_plus_button.png.flat
Manaknight.app-main-109\:/drawable/profit.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_profit.png.flat
Manaknight.app-main-109\:/drawable/profit_overhead.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_profit_overhead.png.flat
Manaknight.app-main-109\:/drawable/progress_bar.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_progress_bar.xml.flat
Manaknight.app-main-109\:/drawable/receive_message_shape.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_receive_message_shape.xml.flat
Manaknight.app-main-109\:/drawable/rounded_edittext.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_rounded_edittext.xml.flat
Manaknight.app-main-109\:/drawable/rounded_edittext2.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_rounded_edittext2.xml.flat
Manaknight.app-main-109\:/drawable/rounded_edittext2_none_editable.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_rounded_edittext2_none_editable.xml.flat
Manaknight.app-main-109\:/drawable/rounded_edittext_none_editable.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_rounded_edittext_none_editable.xml.flat
Manaknight.app-main-109\:/drawable/rounded_textview.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_rounded_textview.xml.flat
Manaknight.app-main-109\:/drawable/rounded_textview_bg.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_rounded_textview_bg.xml.flat
Manaknight.app-main-109\:/drawable/select_camera_shape.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_select_camera_shape.xml.flat
Manaknight.app-main-109\:/drawable/select_image_shape.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_select_image_shape.xml.flat
Manaknight.app-main-109\:/drawable/select_video_shape.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_select_video_shape.xml.flat
Manaknight.app-main-109\:/drawable/send_button_shape.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_send_button_shape.xml.flat
Manaknight.app-main-109\:/drawable/send_message_shape.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_send_message_shape.xml.flat
Manaknight.app-main-109\:/drawable/setting_icon.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_setting_icon.png.flat
Manaknight.app-main-109\:/drawable/subscription_icon.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_subscription_icon.png.flat
Manaknight.app-main-109\:/drawable/tab_account.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tab_account.xml.flat
Manaknight.app-main-109\:/drawable/tab_account_selected.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tab_account_selected.png.flat
Manaknight.app-main-109\:/drawable/tab_account_unselected.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tab_account_unselected.png.flat
Manaknight.app-main-109\:/drawable/tab_cost.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tab_cost.xml.flat
Manaknight.app-main-109\:/drawable/tab_cost_selected.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tab_cost_selected.png.flat
Manaknight.app-main-109\:/drawable/tab_cost_unselected.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tab_cost_unselected.png.flat
Manaknight.app-main-109\:/drawable/tab_home.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tab_home.xml.flat
Manaknight.app-main-109\:/drawable/tab_home_selected.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tab_home_selected.png.flat
Manaknight.app-main-109\:/drawable/tab_home_unselected.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tab_home_unselected.png.flat
Manaknight.app-main-109\:/drawable/tab_project.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tab_project.xml.flat
Manaknight.app-main-109\:/drawable/tab_project_selected.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tab_project_selected.png.flat
Manaknight.app-main-109\:/drawable/tab_project_unselected.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tab_project_unselected.png.flat
Manaknight.app-main-109\:/drawable/tab_team.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tab_team.xml.flat
Manaknight.app-main-109\:/drawable/tab_team_selected.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tab_team_selected.png.flat
Manaknight.app-main-109\:/drawable/tab_team_unselected.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tab_team_unselected.png.flat
Manaknight.app-main-109\:/drawable/unchecked.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_unchecked.png.flat
Manaknight.app-main-109\:/drawable/user_icon.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_user_icon.png.flat
Manaknight.app-main-109\:/font/inter.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_inter.xml.flat
Manaknight.app-main-109\:/font/inter_18pt_bold.ttf=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_inter_18pt_bold.ttf.flat
Manaknight.app-main-109\:/font/inter_18pt_medium.ttf=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_inter_18pt_medium.ttf.flat
Manaknight.app-main-109\:/font/inter_18pt_regular.ttf=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_inter_18pt_regular.ttf.flat
Manaknight.app-main-109\:/font/inter_18pt_semibold.ttf=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_inter_18pt_semibold.ttf.flat
Manaknight.app-main-109\:/font/medium.ttf=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_medium.ttf.flat
Manaknight.app-main-109\:/font/semibold.ttf=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_semibold.ttf.flat
Manaknight.app-main-109\:/font/sf_pro.ttf=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_sf_pro.ttf.flat
Manaknight.app-main-109\:/font/sf_pro_italic.ttf=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_sf_pro_italic.ttf.flat
Manaknight.app-main-109\:/font/sfpro.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\font_sfpro.xml.flat
Manaknight.app-main-109\:/menu/bottom_nav_menu.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_bottom_nav_menu.xml.flat
Manaknight.app-main-109\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
Manaknight.app-main-109\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
Manaknight.app-main-109\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
Manaknight.app-main-109\:/mipmap-hdpi/ic_launcher_foreground.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_foreground.webp.flat
Manaknight.app-main-109\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
Manaknight.app-main-109\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
Manaknight.app-main-109\:/mipmap-mdpi/ic_launcher_foreground.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_foreground.webp.flat
Manaknight.app-main-109\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
Manaknight.app-main-109\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
Manaknight.app-main-109\:/mipmap-xhdpi/ic_launcher_foreground.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_foreground.webp.flat
Manaknight.app-main-109\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
Manaknight.app-main-109\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
Manaknight.app-main-109\:/mipmap-xxhdpi/ic_launcher_foreground.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_foreground.webp.flat
Manaknight.app-main-109\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
Manaknight.app-main-109\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
Manaknight.app-main-109\:/mipmap-xxxhdpi/ic_launcher_foreground.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_foreground.webp.flat
Manaknight.app-main-109\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
Manaknight.app-main-109\:/navigation/mobile_navigation.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\navigation_mobile_navigation.xml.flat
Manaknight.app-main-109\:/raw/loader.json=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\raw_loader.json.flat
Manaknight.app-main-109\:/xml/backup_rules.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
Manaknight.app-main-109\:/xml/data_extraction_rules.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
Manaknight.app-main-109\:/xml/file_provider.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_file_provider.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/activity_main.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_activity_main.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/bottom_add_draw.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_bottom_add_draw.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/bottom_add_employee.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_bottom_add_employee.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/bottom_add_lineal.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_bottom_add_lineal.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/bottom_add_material.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_bottom_add_material.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/bottom_edit_profile.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_bottom_edit_profile.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/bottom_select_customer.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_bottom_select_customer.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/bottom_sheet_month_filter.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_bottom_sheet_month_filter.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/bottom_sheet_multi_select_status_filter.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_bottom_sheet_multi_select_status_filter.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/bottom_sheet_status_filter.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_bottom_sheet_status_filter.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/bottom_update_password.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_bottom_update_password.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/bottom_update_profile.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_bottom_update_profile.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/dialog_add_lineal.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_dialog_add_lineal.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/dialog_add_material.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_dialog_add_material.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/dialog_add_square.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_dialog_add_square.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/dialog_forgetpassword.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_dialog_forgetpassword.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/dialog_resetpassword.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_dialog_resetpassword.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/fragment_accountview.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_fragment_accountview.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/fragment_add_line_items.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_fragment_add_line_items.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/fragment_companysetup.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_fragment_companysetup.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/fragment_completesetup.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_fragment_completesetup.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/fragment_create_customer.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_fragment_create_customer.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/fragment_create_estimation.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_fragment_create_estimation.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/fragment_dashboardview.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_fragment_dashboardview.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/fragment_draws.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_fragment_draws.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/fragment_forget_password.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_fragment_forget_password.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/fragment_home.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_fragment_home.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/fragment_line_items.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_fragment_line_items.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/fragment_linealsetup.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_fragment_linealsetup.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/fragment_linear_line_item.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_fragment_linear_line_item.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/fragment_login.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_fragment_login.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/fragment_material_line_item.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_fragment_material_line_item.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/fragment_materialsetup.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_fragment_materialsetup.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/fragment_profileview.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_fragment_profileview.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/fragment_reset_password.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_fragment_reset_password.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/fragment_sign_up.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_fragment_sign_up.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/fragment_squresetup.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_fragment_squresetup.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/fragment_subscription.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_fragment_subscription.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/header.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_header.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/item_customer.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_item_customer.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/item_draw.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_item_draw.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/item_employee.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_item_employee.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/item_line.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_item_line.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/item_line_lineal.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_item_line_lineal.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/item_line_material.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_item_line_material.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/item_line_total.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_item_line_total.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/item_lineal.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_item_lineal.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout-sw600dp-v13/item_material.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-sw600dp_item_material.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/activity_main.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/bottom_add_draw.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_bottom_add_draw.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/bottom_add_employee.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_bottom_add_employee.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/bottom_add_lineal.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_bottom_add_lineal.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/bottom_add_material.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_bottom_add_material.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/bottom_edit_profile.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_bottom_edit_profile.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/bottom_select_customer.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_bottom_select_customer.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/bottom_sheet_month_filter.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_bottom_sheet_month_filter.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/bottom_sheet_multi_select_status_filter.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_bottom_sheet_multi_select_status_filter.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/bottom_sheet_status_filter.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_bottom_sheet_status_filter.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/bottom_update_password.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_bottom_update_password.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/bottom_update_profile.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_bottom_update_profile.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/dialog_add_lineal.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_add_lineal.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/dialog_add_material.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_add_material.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/dialog_add_square.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_add_square.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/dialog_alert_view.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_alert_view.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/dialog_forgetpassword.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_forgetpassword.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/dialog_resetpassword.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_resetpassword.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_accountview.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_accountview.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_add_line_items.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_add_line_items.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_alerts.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_alerts.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_companysetup.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_companysetup.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_completesetup.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_completesetup.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_costview.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_costview.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_create_customer.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_create_customer.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_create_estimation.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_create_estimation.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_dashboardview.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_dashboardview.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_draws.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_draws.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_forget_password.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_forget_password.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_friend_list.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_friend_list.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_home.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_home.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_labortrackingview.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_labortrackingview.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_line_items.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_line_items.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_linealsetup.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_linealsetup.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_linear_line_item.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_linear_line_item.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_login.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_login.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_material_line_item.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_material_line_item.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_materialsetup.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_materialsetup.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_profile.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_profile.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_profile_edit.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_profile_edit.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_profileview.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_profileview.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_projectview.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_projectview.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_reset_password.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_reset_password.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_room_list.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_room_list.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_sign_up.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_sign_up.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_splash.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_splash.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_squresetup.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_squresetup.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_subscription.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_subscription.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_trackingview.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_trackingview.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/fragment_workerview.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_workerview.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/header.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_header.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/item_app_alert.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_app_alert.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/item_broadcast_video.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_broadcast_video.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/item_customer.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_customer.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/item_dashboard_project.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_dashboard_project.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/item_draw.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_draw.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/item_employee.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_employee.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/item_filter_option.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_filter_option.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/item_line.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_line.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/item_line_lineal.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_line_lineal.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/item_line_material.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_line_material.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/item_line_total.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_line_total.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/item_lineal.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_lineal.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/item_material.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_material.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/item_multi_select_filter_option.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_multi_select_filter_option.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/progress_dialog.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_progress_dialog.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/receive_image_message_item.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_receive_image_message_item.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/receive_text_message_item.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_receive_text_message_item.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/receive_video_message_item.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_receive_video_message_item.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/room_data.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_room_data.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/send_image_message_item.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_send_image_message_item.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/send_text_message_item.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_send_text_message_item.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/send_video_message_item.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_send_video_message_item.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/simple_chat_view_widget.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_simple_chat_view_widget.xml.flat
Manaknight.app-mergeDebugResources-106\:/layout/user_data.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_user_data.xml.flat
