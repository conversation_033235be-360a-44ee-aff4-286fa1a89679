package com.manaknight.app.utils;

/**
 * Example usage of ResponsiveBottomSheetHelper and extension functions
 * This file shows how to migrate existing bottom sheet usage to responsive approach
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\f\u0010\u0003\u001a\u00020\u0004*\u00020\u0005H\u0007J\f\u0010\u0006\u001a\u00020\u0004*\u00020\u0005H\u0007J\f\u0010\u0007\u001a\u00020\u0004*\u00020\u0005H\u0007J\f\u0010\b\u001a\u00020\u0004*\u00020\u0005H\u0007J\f\u0010\t\u001a\u00020\u0004*\u00020\u0005H\u0007\u00a8\u0006\n"}, d2 = {"Lcom/manaknight/app/utils/ResponsiveBottomSheetUsageExample;", "", "()V", "showAddEmployeeSheet", "", "Landroidx/fragment/app/Fragment;", "showCustomSheet", "showEditProfileSheet", "showStatusFilterSheetLegacy", "showUpdatePasswordSheet", "app_debug"})
public final class ResponsiveBottomSheetUsageExample {
    
    public ResponsiveBottomSheetUsageExample() {
        super();
    }
    
    /**
     * Example 1: Using ViewBinding with extension function
     */
    @androidx.compose.runtime.Composable()
    public final void showAddEmployeeSheet(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$showAddEmployeeSheet) {
    }
    
    /**
     * Example 2: Edit Profile Sheet
     */
    @androidx.compose.runtime.Composable()
    public final void showEditProfileSheet(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$showEditProfileSheet) {
    }
    
    /**
     * Example 3: Update Password Sheet
     */
    @androidx.compose.runtime.Composable()
    public final void showUpdatePasswordSheet(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$showUpdatePasswordSheet) {
    }
    
    /**
     * Example 4: Using layout resource (backward compatibility)
     */
    @androidx.compose.runtime.Composable()
    public final void showStatusFilterSheetLegacy(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$showStatusFilterSheetLegacy) {
    }
    
    /**
     * Example 5: Direct usage of ResponsiveBottomSheetHelper
     */
    @androidx.compose.runtime.Composable()
    public final void showCustomSheet(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$showCustomSheet) {
    }
}