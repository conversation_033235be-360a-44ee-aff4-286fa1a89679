// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class SendImageMessageItemBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageView imgMessage;

  @NonNull
  public final TextView txtDate;

  @NonNull
  public final CardView txtMessage;

  private SendImageMessageItemBinding(@NonNull ConstraintLayout rootView,
      @NonNull ImageView imgMessage, @NonNull TextView txtDate, @NonNull CardView txtMessage) {
    this.rootView = rootView;
    this.imgMessage = imgMessage;
    this.txtDate = txtDate;
    this.txtMessage = txtMessage;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static SendImageMessageItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static SendImageMessageItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.send_image_message_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static SendImageMessageItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.imgMessage;
      ImageView imgMessage = ViewBindings.findChildViewById(rootView, id);
      if (imgMessage == null) {
        break missingId;
      }

      id = R.id.txtDate;
      TextView txtDate = ViewBindings.findChildViewById(rootView, id);
      if (txtDate == null) {
        break missingId;
      }

      id = R.id.txtMessage;
      CardView txtMessage = ViewBindings.findChildViewById(rootView, id);
      if (txtMessage == null) {
        break missingId;
      }

      return new SendImageMessageItemBinding((ConstraintLayout) rootView, imgMessage, txtDate,
          txtMessage);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
