// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentAccountviewBinding implements ViewBinding {
  @NonNull
  private final View rootView;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final RelativeLayout container;

  @NonNull
  public final HeaderBinding headerInclude;

  @NonNull
  public final LinearLayout line1;

  @NonNull
  public final LinearLayout line2;

  @NonNull
  public final LinearLayout line3;

  @NonNull
  public final LinearLayout line4;

  @NonNull
  public final TextView tvUserEmail;

  @NonNull
  public final TextView tvUserName;

  private FragmentAccountviewBinding(@NonNull View rootView, @Nullable RelativeLayout container,
      @NonNull HeaderBinding headerInclude, @NonNull LinearLayout line1,
      @NonNull LinearLayout line2, @NonNull LinearLayout line3, @NonNull LinearLayout line4,
      @NonNull TextView tvUserEmail, @NonNull TextView tvUserName) {
    this.rootView = rootView;
    this.container = container;
    this.headerInclude = headerInclude;
    this.line1 = line1;
    this.line2 = line2;
    this.line3 = line3;
    this.line4 = line4;
    this.tvUserEmail = tvUserEmail;
    this.tvUserName = tvUserName;
  }

  @Override
  @NonNull
  public View getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentAccountviewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentAccountviewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_accountview, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentAccountviewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.container;
      RelativeLayout container = ViewBindings.findChildViewById(rootView, id);

      id = R.id.headerInclude;
      View headerInclude = ViewBindings.findChildViewById(rootView, id);
      if (headerInclude == null) {
        break missingId;
      }
      HeaderBinding binding_headerInclude = HeaderBinding.bind(headerInclude);

      id = R.id.line1;
      LinearLayout line1 = ViewBindings.findChildViewById(rootView, id);
      if (line1 == null) {
        break missingId;
      }

      id = R.id.line2;
      LinearLayout line2 = ViewBindings.findChildViewById(rootView, id);
      if (line2 == null) {
        break missingId;
      }

      id = R.id.line3;
      LinearLayout line3 = ViewBindings.findChildViewById(rootView, id);
      if (line3 == null) {
        break missingId;
      }

      id = R.id.line4;
      LinearLayout line4 = ViewBindings.findChildViewById(rootView, id);
      if (line4 == null) {
        break missingId;
      }

      id = R.id.tvUserEmail;
      TextView tvUserEmail = ViewBindings.findChildViewById(rootView, id);
      if (tvUserEmail == null) {
        break missingId;
      }

      id = R.id.tvUserName;
      TextView tvUserName = ViewBindings.findChildViewById(rootView, id);
      if (tvUserName == null) {
        break missingId;
      }

      return new FragmentAccountviewBinding(rootView, container, binding_headerInclude, line1,
          line2, line3, line4, tvUserEmail, tvUserName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
