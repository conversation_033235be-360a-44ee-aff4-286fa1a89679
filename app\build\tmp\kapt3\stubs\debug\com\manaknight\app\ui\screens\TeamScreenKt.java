package com.manaknight.app.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000F\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0014\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001aj\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00032\b\u0010\u0005\u001a\u0004\u0018\u00010\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00010\b28\u0010\t\u001a4\u0012\u0013\u0012\u00110\u000b\u00a2\u0006\f\b\f\u0012\b\b\r\u0012\u0004\b\b(\r\u0012\u0015\u0012\u0013\u0018\u00010\u0003\u00a2\u0006\f\b\f\u0012\b\b\r\u0012\u0004\b\b(\u000e\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001aZ\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00032\b\u0010\u0005\u001a\u0004\u0018\u00010\u00062\u0006\u0010\u0010\u001a\u00020\u000b2\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00010\u00122\u0006\u0010\u000e\u001a\u00020\u000b2\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00010\u0012H\u0007\u001aj\u0010\u0014\u001a\u00020\u00012\b\u0010\u0005\u001a\u0004\u0018\u00010\u00062\u0006\u0010\u0010\u001a\u00020\u000b2\u0006\u0010\u000e\u001a\u00020\u000b2\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00010\b28\u0010\t\u001a4\u0012\u0013\u0012\u00110\u000b\u00a2\u0006\f\b\f\u0012\b\b\r\u0012\u0004\b\b(\r\u0012\u0015\u0012\u0013\u0018\u00010\u0003\u00a2\u0006\f\b\f\u0012\b\b\r\u0012\u0004\b\b(\u000e\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a&\u0010\u0015\u001a\u00020\u00012\u0006\u0010\u0016\u001a\u00020\u000b2\u0006\u0010\u0017\u001a\u00020\u000b2\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00010\bH\u0007\u001a@\u0010\u0019\u001a\u00020\u00012\u0006\u0010\u000e\u001a\u00020\u000b2\u0006\u0010\u001a\u001a\u00020\u000b2\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\u0018\u0010\t\u001a\u0014\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a@\u0010\u001b\u001a\u00020\u00012\u0006\u0010\u001c\u001a\u00020\u000b2\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00010\u00122\u0006\u0010\u001d\u001a\u00020\u000b2\u0012\u0010\u001e\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00010\u0012H\u0007\u001a@\u0010\u001f\u001a\u00020\u00012\u0006\u0010\u001c\u001a\u00020\u000b2\u0006\u0010\u001d\u001a\u00020\u000b2\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\u0018\u0010\t\u001a\u0014\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a8\u0010 \u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u00062\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u00122\u0012\u0010!\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u0012H\u0007\u001a\b\u0010\"\u001a\u00020\u0001H\u0007\u001a\b\u0010#\u001a\u00020\u0001H\u0007\u001a\b\u0010$\u001a\u00020\u0001H\u0007\u001a2\u0010%\u001a\u00020\u00012\u0006\u0010&\u001a\u00020\'2\u0006\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u0006\u0010(\u001a\u00020)2\u0006\u0010*\u001a\u00020+H\u0007\u00a8\u0006,"}, d2 = {"AddEditEmployeeSheet", "", "projectId", "", "userId", "employeeData", "Lcom/manaknight/app/model/remote/TeamMember;", "onClose", "Lkotlin/Function0;", "onSave", "Lkotlin/Function2;", "", "Lkotlin/ParameterName;", "name", "hourlyRate", "AddEditEmployeeSheetContent", "fullName", "onFullNameChange", "Lkotlin/Function1;", "onHourlyRateChange", "AddEditEmployeeSheetHeader", "DefaultsItemRow", "defaultHourlyRate", "defaultProfitOverhead", "onEditClick", "EditDefaultsSheet", "profitOverhead", "EditDefaultsSheetContent", "editedHourlyRate", "editedProfitOverhead", "onProfitOverheadChange", "EditDefaultsSheetHeader", "EmployeeItemRow", "onDeleteClick", "PreviewAddEditEmployeeSheet", "PreviewEditDefaultsSheet", "PreviewTeamScreen", "TeamScreen", "navController", "Landroidx/navigation/NavController;", "baasViewModel", "Lcom/manaknight/app/viewmodels/BaasViewModel;", "dialog", "Landroid/app/Dialog;", "app_debug"})
public final class TeamScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void TeamScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, int userId, int projectId, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.viewmodels.BaasViewModel baasViewModel, @org.jetbrains.annotations.NotNull()
    android.app.Dialog dialog) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void DefaultsItemRow(@org.jetbrains.annotations.NotNull()
    java.lang.String defaultHourlyRate, @org.jetbrains.annotations.NotNull()
    java.lang.String defaultProfitOverhead, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onEditClick) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void EmployeeItemRow(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.TeamMember employeeData, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.TeamMember, kotlin.Unit> onEditClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.manaknight.app.model.remote.TeamMember, kotlin.Unit> onDeleteClick) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void AddEditEmployeeSheet(int projectId, int userId, @org.jetbrains.annotations.Nullable()
    com.manaknight.app.model.remote.TeamMember employeeData, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClose, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.Integer, kotlin.Unit> onSave) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AddEditEmployeeSheetHeader(@org.jetbrains.annotations.Nullable()
    com.manaknight.app.model.remote.TeamMember employeeData, @org.jetbrains.annotations.NotNull()
    java.lang.String fullName, @org.jetbrains.annotations.NotNull()
    java.lang.String hourlyRate, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClose, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.Integer, kotlin.Unit> onSave) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AddEditEmployeeSheetContent(int projectId, int userId, @org.jetbrains.annotations.Nullable()
    com.manaknight.app.model.remote.TeamMember employeeData, @org.jetbrains.annotations.NotNull()
    java.lang.String fullName, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onFullNameChange, @org.jetbrains.annotations.NotNull()
    java.lang.String hourlyRate, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onHourlyRateChange) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void EditDefaultsSheet(@org.jetbrains.annotations.NotNull()
    java.lang.String hourlyRate, @org.jetbrains.annotations.NotNull()
    java.lang.String profitOverhead, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClose, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, kotlin.Unit> onSave) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void EditDefaultsSheetHeader(@org.jetbrains.annotations.NotNull()
    java.lang.String editedHourlyRate, @org.jetbrains.annotations.NotNull()
    java.lang.String editedProfitOverhead, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClose, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, kotlin.Unit> onSave) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void EditDefaultsSheetContent(@org.jetbrains.annotations.NotNull()
    java.lang.String editedHourlyRate, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onHourlyRateChange, @org.jetbrains.annotations.NotNull()
    java.lang.String editedProfitOverhead, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onProfitOverheadChange) {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(showBackground = true)
    @androidx.compose.runtime.Composable()
    public static final void PreviewTeamScreen() {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(showBackground = true)
    @androidx.compose.runtime.Composable()
    public static final void PreviewAddEditEmployeeSheet() {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(showBackground = true)
    @androidx.compose.runtime.Composable()
    public static final void PreviewEditDefaultsSheet() {
    }
}