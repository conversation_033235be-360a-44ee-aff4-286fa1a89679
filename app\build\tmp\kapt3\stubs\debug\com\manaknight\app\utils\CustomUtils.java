package com.manaknight.app.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\b\u0010\u0007\u001a\u0004\u0018\u00010\bJ\u000e\u0010\t\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006J\u0010\u0010\n\u001a\u00020\u000b2\b\u0010\f\u001a\u0004\u0018\u00010\u000bJ\u0010\u0010\r\u001a\u0004\u0018\u00010\b2\u0006\u0010\u0005\u001a\u00020\u0006J\u000e\u0010\u000e\u001a\u00020\u000b2\u0006\u0010\u000f\u001a\u00020\u0010J\u000e\u0010\u0011\u001a\u00020\u000b2\u0006\u0010\u0012\u001a\u00020\u000b\u00a8\u0006\u0013"}, d2 = {"Lcom/manaknight/app/utils/CustomUtils;", "", "()V", "cacheProjectDetailsToLocal", "", "context", "Landroid/content/Context;", "data", "Lcom/manaknight/app/model/remote/profitPro/AllLineItemsResponseModel;", "clearCachedProjectDetails", "formatDate", "", "dateString", "getCachedProjectDetailsFromLocal", "getOrdinal", "position", "", "someOtherUtilityFunction", "input", "app_debug"})
public final class CustomUtils {
    @org.jetbrains.annotations.NotNull()
    public static final com.manaknight.app.utils.CustomUtils INSTANCE = null;
    
    private CustomUtils() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String formatDate(@org.jetbrains.annotations.Nullable()
    java.lang.String dateString) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String someOtherUtilityFunction(@org.jetbrains.annotations.NotNull()
    java.lang.String input) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getOrdinal(int position) {
        return null;
    }
    
    public final void cacheProjectDetailsToLocal(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel data) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel getCachedProjectDetailsFromLocal(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    public final void clearCachedProjectDetails(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
}