package com.manaknight.app.ui.fragments

import Manaknight.R
import android.os.Bundle
import androidx.navigation.NavDirections
import kotlin.Int

public class ProjectTrackingFragmentDirections private constructor() {
  private data class ActionProjectTrackingFragmentToInvoiceFragment(
    public val projectID: Int = 0
  ) : NavDirections {
    public override val actionId: Int = R.id.action_ProjectTrackingFragment_to_InvoiceFragment

    public override val arguments: Bundle
      get() {
        val result = Bundle()
        result.putInt("projectID", this.projectID)
        return result
      }
  }

  public companion object {
    public fun actionProjectTrackingFragmentToInvoiceFragment(projectID: Int = 0): NavDirections =
        ActionProjectTrackingFragmentToInvoiceFragment(projectID)
  }
}
