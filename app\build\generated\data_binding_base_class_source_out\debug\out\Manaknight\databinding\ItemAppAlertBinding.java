// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.imageview.ShapeableImageView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemAppAlertBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ShapeableImageView imageView4;

  @NonNull
  public final TextView textViewAlertMsg;

  @NonNull
  public final TextView tvTimeAgo;

  private ItemAppAlertBinding(@NonNull ConstraintLayout rootView,
      @NonNull ShapeableImageView imageView4, @NonNull TextView textViewAlertMsg,
      @NonNull TextView tvTimeAgo) {
    this.rootView = rootView;
    this.imageView4 = imageView4;
    this.textViewAlertMsg = textViewAlertMsg;
    this.tvTimeAgo = tvTimeAgo;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemAppAlertBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemAppAlertBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_app_alert, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemAppAlertBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.imageView4;
      ShapeableImageView imageView4 = ViewBindings.findChildViewById(rootView, id);
      if (imageView4 == null) {
        break missingId;
      }

      id = R.id.textViewAlertMsg;
      TextView textViewAlertMsg = ViewBindings.findChildViewById(rootView, id);
      if (textViewAlertMsg == null) {
        break missingId;
      }

      id = R.id.tvTimeAgo;
      TextView tvTimeAgo = ViewBindings.findChildViewById(rootView, id);
      if (tvTimeAgo == null) {
        break missingId;
      }

      return new ItemAppAlertBinding((ConstraintLayout) rootView, imageView4, textViewAlertMsg,
          tvTimeAgo);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
