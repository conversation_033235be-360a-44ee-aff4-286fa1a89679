package com.manaknight.app.utils;

/**
 * Helper class for creating responsive bottom sheets that show as dialogs on tablets
 * and bottom sheets on phones
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\u0018\u0000 \u00032\u00020\u0001:\u0001\u0003B\u0005\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0004"}, d2 = {"Lcom/manaknight/app/utils/ResponsiveBottomSheetHelper;", "", "()V", "Companion", "app_debug"})
public final class ResponsiveBottomSheetHelper {
    @org.jetbrains.annotations.NotNull()
    public static final com.manaknight.app.utils.ResponsiveBottomSheetHelper.Companion Companion = null;
    
    public ResponsiveBottomSheetHelper() {
        super();
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002Jf\u0010\u0003\u001a\u00020\u0004\"\b\b\u0000\u0010\u0005*\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2 \u0010\t\u001a\u001c\u0012\u0004\u0012\u00020\u000b\u0012\u0006\u0012\u0004\u0018\u00010\f\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u0002H\u00050\n2\u0018\u0010\u000e\u001a\u0014\u0012\u0004\u0012\u0002H\u0005\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00100\u000f2\u000e\u0010\u0011\u001a\n\u0012\u0004\u0012\u00020\u0010\u0018\u00010\u0012H\u0002Jf\u0010\u0013\u001a\u00020\u0004\"\b\b\u0000\u0010\u0005*\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2 \u0010\t\u001a\u001c\u0012\u0004\u0012\u00020\u000b\u0012\u0006\u0012\u0004\u0018\u00010\f\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u0002H\u00050\n2\u0018\u0010\u000e\u001a\u0014\u0012\u0004\u0012\u0002H\u0005\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00100\u000f2\u000e\u0010\u0011\u001a\n\u0012\u0004\u0012\u00020\u0010\u0018\u00010\u0012H\u0002JB\u0010\u0014\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\u0015\u001a\u00020\u00162\u0018\u0010\u0017\u001a\u0014\u0012\u0004\u0012\u00020\u0018\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00100\u000f2\u000e\u0010\u0011\u001a\n\u0012\u0004\u0012\u00020\u0010\u0018\u00010\u0012H\u0002JB\u0010\u0019\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\u0015\u001a\u00020\u00162\u0018\u0010\u0017\u001a\u0014\u0012\u0004\u0012\u00020\u0018\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00100\u000f2\u000e\u0010\u0011\u001a\n\u0012\u0004\u0012\u00020\u0010\u0018\u00010\u0012H\u0002Jh\u0010\u001a\u001a\u00020\u0004\"\b\b\u0000\u0010\u0005*\u00020\u00062\u0006\u0010\u001b\u001a\u00020\u001c2 \u0010\t\u001a\u001c\u0012\u0004\u0012\u00020\u000b\u0012\u0006\u0012\u0004\u0018\u00010\f\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u0002H\u00050\n2\u0018\u0010\u000e\u001a\u0014\u0012\u0004\u0012\u0002H\u0005\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00100\u000f2\u0010\b\u0002\u0010\u0011\u001a\n\u0012\u0004\u0012\u00020\u0010\u0018\u00010\u0012H\u0007JD\u0010\u001d\u001a\u00020\u00042\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u0015\u001a\u00020\u00162\u0018\u0010\u0017\u001a\u0014\u0012\u0004\u0012\u00020\u0018\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00100\u000f2\u0010\b\u0002\u0010\u0011\u001a\n\u0012\u0004\u0012\u00020\u0010\u0018\u00010\u0012H\u0007\u00a8\u0006\u001e"}, d2 = {"Lcom/manaknight/app/utils/ResponsiveBottomSheetHelper$Companion;", "", "()V", "showAsBottomSheet", "Landroid/app/Dialog;", "T", "Landroidx/viewbinding/ViewBinding;", "context", "Landroid/content/Context;", "bindingInflater", "Lkotlin/Function3;", "Landroid/view/LayoutInflater;", "Landroid/view/ViewGroup;", "", "onBindingReady", "Lkotlin/Function2;", "", "onDismiss", "Lkotlin/Function0;", "showAsDialog", "showLayoutAsBottomSheet", "layoutRes", "", "onViewReady", "Landroid/view/View;", "showLayoutAsDialog", "showResponsiveSheet", "fragment", "Landroidx/fragment/app/Fragment;", "showResponsiveSheetWithLayout", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * Shows a responsive sheet - bottom sheet on phone, dialog on tablet
         */
        @androidx.compose.runtime.Composable()
        @org.jetbrains.annotations.NotNull()
        public final <T extends androidx.viewbinding.ViewBinding>android.app.Dialog showResponsiveSheet(@org.jetbrains.annotations.NotNull()
        androidx.fragment.app.Fragment fragment, @org.jetbrains.annotations.NotNull()
        kotlin.jvm.functions.Function3<? super android.view.LayoutInflater, ? super android.view.ViewGroup, ? super java.lang.Boolean, ? extends T> bindingInflater, @org.jetbrains.annotations.NotNull()
        kotlin.jvm.functions.Function2<? super T, ? super android.app.Dialog, kotlin.Unit> onBindingReady, @org.jetbrains.annotations.Nullable()
        kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
            return null;
        }
        
        /**
         * Shows content as a custom dialog for tablets
         */
        private final <T extends androidx.viewbinding.ViewBinding>android.app.Dialog showAsDialog(android.content.Context context, kotlin.jvm.functions.Function3<? super android.view.LayoutInflater, ? super android.view.ViewGroup, ? super java.lang.Boolean, ? extends T> bindingInflater, kotlin.jvm.functions.Function2<? super T, ? super android.app.Dialog, kotlin.Unit> onBindingReady, kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
            return null;
        }
        
        /**
         * Shows content as bottom sheet for phones
         */
        private final <T extends androidx.viewbinding.ViewBinding>android.app.Dialog showAsBottomSheet(android.content.Context context, kotlin.jvm.functions.Function3<? super android.view.LayoutInflater, ? super android.view.ViewGroup, ? super java.lang.Boolean, ? extends T> bindingInflater, kotlin.jvm.functions.Function2<? super T, ? super android.app.Dialog, kotlin.Unit> onBindingReady, kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
            return null;
        }
        
        /**
         * Shows a responsive sheet using layout resource (for backward compatibility)
         */
        @androidx.compose.runtime.Composable()
        @org.jetbrains.annotations.NotNull()
        public final android.app.Dialog showResponsiveSheetWithLayout(@org.jetbrains.annotations.NotNull()
        androidx.fragment.app.Fragment fragment, int layoutRes, @org.jetbrains.annotations.NotNull()
        kotlin.jvm.functions.Function2<? super android.view.View, ? super android.app.Dialog, kotlin.Unit> onViewReady, @org.jetbrains.annotations.Nullable()
        kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
            return null;
        }
        
        private final android.app.Dialog showLayoutAsDialog(android.content.Context context, int layoutRes, kotlin.jvm.functions.Function2<? super android.view.View, ? super android.app.Dialog, kotlin.Unit> onViewReady, kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
            return null;
        }
        
        private final android.app.Dialog showLayoutAsBottomSheet(android.content.Context context, int layoutRes, kotlin.jvm.functions.Function2<? super android.view.View, ? super android.app.Dialog, kotlin.Unit> onViewReady, kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
            return null;
        }
    }
}