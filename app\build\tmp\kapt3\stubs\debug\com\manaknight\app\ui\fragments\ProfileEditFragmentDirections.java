package com.manaknight.app.ui.fragments;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\u0018\u0000 \u00032\u00020\u0001:\u0001\u0003B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0004"}, d2 = {"Lcom/manaknight/app/ui/fragments/ProfileEditFragmentDirections;", "", "()V", "Companion", "app_debug"})
public final class ProfileEditFragmentDirections {
    @org.jetbrains.annotations.NotNull()
    public static final com.manaknight.app.ui.fragments.ProfileEditFragmentDirections.Companion Companion = null;
    
    private ProfileEditFragmentDirections() {
        super();
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0003\u001a\u00020\u0004J\u0006\u0010\u0005\u001a\u00020\u0004\u00a8\u0006\u0006"}, d2 = {"Lcom/manaknight/app/ui/fragments/ProfileEditFragmentDirections$Companion;", "", "()V", "actionProfileEditFragmentToHomeFragment", "Landroidx/navigation/NavDirections;", "actionProfileEditFragmentToLoginFragment", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.navigation.NavDirections actionProfileEditFragmentToHomeFragment() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.navigation.NavDirections actionProfileEditFragmentToLoginFragment() {
            return null;
        }
    }
}