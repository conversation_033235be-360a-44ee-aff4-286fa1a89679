// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentResetPasswordBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnResetPassword;

  @NonNull
  public final ConstraintLayout constraint;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final RelativeLayout container;

  @NonNull
  public final EditText edTxtCode;

  @NonNull
  public final EditText edTxtPassword;

  @NonNull
  public final EditText edTxtPasswordConfirm;

  @NonNull
  public final HeaderBinding headerInclude;

  /**
   * This binding is not available in all configurations.
   * <p>
   * Present:
   * <ul>
   *   <li>layout-sw600dp/</li>
   * </ul>
   *
   * Absent:
   * <ul>
   *   <li>layout/</li>
   * </ul>
   */
  @Nullable
  public final ConstraintLayout innerConstraintLayout;

  @NonNull
  public final TextView tvCode;

  @NonNull
  public final TextView tvPassword;

  @NonNull
  public final TextView tvPasswordConfirm;

  private FragmentResetPasswordBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnResetPassword, @NonNull ConstraintLayout constraint,
      @Nullable RelativeLayout container, @NonNull EditText edTxtCode,
      @NonNull EditText edTxtPassword, @NonNull EditText edTxtPasswordConfirm,
      @NonNull HeaderBinding headerInclude, @Nullable ConstraintLayout innerConstraintLayout,
      @NonNull TextView tvCode, @NonNull TextView tvPassword, @NonNull TextView tvPasswordConfirm) {
    this.rootView = rootView;
    this.btnResetPassword = btnResetPassword;
    this.constraint = constraint;
    this.container = container;
    this.edTxtCode = edTxtCode;
    this.edTxtPassword = edTxtPassword;
    this.edTxtPasswordConfirm = edTxtPasswordConfirm;
    this.headerInclude = headerInclude;
    this.innerConstraintLayout = innerConstraintLayout;
    this.tvCode = tvCode;
    this.tvPassword = tvPassword;
    this.tvPasswordConfirm = tvPasswordConfirm;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentResetPasswordBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentResetPasswordBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_reset_password, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentResetPasswordBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnResetPassword;
      MaterialButton btnResetPassword = ViewBindings.findChildViewById(rootView, id);
      if (btnResetPassword == null) {
        break missingId;
      }

      ConstraintLayout constraint = (ConstraintLayout) rootView;

      id = R.id.container;
      RelativeLayout container = ViewBindings.findChildViewById(rootView, id);

      id = R.id.edTxtCode;
      EditText edTxtCode = ViewBindings.findChildViewById(rootView, id);
      if (edTxtCode == null) {
        break missingId;
      }

      id = R.id.edTxtPassword;
      EditText edTxtPassword = ViewBindings.findChildViewById(rootView, id);
      if (edTxtPassword == null) {
        break missingId;
      }

      id = R.id.edTxtPasswordConfirm;
      EditText edTxtPasswordConfirm = ViewBindings.findChildViewById(rootView, id);
      if (edTxtPasswordConfirm == null) {
        break missingId;
      }

      id = R.id.headerInclude;
      View headerInclude = ViewBindings.findChildViewById(rootView, id);
      if (headerInclude == null) {
        break missingId;
      }
      HeaderBinding binding_headerInclude = HeaderBinding.bind(headerInclude);

      id = R.id.innerConstraintLayout;
      ConstraintLayout innerConstraintLayout = ViewBindings.findChildViewById(rootView, id);

      id = R.id.tvCode;
      TextView tvCode = ViewBindings.findChildViewById(rootView, id);
      if (tvCode == null) {
        break missingId;
      }

      id = R.id.tvPassword;
      TextView tvPassword = ViewBindings.findChildViewById(rootView, id);
      if (tvPassword == null) {
        break missingId;
      }

      id = R.id.tvPasswordConfirm;
      TextView tvPasswordConfirm = ViewBindings.findChildViewById(rootView, id);
      if (tvPasswordConfirm == null) {
        break missingId;
      }

      return new FragmentResetPasswordBinding((ConstraintLayout) rootView, btnResetPassword,
          constraint, container, edTxtCode, edTxtPassword, edTxtPasswordConfirm,
          binding_headerInclude, innerConstraintLayout, tvCode, tvPassword, tvPasswordConfirm);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
