package com.manaknight.app.di;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0012\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\b\r\"\u0011\u0010\u0000\u001a\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u0003\"\u0017\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007\"\u0011\u0010\b\u001a\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\u0003\"\u0011\u0010\n\u001a\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\u0003\"\u0011\u0010\f\u001a\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u0003\"\u0011\u0010\u000e\u001a\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0003\"\u0011\u0010\u0010\u001a\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0003\u00a8\u0006\u0012"}, d2 = {"dbModule", "Lorg/koin/core/module/Module;", "getDbModule", "()Lorg/koin/core/module/Module;", "koinModules", "", "getKoinModules", "()Ljava/util/List;", "networkModule", "getNetworkModule", "prefModule", "getPrefModule", "remoteDataSourceModule", "getRemoteDataSourceModule", "repoModules", "getRepoModules", "viewModules", "getViewModules", "app_debug"})
public final class ModulesKt {
    @org.jetbrains.annotations.NotNull()
    private static final org.koin.core.module.Module dbModule = null;
    @org.jetbrains.annotations.NotNull()
    private static final org.koin.core.module.Module prefModule = null;
    @org.jetbrains.annotations.NotNull()
    private static final org.koin.core.module.Module networkModule = null;
    @org.jetbrains.annotations.NotNull()
    private static final org.koin.core.module.Module remoteDataSourceModule = null;
    @org.jetbrains.annotations.NotNull()
    private static final org.koin.core.module.Module repoModules = null;
    @org.jetbrains.annotations.NotNull()
    private static final org.koin.core.module.Module viewModules = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<org.koin.core.module.Module> koinModules = null;
    
    @org.jetbrains.annotations.NotNull()
    public static final org.koin.core.module.Module getDbModule() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final org.koin.core.module.Module getPrefModule() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final org.koin.core.module.Module getNetworkModule() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final org.koin.core.module.Module getRemoteDataSourceModule() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final org.koin.core.module.Module getRepoModules() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final org.koin.core.module.Module getViewModules() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<org.koin.core.module.Module> getKoinModules() {
        return null;
    }
}